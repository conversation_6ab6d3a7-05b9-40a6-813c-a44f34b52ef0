FROM eclipse-temurin:17-jre-jammy

WORKDIR /app

# Fix lỗi Snappy cho Kafka
RUN apt-get update && \
    apt-get install -y libsnappy1v5 && \
    rm -rf /var/lib/apt/lists/*

COPY target/market.jar market.jar
COPY libs/logback-spring.xml /app/libs/
COPY app-config/logback-spring.xml /app/app-config/
COPY libs/opentelemetry-javaagent.jar /app/libs/

EXPOSE 6004 5005

ENTRYPOINT ["java", "-jar", "market.jar"]

package com.icetea.lotus.service;

import com.icetea.lotus.entity.KLine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class to verify MarketService collection name consistency
 * This test ensures that findPreviousKline uses the same collection naming convention
 * as other methods, which fixes the TradingView gap issue.
 */
@SuppressWarnings("all")
public class MarketServiceCollectionNameTest {

    @Mock
    private MongoTemplate mongoTemplate;

    private MarketService marketService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        marketService = new MarketService(mongoTemplate);
    }

    @Test
    void testFindPreviousKline_UsesCorrectCollectionName() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "1min";
        long timestamp = 1752576600000L;

        KLine mockKLine = new KLine();
        mockKLine.setTime(1752576540000L);
        mockKLine.setClosePrice(new BigDecimal("2970.15"));

        when(mongoTemplate.findOne(any(Query.class), eq(KLine.class), anyString()))
            .thenReturn(mockKLine);

        // Act
        KLine result = marketService.findPreviousKline(symbol, period, timestamp);

        // Assert
        // Verify that the correct collection name is used (without .toLowerCase())
        verify(mongoTemplate).findOne(
            any(Query.class), 
            eq(KLine.class), 
            eq("exchange_kline_ETH/USDT_1min") // Should NOT be "exchange_kline_eth/usdt_1min"
        );

        System.out.println("[DEBUG_LOG] Collection name consistency test passed:");
        System.out.println("[DEBUG_LOG] Expected collection: exchange_kline_ETH/USDT_1min");
        System.out.println("[DEBUG_LOG] Method called with correct collection name (no toLowerCase)");
    }

    @Test
    void testFindAllKLine_UsesConsistentCollectionName() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "1min";
        long from = 1752576540000L;
        long to = 1752576600000L;

        // Act
        marketService.findAllKLine(symbol, from, to, period);

        // Assert
        // Verify that findAllKLine uses the same collection naming convention
        verify(mongoTemplate).find(
            any(Query.class), 
            eq(KLine.class), 
            eq("exchange_kline_ETH/USDT_1min") // Same format as findPreviousKline
        );

        System.out.println("[DEBUG_LOG] Collection name consistency verified:");
        System.out.println("[DEBUG_LOG] Both findAllKLine and findPreviousKline use same collection naming");
    }

    @Test
    void testCollectionNameConsistency_AcrossAllMethods() {
        // This test demonstrates that the fix ensures all methods use consistent collection names
        String symbol = "BTC/USDT";
        String period = "5min";

        // Test findPreviousKline
        marketService.findPreviousKline(symbol, period, System.currentTimeMillis());

        // Test findAllKLine
        marketService.findAllKLine(symbol, 0L, System.currentTimeMillis(), period);

        // Test findKlineLatest
        marketService.findKlineLatest(symbol, period);

        // Verify findOne methods use the same collection name format
        verify(mongoTemplate, times(2)).findOne(
            any(Query.class), 
            eq(KLine.class), 
            eq("exchange_kline_BTC/USDT_5min")
        );

        // Verify find method uses the same collection name format
        verify(mongoTemplate, times(1)).find(
            any(Query.class), 
            eq(KLine.class), 
            eq("exchange_kline_BTC/USDT_5min")
        );

        System.out.println("[DEBUG_LOG] All MarketService methods use consistent collection naming:");
        System.out.println("[DEBUG_LOG] Collection format: exchange_kline_{symbol}_{period}");
        System.out.println("[DEBUG_LOG] No case conversion applied - maintains original case");
        System.out.println("[DEBUG_LOG] findPreviousKline and findKlineLatest use findOne()");
        System.out.println("[DEBUG_LOG] findAllKLine uses find()");
    }
}

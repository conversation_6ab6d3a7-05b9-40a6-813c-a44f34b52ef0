package com.icetea.lotus.job;

import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.processor.KlineProcessorAdapter;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.HistoricalKlineProcessor;
import com.icetea.lotus.processor.HistoricalKlineProcessorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.task.TaskExecutor;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class to verify the handleHourKLine method functionality
 */
@SuppressWarnings("all")
public class KLineGeneratorJobHourTest {

    @Mock
    private CoinProcessorFactory processorFactory;

    @Mock
    private HistoricalKlineProcessorFactory historicalKlineProcessorFactory;

    @Mock
    private TaskExecutor taskExecutor;

    @Mock
    private CoinProcessor coinProcessor;

    @Mock
    private KlineProcessorAdapter klineAdapter;

    @Mock
    private HistoricalKlineProcessor historicalKlineProcessor;

    private KLineGeneratorJob kLineGeneratorJob;

    private static final String SYMBOL = "BTC/USDT";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        // Create the job instance
        kLineGeneratorJob = new KLineGeneratorJob(processorFactory, historicalKlineProcessorFactory, taskExecutor);

        // Setup mock processor map
        Map<String, CoinProcessor> processorMap = new HashMap<>();
        processorMap.put(SYMBOL, coinProcessor);
        when(processorFactory.getProcessorMap()).thenReturn(processorMap);

        // Setup mock kline adapter
        when(coinProcessor.getKlineAdapter()).thenReturn(klineAdapter);
        when(klineAdapter.isStopKline()).thenReturn(false);

        // Setup mock historical processor
        when(historicalKlineProcessorFactory.getProcessor(SYMBOL)).thenReturn(historicalKlineProcessor);

        // Mock taskExecutor to execute tasks immediately for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));
    }

    @Test
    public void testHandleHourKLineTimeSemantics() {
        // Record the current time before calling the method
        long beforeCall = System.currentTimeMillis();

        // Call the method
        kLineGeneratorJob.handleHourKLine();

        // Capture the arguments passed to HistoricalKlineProcessor.generateKLine
        ArgumentCaptor<Integer> rangeCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Integer> fieldCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Long> timeCaptor = ArgumentCaptor.forClass(Long.class);

        verify(historicalKlineProcessor, times(1)).generateKLine(
            rangeCaptor.capture(), 
            fieldCaptor.capture(), 
            timeCaptor.capture()
        );

        // Verify the parameters
        assertEquals(1, rangeCaptor.getValue(), "Range should be 1 for 1-hour K-line");
        assertEquals(Calendar.HOUR_OF_DAY, fieldCaptor.getValue(), "Field should be Calendar.HOUR_OF_DAY");

        // Verify time semantics - the time should represent the START of the previous hour
        long capturedTime = timeCaptor.getValue();

        // Create expected time: previous hour with minute, second, millisecond set to 0
        Calendar expectedCalendar = Calendar.getInstance();
        expectedCalendar.add(Calendar.HOUR_OF_DAY, -1);
        expectedCalendar.set(Calendar.MINUTE, 0);
        expectedCalendar.set(Calendar.SECOND, 0);
        expectedCalendar.set(Calendar.MILLISECOND, 0);
        long expectedTime = expectedCalendar.getTimeInMillis();

        // The captured time should be close to the expected time (within a few seconds)
        long timeDifference = Math.abs(capturedTime - expectedTime);
        assertTrue(timeDifference < 5000, 
            String.format("Time difference should be less than 5 seconds. Expected: %d, Actual: %d, Difference: %d", 
                expectedTime, capturedTime, timeDifference));

        // Verify that the time represents the START of the hour (minute, second, millisecond should be 0)
        Calendar capturedCalendar = Calendar.getInstance();
        capturedCalendar.setTimeInMillis(capturedTime);

        assertEquals(0, capturedCalendar.get(Calendar.MINUTE), 
            "Minute should be 0 for hour boundary");
        assertEquals(0, capturedCalendar.get(Calendar.SECOND), 
            "Second should be 0 for hour boundary");
        assertEquals(0, capturedCalendar.get(Calendar.MILLISECOND), 
            "Millisecond should be 0 for hour boundary");

        System.out.println("[DEBUG_LOG] handleHourKLine called with:");
        System.out.println("[DEBUG_LOG] Range: " + rangeCaptor.getValue());
        System.out.println("[DEBUG_LOG] Field: " + fieldCaptor.getValue() + " (Calendar.HOUR_OF_DAY = " + Calendar.HOUR_OF_DAY + ")");
        System.out.println("[DEBUG_LOG] Time: " + capturedTime + " (" + capturedCalendar.getTime() + ")");
        System.out.println("[DEBUG_LOG] Expected time: " + expectedTime + " (" + expectedCalendar.getTime() + ")");
    }

    @Test
    public void testHandleHourKLineSkipsWhenStopped() {
        // Setup: kline generation is stopped
        when(klineAdapter.isStopKline()).thenReturn(true);

        // Call the method
        kLineGeneratorJob.handleHourKLine();

        // Verify that generateKLine is not called when stopped
        verify(historicalKlineProcessor, never()).generateKLine(anyInt(), anyInt(), anyLong());

        System.out.println("[DEBUG_LOG] handleHourKLine correctly skipped when kline generation is stopped");
    }

    @Test
    public void testHandleHourKLineProcessesAllSymbols() {
        // Setup multiple symbols
        Map<String, CoinProcessor> processorMap = new HashMap<>();

        CoinProcessor processor1 = mock(CoinProcessor.class);
        KlineProcessorAdapter adapter1 = mock(KlineProcessorAdapter.class);
        when(processor1.getKlineAdapter()).thenReturn(adapter1);
        when(adapter1.isStopKline()).thenReturn(false);

        CoinProcessor processor2 = mock(CoinProcessor.class);
        KlineProcessorAdapter adapter2 = mock(KlineProcessorAdapter.class);
        when(processor2.getKlineAdapter()).thenReturn(adapter2);
        when(adapter2.isStopKline()).thenReturn(false);

        processorMap.put("BTC/USDT", processor1);
        processorMap.put("ETH/USDT", processor2);

        when(processorFactory.getProcessorMap()).thenReturn(processorMap);

        // Call the method
        kLineGeneratorJob.handleHourKLine();

        // Verify that generateKLine is called for both symbols
        verify(adapter1, times(1)).generateKLine(eq(1), eq(Calendar.HOUR_OF_DAY), anyLong());
        verify(adapter2, times(1)).generateKLine(eq(1), eq(Calendar.HOUR_OF_DAY), anyLong());

        System.out.println("[DEBUG_LOG] handleHourKLine correctly processed multiple symbols");
    }

    @Test
    public void testHandleHourKLineTimeBoundaryAlignment() {
        // Call the method
        kLineGeneratorJob.handleHourKLine();

        // Capture the time argument
        ArgumentCaptor<Long> timeCaptor = ArgumentCaptor.forClass(Long.class);
        verify(klineAdapter).generateKLine(anyInt(), anyInt(), timeCaptor.capture());

        long capturedTime = timeCaptor.getValue();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(capturedTime);

        // Verify that the time is properly aligned to hour boundary
        assertEquals(0, calendar.get(Calendar.MINUTE), "Minutes should be 0");
        assertEquals(0, calendar.get(Calendar.SECOND), "Seconds should be 0");
        assertEquals(0, calendar.get(Calendar.MILLISECOND), "Milliseconds should be 0");

        // Verify that it's the previous hour
        Calendar currentHour = Calendar.getInstance();
        currentHour.set(Calendar.MINUTE, 0);
        currentHour.set(Calendar.SECOND, 0);
        currentHour.set(Calendar.MILLISECOND, 0);

        Calendar expectedPreviousHour = (Calendar) currentHour.clone();
        expectedPreviousHour.add(Calendar.HOUR_OF_DAY, -1);

        long timeDifference = Math.abs(capturedTime - expectedPreviousHour.getTimeInMillis());
        assertTrue(timeDifference < 5000, 
            "Captured time should represent the previous hour boundary");

        System.out.println("[DEBUG_LOG] Time boundary alignment verified:");
        System.out.println("[DEBUG_LOG] Captured time: " + calendar.getTime());
        System.out.println("[DEBUG_LOG] Expected previous hour: " + expectedPreviousHour.getTime());
    }
}

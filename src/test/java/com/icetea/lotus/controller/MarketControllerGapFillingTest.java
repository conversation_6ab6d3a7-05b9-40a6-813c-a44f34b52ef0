package com.icetea.lotus.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class to verify MarketController gap filling logic fixes
 * This test ensures that the TradingView F5 refresh gap issue is resolved
 */
@SuppressWarnings("all")
public class MarketControllerGapFillingTest {

    @Mock
    private MarketService marketService;

    private MarketController controller;
    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        controller = new MarketController(
            marketService, null, null, null, null, null, null, null, null, null
        );
        ReflectionTestUtils.setField(controller, "objectMapper", objectMapper);
    }

    @Test
    void testFillKLineGaps_WithProperStartingPrice_NoZeroGaps() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "1min";
        long from = 1752576480000L; // Earlier time
        long to = 1752576660000L;   // Later time

        // Create existing KLines with gaps at the beginning
        List<KLine> existingKLines = Arrays.asList(
            createKLine(1752576600000L, "2970.15", "2975.00", period), // Later period
            createKLine(1752576660000L, "2975.00", "2980.00", period)  // Even later period
        );

        // Mock previous KLine before the requested range
        KLine previousKLine = createKLine(1752576420000L, "2965.00", "2968.50", period);
        when(marketService.findPreviousKline(symbol, period, from))
            .thenReturn(previousKLine);

        // Act
        List<KLine> result = (List<KLine>) ReflectionTestUtils.invokeMethod(
            controller, "fillKLineGaps", existingKLines, from, to, period, symbol
        );

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > existingKLines.size(), "Should have filled gaps");

        // Verify no zero prices in result
        for (KLine kline : result) {
            assertNotNull(kline.getOpenPrice(), "Open price should not be null");
            assertNotNull(kline.getClosePrice(), "Close price should not be null");
            assertTrue(kline.getOpenPrice().compareTo(BigDecimal.ZERO) > 0, 
                      "Open price should be greater than zero: " + kline.getOpenPrice());
            assertTrue(kline.getClosePrice().compareTo(BigDecimal.ZERO) > 0, 
                      "Close price should be greater than zero: " + kline.getClosePrice());
        }

        // Verify first filled gap uses previous KLine's close price
        KLine firstResult = result.get(0);
        assertEquals(previousKLine.getClosePrice(), firstResult.getOpenPrice(),
                    "First gap should use previous KLine's close price as open price");

        System.out.println("[DEBUG_LOG] Gap filling test passed:");
        System.out.println("[DEBUG_LOG] Previous KLine close price: " + previousKLine.getClosePrice());
        System.out.println("[DEBUG_LOG] First result open price: " + firstResult.getOpenPrice());
        System.out.println("[DEBUG_LOG] Total KLines after gap filling: " + result.size());
        System.out.println("[DEBUG_LOG] Original KLines: " + existingKLines.size());

        // Verify the correct method was called
        verify(marketService).findPreviousKline(symbol, period, from);
    }

    @Test
    void testGetProperStartingPrice_Strategy1_EarliestKLineOpenPrice() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "5min";
        long from = 1752576600000L;

        List<KLine> existingKLines = Arrays.asList(
            createKLine(1752576900000L, "2980.00", "2985.00", period), // Later
            createKLine(1752576600000L, "2970.15", "2975.00", period), // Earlier
            createKLine(1752577200000L, "2985.00", "2990.00", period)  // Latest
        );

        // Act
        BigDecimal result = (BigDecimal) ReflectionTestUtils.invokeMethod(
            controller, "getProperStartingPrice", existingKLines, from, period, symbol
        );

        // Assert
        assertNotNull(result);
        assertEquals(new BigDecimal("2970.15"), result, 
                    "Should return earliest KLine's open price");

        System.out.println("[DEBUG_LOG] Strategy 1 test passed:");
        System.out.println("[DEBUG_LOG] Earliest KLine open price: " + result);
    }

    @Test
    void testGetProperStartingPrice_Strategy2_PreviousKLineClosePrice() {
        // Arrange
        String symbol = "BTC/USDT";
        String period = "1min";
        long from = 1752576600000L;

        // Empty existing KLines to trigger strategy 2
        List<KLine> existingKLines = Arrays.asList();

        // Mock previous KLine
        KLine previousKLine = createKLine(1752576540000L, "45000.00", "45100.00", period);
        when(marketService.findPreviousKline(symbol, period, from))
            .thenReturn(previousKLine);

        // Act
        BigDecimal result = (BigDecimal) ReflectionTestUtils.invokeMethod(
            controller, "getProperStartingPrice", existingKLines, from, period, symbol
        );

        // Assert
        assertNotNull(result);
        assertEquals(new BigDecimal("45100.00"), result, 
                    "Should return previous KLine's close price");

        System.out.println("[DEBUG_LOG] Strategy 2 test passed:");
        System.out.println("[DEBUG_LOG] Previous KLine close price: " + result);

        verify(marketService).findPreviousKline(symbol, period, from);
    }

    @Test
    void testGetProperStartingPrice_Strategy3_FirstAvailablePrice() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "15min";
        long from = 1752576600000L;

        List<KLine> existingKLines = Arrays.asList(
            createKLine(1752576900000L, "0", "2985.00", period),        // Zero open price, valid close price
            createKLine(1752577200000L, "2990.00", "2995.00", period)   // Valid open price
        );

        // Mock no previous KLine found
        when(marketService.findPreviousKline(symbol, period, from))
            .thenReturn(null);

        // Act
        BigDecimal result = (BigDecimal) ReflectionTestUtils.invokeMethod(
            controller, "getProperStartingPrice", existingKLines, from, period, symbol
        );

        // Assert
        assertNotNull(result);
        // Strategy 3 should find the first available close price (2985.00) since open price is zero
        assertEquals(new BigDecimal("2985.00"), result, 
                    "Should return first available non-zero price (close price when open is zero)");

        System.out.println("[DEBUG_LOG] Strategy 3 test passed:");
        System.out.println("[DEBUG_LOG] First available price: " + result);
    }

    @Test
    void testGetProperStartingPrice_Strategy4_NoValidPrice() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "30min";
        long from = 1752576600000L;

        // Empty existing KLines
        List<KLine> existingKLines = Arrays.asList();

        // Mock no previous KLine found
        when(marketService.findPreviousKline(symbol, period, from))
            .thenReturn(null);

        // Act
        BigDecimal result = (BigDecimal) ReflectionTestUtils.invokeMethod(
            controller, "getProperStartingPrice", existingKLines, from, period, symbol
        );

        // Assert
        assertNull(result, "Should return null when no valid price is found");

        System.out.println("[DEBUG_LOG] Strategy 4 test passed:");
        System.out.println("[DEBUG_LOG] No valid price found, returned null");
    }

    @Test
    void testFillKLineGaps_SkipsGapFillingWhenNoValidPrice() {
        // Arrange
        String symbol = "ETH/USDT";
        String period = "1min";
        long from = 1752576480000L;
        long to = 1752576660000L;

        // Create existing KLines with gaps and no valid starting price (both open and close are zero)
        List<KLine> existingKLines = Arrays.asList(
            createKLine(1752576600000L, "0", "0", period) // Both open and close prices are zero
        );

        // Mock no previous KLine found
        when(marketService.findPreviousKline(symbol, period, from))
            .thenReturn(null);

        // Act
        List<KLine> result = (List<KLine>) ReflectionTestUtils.invokeMethod(
            controller, "fillKLineGaps", existingKLines, from, to, period, symbol
        );

        // Assert
        assertNotNull(result);
        // Should only contain the original KLine, gaps should be skipped due to no valid starting price
        assertEquals(1, result.size(), "Should skip gap filling when no valid price available");
        assertEquals(existingKLines.get(0), result.get(0), "Should contain original KLine");

        System.out.println("[DEBUG_LOG] Skip gap filling test passed:");
        System.out.println("[DEBUG_LOG] Result size: " + result.size());
        System.out.println("[DEBUG_LOG] Gaps were skipped due to no valid starting price");
    }

    private KLine createKLine(long time, String openPrice, String closePrice, String period) {
        KLine kline = new KLine();
        kline.setTime(time);
        kline.setPeriod(period);
        kline.setOpenPrice(new BigDecimal(openPrice));
        kline.setClosePrice(new BigDecimal(closePrice));
        kline.setHighestPrice(new BigDecimal(closePrice));
        kline.setLowestPrice(new BigDecimal(openPrice));
        kline.setVolume(new BigDecimal("100.0"));
        kline.setTurnover(new BigDecimal("1000.0"));
        kline.setCount(10);
        return kline;
    }
}

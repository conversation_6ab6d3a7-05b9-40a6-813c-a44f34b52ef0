package com.icetea.lotus.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.KLine;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;

@SpringBootTest
@SuppressWarnings("all")
public class KLineSerializationTest {

    @Test
    void testKLineSerializationWithDefaultObjectMapper() throws Exception {
        System.out.println("[DEBUG_LOG] Testing KLine serialization with @JsonSerialize annotations");

        // Create a test KLine
        KLine kLine = new KLine();
        kLine.setOpenPrice(BigDecimal.valueOf(117708.00));
        kLine.setHighestPrice(BigDecimal.valueOf(117900.00));
        kLine.setLowestPrice(BigDecimal.valueOf(117708.00));
        kLine.setClosePrice(BigDecimal.valueOf(117900.00));
        kLine.setTime(1640995200000L);
        kLine.setPeriod("1min");
        kLine.setCount(126);
        kLine.setVolume(BigDecimal.valueOf(82.6041042695936744));
        kLine.setTurnover(BigDecimal.valueOf(9731181.90504768));

        // Test with default ObjectMapper
        ObjectMapper defaultMapper = new ObjectMapper();
        String defaultJson = defaultMapper.writeValueAsString(kLine);
        System.out.println("[DEBUG_LOG] Default ObjectMapper serialization:");
        System.out.println(defaultJson);

        // Test with ObjectMapper that registers modules (like in RedisTemplateConfig)
        ObjectMapper moduleMapper = new ObjectMapper();
        moduleMapper.findAndRegisterModules();
        String moduleJson = moduleMapper.writeValueAsString(kLine);
        System.out.println("[DEBUG_LOG] ObjectMapper with modules serialization:");
        System.out.println(moduleJson);

        // Verify that BigDecimal fields are serialized as simple strings
        System.out.println("[DEBUG_LOG] Checking if BigDecimal fields are serialized correctly...");

        // Check that the JSON contains simple string values, not nested objects
        if (defaultJson.contains("\"openPrice\":\"117708") && 
            defaultJson.contains("\"closePrice\":\"117900") &&
            !defaultJson.contains("\"source\"") && 
            !defaultJson.contains("\"parsedValue\"")) {
            System.out.println("[DEBUG_LOG] SUCCESS: BigDecimal fields are serialized as simple strings!");
        } else {
            System.out.println("[DEBUG_LOG] WARNING: BigDecimal fields might still have nested structure");
        }

        // Same check for modules version
        if (moduleJson.contains("\"openPrice\":\"117708") && 
            moduleJson.contains("\"closePrice\":\"117900") &&
            !moduleJson.contains("\"source\"") && 
            !moduleJson.contains("\"parsedValue\"")) {
            System.out.println("[DEBUG_LOG] SUCCESS: Modules version also serializes BigDecimal as simple strings!");
        } else {
            System.out.println("[DEBUG_LOG] WARNING: Modules version might still have nested structure");
        }

        // Also check what modules are being registered
        System.out.println("[DEBUG_LOG] Registered modules:");
        moduleMapper.getRegisteredModuleIds().forEach(moduleId -> 
            System.out.println("[DEBUG_LOG] - " + moduleId)
        );
    }
}

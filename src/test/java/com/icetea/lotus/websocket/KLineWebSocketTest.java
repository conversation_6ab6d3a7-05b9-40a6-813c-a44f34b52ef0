package com.icetea.lotus.websocket;

import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.handler.WebsocketMarketHandler;
import com.icetea.lotus.job.ExchangePushJob;
import com.icetea.lotus.service.MarketHandlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.math.BigDecimal;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Test for WebSocket K-line functionality according to specification
 */
@SuppressWarnings("all")
public class KLineWebSocketTest {

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private com.icetea.lotus.job.ExchangePushJob pushJob;

    @Mock
    private RedisTemplate redisTemplate;

    private WebsocketMarketHandler websocketHandler;
    private MarketHandlerService marketHandlerService;
    private ExchangePushJob exchangePushJob;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        websocketHandler = new WebsocketMarketHandler(messagingTemplate, pushJob, redisTemplate, exchangePushJob);
        marketHandlerService = new MarketHandlerService(java.util.List.of(websocketHandler));
    }

    @Test
    void testCompleteKLineWebSocketTopic() {
        System.out.println("[DEBUG_LOG] Testing complete K-line WebSocket topic format");

        // Create test K-line
        KLine kLine = createTestKLine("1min");

        // Call handleKLine (complete K-line)
        websocketHandler.handleKLine("BTCUSDT", kLine);

        // Verify the new topic format is used
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Object> dataCaptor = ArgumentCaptor.forClass(Object.class);

        verify(messagingTemplate, times(2)).convertAndSend(topicCaptor.capture(), dataCaptor.capture());

        // Check that both new and old topic formats are called
        var topics = topicCaptor.getAllValues();
        assertTrue(topics.contains("/topic/market/kline/BTCUSDT/1min"), 
                   "Should send to new topic format with period");
        assertTrue(topics.contains("/topic/market/kline/BTCUSDT"), 
                   "Should maintain backward compatibility with old topic format");

        System.out.println("[DEBUG_LOG] Complete K-line WebSocket topic test passed");
    }

    @Test
    void testCurrentKLineWebSocketTopic() {
        System.out.println("[DEBUG_LOG] Testing current K-line WebSocket topic format");

        // Create test K-line
        KLine kLine = createTestKLine("1min");

        // Call handleCurrentKLine (incomplete K-line)
        websocketHandler.handleCurrentKLine("BTCUSDT", kLine);

        // Verify the current K-line topic is used
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Object> dataCaptor = ArgumentCaptor.forClass(Object.class);

        verify(messagingTemplate, times(1)).convertAndSend(topicCaptor.capture(), dataCaptor.capture());

        String topic = topicCaptor.getValue();
        assertEquals("/topic/market/current-kline/BTCUSDT/1min", topic,
                    "Should send to current K-line topic with period");

        // Verify the data contains incomplete flag
        Object data = dataCaptor.getValue();
        assertTrue(data instanceof Map, "Data should be a Map");
        @SuppressWarnings("unchecked")
        Map<String, Object> klineData = (Map<String, Object>) data;

        assertTrue((Boolean) klineData.get("incomplete"), "Current K-line should be marked as incomplete");
        assertEquals("BTCUSDT", klineData.get("symbol"), "Should include symbol in data");

        System.out.println("[DEBUG_LOG] Current K-line WebSocket topic test passed");
    }

    @Test
    void testKLineDataStructure() {
        System.out.println("[DEBUG_LOG] Testing K-line data structure according to specification");

        // Create test K-line
        KLine kLine = createTestKLine("5min");

        // Call handleCurrentKLine
        websocketHandler.handleCurrentKLine("ETHUSDT", kLine);

        // Capture the data
        ArgumentCaptor<Object> dataCaptor = ArgumentCaptor.forClass(Object.class);
        verify(messagingTemplate).convertAndSend(anyString(), dataCaptor.capture());

        Object data = dataCaptor.getValue();
        assertTrue(data instanceof Map, "Data should be a Map");
        @SuppressWarnings("unchecked")
        Map<String, Object> klineData = (Map<String, Object>) data;

        // Verify all required fields according to specification
        assertEquals("ETHUSDT", klineData.get("symbol"));
        assertEquals(System.currentTimeMillis(), ((Long) klineData.get("time")).longValue(), 1000);
        assertEquals("5min", klineData.get("period"));
        assertEquals(new BigDecimal("50000"), klineData.get("openPrice"));
        assertEquals(new BigDecimal("51000"), klineData.get("highestPrice"));
        assertEquals(new BigDecimal("49000"), klineData.get("lowestPrice"));
        assertEquals(new BigDecimal("50500"), klineData.get("closePrice"));
        assertEquals(new BigDecimal("10.5"), klineData.get("volume"));
        assertEquals(new BigDecimal("525000"), klineData.get("turnover"));
        assertEquals(100, klineData.get("count"));
        assertTrue((Boolean) klineData.get("incomplete"));

        System.out.println("[DEBUG_LOG] K-line data structure test passed");
    }

    @Test
    void testMarketHandlerServiceCurrentKLineIntegration() {
        System.out.println("[DEBUG_LOG] Testing MarketHandlerService current K-line integration");

        // Create test K-line
        KLine kLine = createTestKLine("15min");

        // Call through MarketHandlerService
        marketHandlerService.handleCurrentKLine("ADAUSDT", kLine);

        // Verify WebSocket handler was called
        verify(messagingTemplate, times(1)).convertAndSend(
                eq("/topic/market/current-kline/ADAUSDT/15min"), 
                any(Map.class)
        );

        System.out.println("[DEBUG_LOG] MarketHandlerService current K-line integration test passed");
    }

    @Test
    void testDifferentPeriods() {
        System.out.println("[DEBUG_LOG] Testing different K-line periods");

        String[] periods = {"1min", "5min", "15min", "30min", "1hour", "4hour", "1day", "1week", "1mon"};
        String symbol = "BNBUSDT";

        for (String period : periods) {
            KLine kLine = createTestKLine(period);

            // Test complete K-line
            websocketHandler.handleKLine(symbol, kLine);

            // Test current K-line
            websocketHandler.handleCurrentKLine(symbol, kLine);
        }

        // Verify all periods were handled correctly
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        verify(messagingTemplate, times(periods.length * 3)).convertAndSend(topicCaptor.capture(), any(Object.class));

        var topics = topicCaptor.getAllValues();

        // Check that topics for all periods were created
        for (String period : periods) {
            String completeTopic = "/topic/market/kline/" + symbol + "/" + period;
            String currentTopic = "/topic/market/current-kline/" + symbol + "/" + period;

            assertTrue(topics.contains(completeTopic), 
                      "Should have complete topic for period: " + period);
            assertTrue(topics.contains(currentTopic), 
                      "Should have current topic for period: " + period);
        }

        System.out.println("[DEBUG_LOG] Different periods test passed");
    }

    private KLine createTestKLine(String period) {
        KLine kLine = new KLine(period);
        kLine.setTime(System.currentTimeMillis());
        kLine.setOpenPrice(new BigDecimal("50000"));
        kLine.setHighestPrice(new BigDecimal("51000"));
        kLine.setLowestPrice(new BigDecimal("49000"));
        kLine.setClosePrice(new BigDecimal("50500"));
        kLine.setVolume(new BigDecimal("10.5"));
        kLine.setTurnover(new BigDecimal("525000"));
        kLine.setCount(100);
        return kLine;
    }
}

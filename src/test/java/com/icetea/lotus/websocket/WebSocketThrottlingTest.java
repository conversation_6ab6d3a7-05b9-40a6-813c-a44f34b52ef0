package com.icetea.lotus.websocket;

import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.handler.WebsocketMarketHandler;
import com.icetea.lotus.job.ExchangePushJob;
import com.icetea.lotus.service.MarketHandlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Test class to verify WebSocket throttling mechanism
 * This addresses the issue where client UI gets close connection when consuming 5min topic
 */
@SuppressWarnings("all")
public class WebSocketThrottlingTest {

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private ExchangePushJob pushJob;

    @Mock
    private RedisTemplate redisTemplate;

    private WebsocketMarketHandler websocketHandler;

    @Mock
    private ExchangePushJob exchangePushJob;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        websocketHandler = new WebsocketMarketHandler(messagingTemplate, pushJob, redisTemplate, exchangePushJob);
    }

    @Test
    void testThrottlingPreventsRapidUpdates() throws InterruptedException {
        System.out.println("[DEBUG_LOG] Testing throttling mechanism for 5min period");

        // Create test K-line for 5min period
        KLine kLine = createTestKLine("5min");
        String symbol = "ETH/USDT";

        // Send multiple rapid updates (should be throttled)
        for (int i = 0; i < 5; i++) {
            websocketHandler.handleCurrentKLine(symbol, kLine);
            Thread.sleep(100); // 100ms between calls (much faster than 2s throttle interval)
        }

        // Verify that only the first call went through (others were throttled)
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/5min"), 
            any(Object.class)
        );

        System.out.println("[DEBUG_LOG] Throttling test passed - only 1 message sent out of 5 rapid calls");
    }

    @Test
    void testThrottlingAllowsUpdatesAfterInterval() throws InterruptedException {
        System.out.println("[DEBUG_LOG] Testing throttling allows updates after interval");

        KLine kLine = createTestKLine("5min");
        String symbol = "ETH/USDT";

        // Send first update
        websocketHandler.handleCurrentKLine(symbol, kLine);

        // Wait for throttle interval to pass (5min has 2s interval)
        Thread.sleep(2100); // Wait 2.1 seconds

        // Send second update (should go through)
        websocketHandler.handleCurrentKLine(symbol, kLine);

        // Verify both calls went through
        verify(messagingTemplate, times(2)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/5min"), 
            any(Object.class)
        );

        System.out.println("[DEBUG_LOG] Interval test passed - 2 messages sent with proper timing");
    }

    @Test
    void testDifferentPeriodsHaveDifferentThrottleIntervals() throws InterruptedException {
        System.out.println("[DEBUG_LOG] Testing different periods have different throttle intervals");

        String symbol = "ETH/USDT";

        // Test 1min period (1s throttle)
        KLine kLine1min = createTestKLine("1min");
        websocketHandler.handleCurrentKLine(symbol, kLine1min);
        Thread.sleep(500); // Wait 0.5s (less than 1s throttle)
        websocketHandler.handleCurrentKLine(symbol, kLine1min);

        // Test 5min period (2s throttle)  
        KLine kLine5min = createTestKLine("5min");
        websocketHandler.handleCurrentKLine(symbol, kLine5min);
        Thread.sleep(1500); // Wait 1.5s (less than 2s throttle)
        websocketHandler.handleCurrentKLine(symbol, kLine5min);

        // Verify throttling worked for both periods
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/1min"), 
            any(Object.class)
        );
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/5min"), 
            any(Object.class)
        );

        System.out.println("[DEBUG_LOG] Different periods throttling test passed");
    }

    @Test
    void testThrottlingIsPerSymbolPeriodCombination() {
        System.out.println("[DEBUG_LOG] Testing throttling is per symbol-period combination");

        KLine kLine5min = createTestKLine("5min");

        // Send updates for different symbols (should not throttle each other)
        websocketHandler.handleCurrentKLine("ETH/USDT", kLine5min);
        websocketHandler.handleCurrentKLine("BTC/USDT", kLine5min);

        // Send updates for same symbol but different periods (should not throttle each other)
        KLine kLine1min = createTestKLine("1min");
        websocketHandler.handleCurrentKLine("ETH/USDT", kLine1min);

        // Verify all 3 calls went through (different symbol-period combinations)
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/5min"), 
            any(Object.class)
        );
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/BTC/USDT/5min"), 
            any(Object.class)
        );
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/1min"), 
            any(Object.class)
        );

        System.out.println("[DEBUG_LOG] Per symbol-period combination test passed");
    }

    @Test
    void testHighFrequencyTrading5minScenario() throws InterruptedException {
        System.out.println("[DEBUG_LOG] Testing high-frequency trading scenario for 5min period");

        KLine kLine = createTestKLine("5min");
        String symbol = "ETH/USDT";

        // Simulate high-frequency trading (100 updates in 1 second)
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            websocketHandler.handleCurrentKLine(symbol, kLine);
            Thread.sleep(10); // 10ms between calls (very high frequency)
        }
        long endTime = System.currentTimeMillis();

        System.out.println("[DEBUG_LOG] Sent 100 updates in " + (endTime - startTime) + "ms");

        // With 2s throttling for 5min, only 1 update should go through in this timeframe
        verify(messagingTemplate, times(1)).convertAndSend(
            eq("/topic/market/current-kline/ETH/USDT/5min"), 
            any(Object.class)
        );

        System.out.println("[DEBUG_LOG] High-frequency trading test passed - connection overload prevented");
    }

    private KLine createTestKLine(String period) {
        KLine kLine = new KLine();
        kLine.setPeriod(period);
        kLine.setTime(System.currentTimeMillis());
        kLine.setOpenPrice(new BigDecimal("2980.00"));
        kLine.setHighestPrice(new BigDecimal("2985.00"));
        kLine.setLowestPrice(new BigDecimal("2975.00"));
        kLine.setClosePrice(new BigDecimal("2982.50"));
        kLine.setVolume(new BigDecimal("100.5"));
        kLine.setTurnover(new BigDecimal("299250.00"));
        kLine.setCount(50);
        kLine.setIncomplete(true);
        return kLine;
    }
}

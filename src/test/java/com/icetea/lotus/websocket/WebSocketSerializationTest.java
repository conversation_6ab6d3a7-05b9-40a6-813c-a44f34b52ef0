package com.icetea.lotus.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.handler.WebsocketMarketHandler;
import com.icetea.lotus.job.ExchangePushJob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@SuppressWarnings("all")
public class WebSocketSerializationTest {

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ExchangePushJob exchangePushJob;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testWebSocketKLineSerializationWithSimpMessagingTemplate() throws Exception {
        System.out.println("[DEBUG_LOG] Testing KLine serialization through SimpMessagingTemplate");

        // Create a test KLine
        KLine kLine = new KLine();
        kLine.setOpenPrice(BigDecimal.valueOf(117708.00));
        kLine.setHighestPrice(BigDecimal.valueOf(117900.00));
        kLine.setLowestPrice(BigDecimal.valueOf(117708.00));
        kLine.setClosePrice(BigDecimal.valueOf(117900.00));
        kLine.setTime(1640995200000L);
        kLine.setPeriod("1min");
        kLine.setCount(126);
        kLine.setVolume(BigDecimal.valueOf(82.6041042695936744));
        kLine.setTurnover(BigDecimal.valueOf(9731181.90504768));

        // Create WebsocketMarketHandler and send the message
        WebsocketMarketHandler handler = new WebsocketMarketHandler(messagingTemplate, null, redisTemplate, exchangePushJob);
        handler.handleKLine("BTC/USDT", kLine);

        // Capture the argument sent to messagingTemplate
        ArgumentCaptor<Object> messageCaptor = ArgumentCaptor.forClass(Object.class);
        verify(messagingTemplate).convertAndSend(eq("/topic/market/kline/BTC/USDT"), messageCaptor.capture());

        Object sentMessage = messageCaptor.getValue();
        System.out.println("[DEBUG_LOG] Message sent via SimpMessagingTemplate:");
        System.out.println("[DEBUG_LOG] Message class: " + sentMessage.getClass().getName());

        // Serialize the captured message to see what it looks like
        ObjectMapper mapper = new ObjectMapper();
        String serializedMessage = mapper.writeValueAsString(sentMessage);
        System.out.println("[DEBUG_LOG] Serialized message:");
        System.out.println(serializedMessage);

        // Check if the message has the nested structure
        if (serializedMessage.contains("\"source\"") && serializedMessage.contains("\"parsedValue\"")) {
            System.out.println("[DEBUG_LOG] CONFIRMED: Message has nested 'source' and 'parsedValue' structure!");
            System.out.println("[DEBUG_LOG] This explains why the frontend is not working correctly.");
        } else {
            System.out.println("[DEBUG_LOG] Message has normal structure - issue might be elsewhere");
        }
    }
}

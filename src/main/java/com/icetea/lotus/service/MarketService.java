package com.icetea.lotus.service;


import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketService {

    public static final String EXCHANGE_KLINE = "exchange_kline_";
    public static final String EXCHANGE_TRADE = "exchange_trade_";
    private final MongoTemplate mongoTemplate;

    public List<KLine> findAllKLine(String symbol, String period) {
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.DESC, "time"));
        Query query = new Query().with(sort).limit(1000);

        return mongoTemplate.find(query, KLine.class, EXCHANGE_KLINE + symbol + "_" + period);
    }

    public List<KLine> findAllKLine(String symbol, long fromTime, long toTime, String period) {
        Criteria criteria = Criteria.where("time").gte(fromTime).lte(toTime);
        Sort sort = Sort.by(Sort.Order.asc("time"));
        Query query = new Query(criteria).with(sort);
        return mongoTemplate.find(query, KLine.class, EXCHANGE_KLINE + symbol + "_" + period);
    }

    public ExchangeTrade findFirstTrade(String symbol, long fromTime, long toTime) {
        Criteria criteria = Criteria.where("time").gte(fromTime).andOperator(Criteria.where("time").lte(toTime));
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.ASC, "time"));
        Query query = new Query(criteria).with(sort);
        return mongoTemplate.findOne(query, ExchangeTrade.class, EXCHANGE_TRADE + symbol);
    }

    public ExchangeTrade findLastTrade(String symbol, long fromTime, long toTime) {
        Criteria criteria = Criteria.where("time").gte(fromTime).andOperator(Criteria.where("time").lte(toTime));
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.DESC, "time"));
        Query query = new Query(criteria).with(sort);
        return mongoTemplate.findOne(query, ExchangeTrade.class, EXCHANGE_TRADE + symbol);
    }

    public ExchangeTrade findTrade(String symbol, long fromTime, long toTime, Sort.Order order) {
        Criteria criteria = Criteria.where("time").gte(fromTime).andOperator(Criteria.where("time").lte(toTime));
        Sort sort = Sort.by(order);
        Query query = new Query(criteria).with(sort);
        return mongoTemplate.findOne(query, ExchangeTrade.class, EXCHANGE_TRADE + symbol);
    }

    public List<ExchangeTrade> findTradeByTimeRange(String symbol, long timeStart, long timeEnd) {
        Criteria criteria = Criteria.where("time").gte(timeStart).lt(timeEnd);
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.ASC, "time"));
        Query query = new Query(criteria).with(sort);

        return mongoTemplate.find(query, ExchangeTrade.class, EXCHANGE_TRADE + symbol);
    }

    public void saveKLine(String symbol, KLine kLine) {
        Query query = new Query(Criteria.where("time").is(kLine.getTime())
                .and("period").is(kLine.getPeriod())
                .and("symbol").is(symbol));

        Update update = new Update()
                .set("openPrice", kLine.getOpenPrice())
                .set("closePrice", kLine.getClosePrice())
                .set("highestPrice", kLine.getHighestPrice())
                .set("lowestPrice", kLine.getLowestPrice())
                .set("volume", kLine.getVolume())
                .set("turnover", kLine.getTurnover())
                .set("count", kLine.getCount())
                .set("incomplete", kLine.getIncomplete())
                .set("symbol", symbol);

        mongoTemplate.upsert(query, update, EXCHANGE_KLINE + symbol + "_" + kLine.getPeriod());
    }

    @SuppressWarnings("all")
    public BigDecimal findTradeVolume(String symbol, long timeStart, long timeEnd) {
        Criteria criteria = Criteria.where("time").gt(timeStart)
                .andOperator(Criteria.where("time").lte(timeEnd));
        // .andOperator(Criteria.where("volume").gt(0));
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.ASC, "time"));
        Query query = new Query(criteria).with(sort);
        List<KLine> kLines = mongoTemplate.find(query, KLine.class, EXCHANGE_KLINE + symbol + "_1min");
        BigDecimal totalVolume = BigDecimal.ZERO;
        for (KLine kLine : kLines) {
            totalVolume = totalVolume.add(kLine.getTurnover());
        }
        return totalVolume;
    }

    public KLine findPreviousKline(String symbol, String period, long timestamp) {
        Query query = new Query()
                .addCriteria(Criteria.where("time").lt(timestamp))
                .with(Sort.by(Sort.Direction.DESC, "time"))
                .limit(1);
        return mongoTemplate.findOne(query, KLine.class, EXCHANGE_KLINE + symbol + "_" + period);
    }

    public KLine findAllTimeHigh(String symbol, String period) {
        Sort sort = Sort.by(Sort.Order.desc("highestPrice"));
        Query query = new Query().with(sort).limit(1);
        return mongoTemplate.findOne(query, KLine.class, EXCHANGE_KLINE + symbol + "_" + period);
    }

    public KLine findKlineLatest(String symbol, String period) {
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.DESC, "time"));
        Query query = new Query()
                .with(sort)
                .limit(1);
        return mongoTemplate.findOne(query, KLine.class, EXCHANGE_KLINE + symbol + "_" + period);
    }

}

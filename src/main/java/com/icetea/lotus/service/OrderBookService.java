package com.icetea.lotus.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.component.PriceDataPoint;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Queue;

public interface OrderBookService {
    Map<String, ObjectNode> getFutureOrderBook(String symbol);
    Map<String, Object> testFutureOrderBook(String symbol, Integer action);
    Queue<PriceDataPoint> getDataPoint(String symbol);

    void testUpdateMarkPrice(String symbol, BigDecimal markPrice);
}

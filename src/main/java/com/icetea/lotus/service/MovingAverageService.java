package com.icetea.lotus.service;

import com.icetea.lotus.component.PriceDataPoint;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Service
public class MovingAverageService {

    private final Map<String, Queue<PriceDataPoint>> symbolDataPoints = new ConcurrentHashMap<>();

    public synchronized void addDataPoint(String symbol, BigDecimal impactMidPrice, BigDecimal indexPrice) {
        Queue<PriceDataPoint> dataPoints = symbolDataPoints.computeIfAbsent(
                symbol, k -> new ConcurrentLinkedQueue<>()
        );

        // Create new data point
        LocalDateTime dateTime = LocalDateTime.now();
        PriceDataPoint dataPoint = new PriceDataPoint(
                dateTime,
                impactMidPrice,
                indexPrice
        );

        // Add to queue
        dataPoints.offer(dataPoint);

        // Clean old data points (older than 10 minutes)
        cleanOldDataPoints(symbol, dateTime);
    }
    
    public synchronized BigDecimal getCurrentMovingAverage(String symbol) {
        return calculateMovingAverage(symbol);
    }

    public Queue<PriceDataPoint> getDataPoint(String symbol) {
        return symbolDataPoints.get(symbol);
    }

    private void cleanOldDataPoints(String symbol, LocalDateTime currentTime) {
        Queue<PriceDataPoint> dataPoints = symbolDataPoints.get(symbol);
        if (dataPoints == null) return;

        int windowMinutes = 10;
        LocalDateTime cutoffTime = currentTime.minusMinutes(windowMinutes);

        dataPoints.removeIf(dataPoint ->
                dataPoint.getTimestamp().isBefore(cutoffTime)
        );
    }
    
    private BigDecimal calculateMovingAverage(String symbol) {
        Queue<PriceDataPoint> dataPoints = symbolDataPoints.get(symbol);
        List<PriceDataPoint> recentPoints = new ArrayList<>(dataPoints);
        
        if (recentPoints.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // Sort by timestamp (most recent first)
        recentPoints.sort((a, b) -> b.getTimestamp().compareTo(a.getTimestamp()));
        
        // Calculate moving average
        BigDecimal sum = recentPoints.stream()
            .map(PriceDataPoint::getNormalizedDifference)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return sum.divide(
                BigDecimal.valueOf(recentPoints.size()),
                8,
                RoundingMode.HALF_UP
        );
    }
}

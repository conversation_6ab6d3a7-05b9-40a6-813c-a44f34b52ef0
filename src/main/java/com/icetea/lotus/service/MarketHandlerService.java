package com.icetea.lotus.service;

import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.handler.MarketHandler;
import com.icetea.lotus.handler.MongoMarketHandler;
import com.icetea.lotus.handler.WebsocketMarketHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service that manages all MarketHandlers and provides centralized handling
 * This service encapsulates all MarketHandler instances and provides a single point
 * for handling market data across all processors.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MarketHandlerService {

    private final List<MarketHandler> marketHandlers;

    /**
     * Handle trade data by delegating to all registered MarketHandlers
     *
     * @param symbol        The trading symbol
     * @param exchangeTrade The trade data
     * @param thumb         The coin thumb data
     */
    public void handleTrade(String symbol, ExchangeTrade exchangeTrade, CoinThumb thumb) {
        log.debug("Handling trade for symbol: {}", symbol);
        for (MarketHandler handler : marketHandlers) {
            try {
                handler.handleTrade(symbol, exchangeTrade, thumb);
            } catch (Exception e) {
                log.error("Error handling trade with handler {}: {}", handler.getClass().getSimpleName(), e.getMessage(), e);
            }
        }
    }

    /**
     * Handle K-line data by delegating to all registered MarketHandlers
     *
     * @param symbol The trading symbol
     * @param kLine  The K-line data
     */
    public void handleKLine(String symbol, KLine kLine) {
        log.debug("Handling K-line for symbol: {}", symbol);
        for (MarketHandler handler : marketHandlers) {
            try {
                handler.handleKLine(symbol, kLine);
            } catch (Exception e) {
                log.error("Error handling K-line with handler {}: {}", handler.getClass().getSimpleName(), e.getMessage(), e);
            }
        }
    }

    /**
     * Handle current (incomplete) K-line data by delegating to WebSocket handlers
     *
     * @param symbol The trading symbol
     * @param kLine  The current K-line data
     */
    public void handleCurrentKLine(String symbol, KLine kLine) {
        log.debug("Handling current K-line for symbol: {}", symbol);
        for (MarketHandler handler : marketHandlers) {
            try {
                // Only call handleCurrentKLine if the handler supports it (WebsocketMarketHandler)
                if (handler instanceof WebsocketMarketHandler websocketMarketHandler) {
                    websocketMarketHandler.handleCurrentKLine(symbol, kLine);
                }
            } catch (Exception e) {
                log.error("Error handling current K-line with handler {}: {}", handler.getClass().getSimpleName(), e.getMessage(), e);
            }
        }
    }

    /**
     * Get the number of registered handlers
     *
     * @return The number of handlers
     */
    public int getHandlerCount() {
        return marketHandlers.size();
    }

    /**
     * Get all registered handlers (for testing purposes)
     *
     * @return List of all handlers
     */
    public List<MarketHandler> getHandlers() {
        return marketHandlers;
    }

    /**
     * Handle K-line data persistence by delegating only to MongoMarketHandler
     * This method is specifically for persisting K-line data to the database without
     * broadcasting to WebSocket clients.
     *
     * @param symbol The trading symbol
     * @param kLine  The K-line data to persist
     */
    public void handlePersistKLine(String symbol, KLine kLine) {
        log.debug("Persisting K-line for symbol: {}", symbol);
        for (MarketHandler handler : marketHandlers) {
            try {
                // Only call handleKLine on MongoMarketHandler for persistence
                if (handler instanceof MongoMarketHandler mongoMarketHandler) {
                    mongoMarketHandler.handleKLine(symbol, kLine);
                    log.debug("Persisted K-line with handler: {}", handler.getClass().getSimpleName());
                }
            } catch (Exception e) {
                log.error("Error persisting K-line with handler {}: {}", handler.getClass().getSimpleName(), e.getMessage(), e);
            }
        }
    }
}

package com.icetea.lotus.service;


import com.icetea.lotus.dto.CoinListResponseDto;
import com.icetea.lotus.dto.TopCoinResponseDto;
import com.icetea.lotus.entity.CoinThumb;
import org.springframework.data.domain.Page;

import java.util.List;

public interface CoinThumbService {
    List<CoinThumb> findSymbolThumb();

    Page<TopCoinResponseDto> findTopGainer(int pageNo, int pageSize);

    CoinThumb getCoinThumb(String symbol);

    Page<TopCoinResponseDto> findTopTrending(int pageNo, int pageSize);

    Page<TopCoinResponseDto> findNewlyListed(int pageNo, int pageSize);
    Page<CoinListResponseDto> findCoinList(int pageNo, int pageSize, String symbol, String coinBase, String sortType);
}

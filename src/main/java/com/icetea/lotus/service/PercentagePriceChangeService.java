package com.icetea.lotus.service;

import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.enums.PeriodType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class PercentagePriceChangeService {

    private final MarketService marketService;

    /**
     * Calculates the percentage price change of a given symbol over a specified period.
     * <p>
     * The formula used is:
     * <pre>
     *     Price Change (%) = ((P_now - P_before) / P_before) * 100
     * </pre>
     * where:
     * <ul>
     *   <li><b>P_now</b> is the closing price of the latest candle (at the end of the period)</li>
     *   <li><b>P_before</b> is the closing price of the earliest candle (at the beginning of the period)</li>
     * </ul>
     * <p>
     * If no data is found or P_before is zero, the method returns 0.
     *
     * @param symbol the trading symbol (e.g. "BTCUSDT")
     * @param period the time range to calculate change over. Must be one of:
     *               "ONE_HOUR", "ONE_DAY", "ONE_WEEK"
     * @return the percentage change in price as {@link BigDecimal}, rounded to 2 decimal places
     */
    public BigDecimal calculateChg(String symbol, String period) {
        log.info("Start calculateChg, symbol: {}, period: {}", symbol, period);

        long periodTimeMillis;
        PeriodType periodType = PeriodType.fromValue(period);
        switch (periodType) {
            case ONE_HOUR -> periodTimeMillis = 60 * 60 * 1000L;
            case ONE_DAY -> periodTimeMillis = 24 * 60 * 60 * 1000L;
            case ONE_WEEK -> periodTimeMillis = 7 * 24 * 60 * 60 * 1000L;
            default -> throw new IllegalArgumentException("Invalid period: " + period);
        }

        long now = Instant.now().toEpochMilli();
        long from = now - periodTimeMillis;

        List<KLine> kLines = marketService.findAllKLine(symbol, from, now, PeriodType.ONE_MIN.getValue());

        if (kLines.isEmpty()) {
            log.warn("No KLine data found for symbol {} in period {}", symbol, period);
            return BigDecimal.ZERO;
        }

        KLine first = kLines.stream()
                .filter(k -> k.getClosePrice() != null)
                .min(Comparator.comparing(KLine::getTime))
                .orElse(null);

        KLine last = kLines.stream()
                .filter(k -> k.getClosePrice() != null)
                .max(Comparator.comparing(KLine::getTime))
                .orElse(null);

        if (first == null || last == null || first.getClosePrice().compareTo(BigDecimal.ZERO) == 0) {
            log.warn("Invalid price data for symbol: {}", symbol);
            return BigDecimal.ZERO;
        }

        BigDecimal priceFirst = first.getClosePrice();
        BigDecimal priceLast = last.getClosePrice();

        BigDecimal change = priceLast.subtract(priceFirst)
                .divide(priceFirst, 6, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

        return change.setScale(2, RoundingMode.HALF_UP);
    }
}

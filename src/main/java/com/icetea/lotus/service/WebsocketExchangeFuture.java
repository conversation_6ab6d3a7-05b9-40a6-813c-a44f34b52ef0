package com.icetea.lotus.service;

import com.icetea.lotus.dto.ContractCoinInfo;

import java.math.BigDecimal;

public interface WebsocketExchangeFuture {
    BigDecimal calculateIndexPriceToWebsocket(String symbol);

    void connectGate(String symbol);

    void connectOkx(String symbol);

    void connectBitget(String symbol);

    void connectBinance(String symbol);

    void updateContractCoinInfoWebSocket(String symbol, ContractCoinInfo coinInfo, BigDecimal indexPrice);
}

package com.icetea.lotus.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.enums.ExchangeFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;

@Slf4j
@Service
public class ExchangeService {
    private final ObjectMapper objectMapper;

    @Autowired
    public ExchangeService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public BigDecimal fetchBinanceFuturesLastPrices(String symbol) {
        try {
            String binanceSymbol = toExchangeFutureSymbol(symbol, ExchangeFuture.BINANCE_FUTURE);
            String url = String.format(CommonConstants.GetPremiumIndex.URL_BINANCE_LAST_PRICE, binanceSymbol);

            log.info("Fetching Binance price for symbol = {}, binanceSymbol= {}", symbol, binanceSymbol);

            JsonNode node = objectMapper.readTree(getRequest(url));
            return new BigDecimal(node.get("lastPrice").asText());

        } catch (Exception e) {
            log.error("Error fetching Binance price for {}: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    public BigDecimal fetchBitgetFuturesLastPrices(String symbol) {
        try {
            symbol = toExchangeFutureSymbol(symbol, ExchangeFuture.BITGET_FUTURE);
            String url = String.format(CommonConstants.GetPremiumIndex.URL_BIT_GET_LAST_PRICE, symbol.toUpperCase());

            log.debug("Fetching KuCoin price for symbol: {}", symbol);
            JsonNode node = objectMapper.readTree(getRequest(url));
            JsonNode data = node.get("data");
            BigDecimal lastPrice = BigDecimal.ZERO;
            if (data != null && data.isArray() && !data.isEmpty()) {
                lastPrice = new BigDecimal(data.get(0).get("lastPr").asText());
            }
            return lastPrice;

        } catch (Exception e) {
            log.error("Error fetching KuCoin price for {}: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    public BigDecimal fetchOkxFuturesLastPrices(String symbol) {
        try {
            String okxSymbol = toExchangeFutureSymbol(symbol, ExchangeFuture.OKX_FUTURE);
            String url = String.format(CommonConstants.GetPremiumIndex.URL_OKX_LAST_PRICE, okxSymbol);

            log.debug("Fetching okx last price for symbol: {} (converted to: {})", symbol, okxSymbol);
            JsonNode node = objectMapper.readTree(getRequest(url));
            JsonNode data = node.get("data");
            BigDecimal lastPrice = BigDecimal.ZERO;
            if (data != null && data.isArray() && !data.isEmpty()) {
                lastPrice = new BigDecimal(data.get(0).get("last").asText());
            }
            return lastPrice;

        } catch (Exception e) {
            log.error("Error fetching okx last price for {}: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    public BigDecimal fetchGateIoFuturesLastPrices(String symbol) {
        try {
            String okxSymbol = toExchangeFutureSymbol(symbol, ExchangeFuture.GATEIO_FUTURE);
            String url = String.format(CommonConstants.GetPremiumIndex.URL_GATE_IO_LAST_PRICE, okxSymbol);

            log.debug("Fetching gate.io last price for symbol: {} (converted to: {})", symbol, okxSymbol);
            JsonNode node = objectMapper.readTree(getRequest(url));
            BigDecimal lastPrice = BigDecimal.ZERO;
            if (node != null && node.isArray() && !node.isEmpty()) {
                lastPrice = new BigDecimal(node.get(0).get("last").asText());
            }
            return lastPrice;

        } catch (Exception e) {
            log.error("Error fetching gate.io price for {}: {}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    public String toExchangeFutureSymbol(String symbol, ExchangeFuture exchange) {
        if (symbol == null || !symbol.contains("/")) {
            return symbol; // fallback If not format BTC/USDT
        }

        String[] parts = symbol.split("/");
        if (parts.length != 2) {
            return symbol; // fallback
        }

        String base = parts[0].toUpperCase();
        String quote = parts[1].toUpperCase();
        String futureSymbol = switch (exchange) {
            case BINANCE_FUTURE, BITGET_FUTURE -> base + quote;     // Binance futures: BTCUSDT
            case OKX_FUTURE -> base + "-" + quote;        // OKX perpetual: BTC-USDT
            case GATEIO_FUTURE -> base + "_" + quote;               // Gate.io perpetual: BTC_USDT
        };

        return futureSymbol.toUpperCase();
    }


    private String getRequest(String url) {
        HttpClient client = HttpClientBuilder.create().build();
        HttpGet request = new HttpGet(url);
        HttpResponse response = null;
        try {
            response = client.execute(request);
            return EntityUtils.toString(response.getEntity());
        } catch (IOException e) {
            log.error("getRequest from {} - get error {}", url, e.getMessage());
            return "";
        }
    }
}
package com.icetea.lotus.service;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.dto.CoinListResponseDto;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.util.MessageResult;
import org.springframework.data.domain.Page;

public interface FavorCoinService {
    Page<CoinListResponseDto> findFavorCoin(@CurrentUser AuthMember member, int pageNo, int pageSize, String symbol, String coinBase, String sortType);
    MessageResult addFavor(@CurrentUser AuthMember member, String symbol);
    MessageResult deleteFavor(@CurrentUser AuthMember member, String symbol);
}

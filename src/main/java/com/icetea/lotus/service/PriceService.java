package com.icetea.lotus.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PriceService {
    BigDecimal calculateIndexPrice(String symbol);

    BigDecimal calculateMarkPrice(String symbol, BigDecimal indexPrice, BigDecimal basisRate);

    BigDecimal calculateImpactMidPrice(String symbol, BigDecimal indexPrice, Map<String, Object> orderBook);

    BigDecimal calculateAveragePremiumIndex(List<BigDecimal> premiumIndexes, int scale);

    BigDecimal calculateFundingRate(BigDecimal avgPremiumIndex, BigDecimal interestRate,
                                    BigDecimal currentPremiumIndex, int scale);

    BigDecimal calculatePremiumIndex(Map<String, Object> orderBook, BigDecimal indexPrice, int scale);

    BigDecimal calculateInterestRate (int fundingIntervalHours);
}

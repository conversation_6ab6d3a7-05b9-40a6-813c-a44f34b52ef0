package com.icetea.lotus.service.impl;

import com.icetea.lotus.dto.CoinListResponseDto;
import com.icetea.lotus.dto.TopCoinResponseDto;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinThumbService;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.util.PaginationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.icetea.lotus.constants.CommonConstants.COIN_EXPIRE_TIME;
import static com.icetea.lotus.constants.CommonConstants.COIN_PREFIX;

@Slf4j
@Service
@RequiredArgsConstructor
public class CoinThumbServiceImpl implements CoinThumbService {
    private final Logger logger = org.slf4j.LoggerFactory.getLogger(CoinThumbServiceImpl.class);
    private final ExchangeCoinService coinService;
    private final CoinProcessorFactory coinProcessorFactory;
    private final CoinService coinInfoService;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public List<CoinThumb> findSymbolThumb() {
        List<ExchangeCoin> coins = this.visibleCoin();
        List<CoinThumb> thumbs = new ArrayList<>();
        for (ExchangeCoin coin : coins) {
            CoinProcessor processor = coinProcessorFactory.getProcessor(coin.getSymbol());
            CoinThumb thumb = processor.getThumb();
            thumb.setZone(coin.getZone());
            thumbs.add(thumb);
        }
        return thumbs;
    }

    @Override
    public Page<TopCoinResponseDto> findTopGainer(int pageNo, int pageSize) {
        logger.info("Finding top gainers with pageNo: {}, pageSize: {}", pageNo, pageSize);
        List<CoinThumb> thumbs = this.findSymbolThumb();
        List<TopCoinResponseDto> result = new ArrayList<>();

        for (CoinThumb thumb : thumbs) {
            logger.info("Processing thumb for symbol: {}", thumb.getSymbol());
            String redisKey = COIN_PREFIX + thumb.getSymbol().substring(0, thumb.getSymbol().indexOf("/"));
            Coin coin = this.getCoin(thumb.getSymbol().substring(0, thumb.getSymbol().indexOf("/")), redisKey);

            if (coin != null) {
                logger.info("Found coin {} in cache/db with icon URL: {}", coin.getUnit(), coin.getIconUrl());
                TopCoinResponseDto responseDto = TopCoinResponseDto.builder()
                        .iconUrl(coin.getIconUrl())
                        .coinThumb(thumb)
                        .build();
                result.add(responseDto);
            } else {
                logger.info("Coin not found for symbol: {}", thumb.getSymbol());
            }
        }

        result.sort((o1, o2) -> o2.getCoinThumb().getChg().compareTo(o1.getCoinThumb().getChg()));

        PageRequest pageRequest = PageRequest.of(pageNo, pageSize);
        return PaginationUtils.toPage(result, pageRequest);
    }

    @Override
    public CoinThumb getCoinThumb(String symbol) {
        CoinProcessor processor = coinProcessorFactory.getProcessor(symbol);
        return processor.getThumb();
    }

    @Override
    public Page<TopCoinResponseDto> findTopTrending(int pageNo, int pageSize) {
        logger.info("Finding top trending coins with pageNo: {}, pageSize: {}", pageNo, pageSize);
        List<CoinThumb> thumbs = this.findSymbolThumb();
        List<TopCoinResponseDto> result = new ArrayList<>();

        for (CoinThumb thumb : thumbs) {
            logger.info("Processing top trending thumb for symbol: {}", thumb.getSymbol());
            String redisKey = COIN_PREFIX + thumb.getSymbol().substring(0, thumb.getSymbol().indexOf("/"));
            Coin coin = this.getCoin(thumb.getSymbol().substring(0, thumb.getSymbol().indexOf("/")), redisKey);

            if (coin != null) {
                logger.info("Found coin top trending {} in cache: {}", coin.getUnit(), coin.getIconUrl());
                TopCoinResponseDto responseDto = TopCoinResponseDto.builder()
                        .iconUrl(coin.getIconUrl())
                        .coinThumb(thumb)
                        .build();
                result.add(responseDto);
            } else {
                logger.info("Coin not found top trending for symbol: {}", thumb.getSymbol());
            }
        }

        // Sort by turnover first
        result.sort((o1, o2) -> o2.getCoinThumb().getTurnover().compareTo(o1.getCoinThumb().getTurnover()));

        PageRequest pageRequest = PageRequest.of(pageNo, pageSize);
        return PaginationUtils.toPage(result, pageRequest);
    }

    @Override
    public Page<TopCoinResponseDto> findNewlyListed(int pageNo, int pageSize) {
        logger.info("Finding newly listed coins with pageNo: {}, pageSize: {}", pageNo, pageSize);
        List<ExchangeCoin> allCoins = this.visibleCoin()
                .stream()
                .sorted(
                        Comparator.comparing(ExchangeCoin::getStartTime,
                                Comparator.nullsLast(Comparator.naturalOrder())
                        ).reversed()
                ).toList();
        logger.info("Found {} total newly listed coins", allCoins.size());

        // Apply pagination to the coin list first
        int startIndex = pageNo * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allCoins.size());

        List<ExchangeCoin> coins;
        if (startIndex >= allCoins.size()) {
            logger.info("Page {} is out of bounds, returning empty page", pageNo);
            coins = new ArrayList<>();
        } else {
            coins = allCoins.subList(startIndex, endIndex);
        }

        List<TopCoinResponseDto> result = new ArrayList<>();
        for (ExchangeCoin exchangeCoin : coins) {
            logger.info("Processing newly listed coin: {}", exchangeCoin.getSymbol());
            CoinProcessor processor = coinProcessorFactory.getProcessor(exchangeCoin.getSymbol());
            CoinThumb thumb = processor.getThumb();

            String redisKey = COIN_PREFIX + exchangeCoin.getCoinSymbol();
            Coin coin = this.getCoin(exchangeCoin.getCoinSymbol(), redisKey);

            if (coin != null && thumb != null) {
                logger.info("Found newly listed coin {} in cache: {}", coin.getUnit(), coin.getIconUrl());
                TopCoinResponseDto responseDto = TopCoinResponseDto.builder()
                        .iconUrl(coin.getIconUrl())
                        .coinThumb(thumb)
                        .build();
                result.add(responseDto);
            } else {
                logger.info("Newly listed coin or thumb not found for symbol: {}", exchangeCoin.getSymbol());
            }
        }
        logger.info("Returning {} newly listed coins for page {} with size {}", result.size(), pageNo, pageSize);
        PageRequest pageRequest = PageRequest.of(pageNo, pageSize);
        return new PageImpl<>(result, pageRequest, allCoins.size());
    }

    @Override
    public Page<CoinListResponseDto> findCoinList(int pageNo, int pageSize, String symbol, String coinBase, String sortType) {
        Page<ExchangeCoin> exchangeCoins = coinService.findExchangeCoinByFilter(pageNo, pageSize, symbol, coinBase, sortType);
        List<CoinListResponseDto> result = new ArrayList<>();
        logger.info("Finding coin list with pageNo: {}, pageSize: {}, symbol: {}", pageNo, pageSize, symbol);

        for (ExchangeCoin exchangeCoin : exchangeCoins.getContent()) {
            logger.info("Processing exchange coin: {}", exchangeCoin.getSymbol());
            CoinProcessor processor = coinProcessorFactory.getProcessor(exchangeCoin.getSymbol());
            CoinThumb thumb = processor.getThumb();

            String redisKey = COIN_PREFIX + exchangeCoin.getCoinSymbol();
            Coin coin = this.getCoin(exchangeCoin.getCoinSymbol(), redisKey);

            if (coin != null && thumb != null) {
                BigDecimal marketCap = thumb.getClose().multiply(coin.getCirculationSupply());
                if (marketCap.compareTo(BigDecimal.ZERO) <= 0) {
                    marketCap = BigDecimal.ZERO;
                    logger.info("Market cap for {} is zero or negative, setting to zero", exchangeCoin.getSymbol());
                }
                CoinListResponseDto responseDto = CoinListResponseDto.builder()
                        .marketCap(marketCap.setScale(0, RoundingMode.DOWN))
                        .iconUrl(coin.getIconUrl())
                        .coinThumb(thumb)
                        .build();

                result.add(responseDto);
                logger.info("Added coin {} to result list with market cap {}", coin.getUnit(), marketCap);
            } else {
                logger.info("Skipping coin {} - coin or thumb data is null", exchangeCoin.getSymbol());
            }
        }
        if (sortType == null) {
            result.sort((a, b) -> b.getMarketCap().compareTo(a.getMarketCap()));
        }
        logger.info("Returning sorted coin list with {} entries", result);
        return new PageImpl<>(result, exchangeCoins.getPageable(), exchangeCoins.getTotalElements());
    }

    private List<ExchangeCoin> visibleCoin() {
        return coinService.findAllVisible();
    }

    private Coin getCoin(String coinSymbol, String redisKey) {
        Coin coin = (Coin) redisTemplate.opsForValue().get(redisKey);
        if (coin == null) {
            coin = coinInfoService.findByUnit(coinSymbol);
            if (coin != null) {
                redisTemplate.opsForValue().set(redisKey, coin, COIN_EXPIRE_TIME, TimeUnit.SECONDS);
            }
        }
        return coin;
    }
}

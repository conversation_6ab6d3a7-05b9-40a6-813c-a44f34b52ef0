package com.icetea.lotus.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.config.OrderBookFutureStorage;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.constants.CommonConstants.GetPremiumIndexWebsocket;
import com.icetea.lotus.dto.ContractCoinInfo;
import com.icetea.lotus.dto.RealtimeWalletWhenChangeMarkPrice;
import com.icetea.lotus.dto.UpdatePositionWithMarkPrice;
import com.icetea.lotus.enums.ExchangeFuture;
import com.icetea.lotus.service.ExchangeService;
import com.icetea.lotus.service.MovingAverageService;
import com.icetea.lotus.service.PremiumIndexService;
import com.icetea.lotus.service.PriceService;
import com.icetea.lotus.service.WebsocketExchangeFuture;
import com.icetea.lotus.util.RedisEventPublisher;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebsocketExchangeFutureImpl implements WebsocketExchangeFuture {
    private final Map<String, BigDecimal> lastPrices = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper;
    private final ExchangeService exchangeService;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);
    private final Map<String, WebSocketClient> connections = new ConcurrentHashMap<>();
    private final OrderBookFutureStorage orderBookFutureStorage;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisEventPublisher redisEventPublisher;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final PriceService priceService;
    private final Map<String, ContractCoinInfo> contractCoinInfoMap = new ConcurrentHashMap<>();
    private final MovingAverageService movingAverageService;
    private final SimpMessagingTemplate messagingTemplate;
    private final PremiumIndexService premiumIndexService;

    @Value("${topic-kafka.contract.position-update-with-mark-price}")
    private String positionUpdateWithMarkPriceTopic;

    @Value("${topic-kafka.contract.wallet-realtime-with-mark-price}")
    private String walletRealtimeWithMarkPriceTopic;

    @PostConstruct
    public void startIndexPriceScheduler() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                Map<String, String> symbolMap = getListEnabledSymbol();
                symbolMap.keySet().forEach(this::calculateIndexPriceToWebsocket);
            } catch (Exception e) {
                log.error("Error in index price scheduler: {}", e.getMessage(), e);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }


    @Override
    @SuppressWarnings("all")
    public BigDecimal calculateIndexPriceToWebsocket(String symbol) {
        try {
            // Lấy giá từ các exchange
            BigDecimal gatePrice = lastPrices.get("gate_" + symbol);
            BigDecimal okxPrice = lastPrices.get("okx_" + symbol);
            BigDecimal bitgetPrice = lastPrices.get("bitget_" + symbol);
            BigDecimal binancePrice = lastPrices.get("binance_" + symbol);

            // Đếm số exchange có giá
            int count = 0;
            BigDecimal total = BigDecimal.ZERO;

            if (gatePrice != null) {
                total = total.add(gatePrice);
                count++;
            }
            if (okxPrice != null) {
                total = total.add(okxPrice);
                count++;
            }
            if (bitgetPrice != null) {
                total = total.add(bitgetPrice);
                count++;
            }
            if (binancePrice != null) {
                total = total.add(binancePrice);
                count++;
            }

            if (count == 0) {
                log.warn(" No price data available for symbol: {}", symbol);
                return null;
            }

            // Tính trung bình
            BigDecimal indexPrice = total.divide(BigDecimal.valueOf(count), 8, RoundingMode.HALF_UP);
            if (contractCoinInfoMap.isEmpty()) {
                Map<String, String> symbolMap = getListEnabledSymbol();
                symbolMap.forEach((key, value) -> contractCoinInfoMap.put(key, new ContractCoinInfo()));
            }
            ContractCoinInfo coinInfo = contractCoinInfoMap.get(symbol);
            this.updateContractCoinInfoWebSocket(symbol, coinInfo, indexPrice);

            return indexPrice;
        } catch (Exception e) {
            log.error("Error calculating index price for {}: {}", symbol, e.getMessage());
            return null;
        }
    }

    @Override
    public void connectGate(String symbol) {
        try {
            WebSocketClient client = new WebSocketClient(new URI(GetPremiumIndexWebsocket.WEBSOCKET_GATE_IO_LAST_PRICE)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("Connected to Gate.io WebSocket for symbol: {}", symbol);
                    String futureSymbol = exchangeService.toExchangeFutureSymbol(symbol, ExchangeFuture.GATEIO_FUTURE);

                    String subMessage = String.format("""
                            {
                                "channel": "spot.tickers",
                                "event": "subscribe",
                                "payload": ["%s"]
                            }
                            """, futureSymbol);

                    send(subMessage);
                    log.info("Subscribed to Gate.io ticker: {}", futureSymbol);
                }

                @Override
                public void onMessage(String message) {
                    try {
                        JsonNode json = objectMapper.readTree(message);
                        if (json.has("result")) {
                            JsonNode result = json.get("result");
                            if (result != null && result.isArray() && !result.isEmpty()) {
                                BigDecimal last = new BigDecimal(result.get(0).get("last").asText());
                                lastPrices.put("gate_" + symbol, last);
                            }
                        }
                    } catch (Exception e) {
                        log.debug("Failed to parse Gate.io message for {}: {}", symbol, e.getMessage());
                    }
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn("Gate.io connection closed for {}: {} {}", symbol, code, reason);
                    // Remove from connections map
                    connections.remove("gate_" + symbol);
                    scheduleReconnect("gate_" + symbol, () -> connectGate(symbol));
                }

                @Override
                public void onError(Exception ex) {
                    log.error("Gate.io WebSocket error for {}: {}", symbol, ex.getMessage());
                }
            };

            client.connect();
            connections.put("gate_" + symbol, client);

        } catch (Exception e) {
            log.error("Failed to connect Gate.io for {}: {}", symbol, e.getMessage());
        }
    }

    @Override
    public void connectOkx(String symbol) {
        try {
            String futureSymbol = exchangeService.toExchangeFutureSymbol(symbol, ExchangeFuture.OKX_FUTURE);
            WebSocketClient client = new WebSocketClient(new URI(GetPremiumIndexWebsocket.WEBSOCKET_OKX_LAST_PRICE)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("Connected to OKX WebSocket for symbol: {}", futureSymbol);
                    String subMessage = String.format("""
                            {
                                "op": "subscribe",
                                "args": [
                                    {
                                        "channel": "tickers",
                                        "instId": "%s"
                                    }
                                ]
                            }
                            """, futureSymbol);

                    send(subMessage);
                    log.info("Subscribed to OKX ticker: {}", futureSymbol);
                }

                @Override
                public void onMessage(String message) {
                    try {
                        JsonNode json = objectMapper.readTree(message);
                        if (json.has("data")) {
                            JsonNode dataArray = json.get("data");
                            if (dataArray != null && dataArray.isArray() && !dataArray.isEmpty()) {
                                JsonNode data = dataArray.get(0);
                                BigDecimal last = new BigDecimal(data.get("last").asText());
                                lastPrices.put("okx_" + symbol, last);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Failed to parse OKX message for {}: {}", symbol, e.getMessage());
                    }
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn(" OKX connection closed for {}: {} {}", symbol, code, reason);
                    connections.remove("okx_" + symbol);
                    scheduleReconnect("okx_" + symbol, () -> connectOkx(symbol));
                }

                @Override
                public void onError(Exception ex) {
                    log.error(" OKX WebSocket error for {}: {}", symbol, ex.getMessage());
                }
            };

            client.connect();
            connections.put("okx_" + symbol, client);

        } catch (Exception e) {
            log.error("Failed to connect OKX for {}: {}", symbol, e.getMessage());
        }
    }

    @Override
    public void connectBitget(String symbol) {
        try {
            String futureSymbol = exchangeService.toExchangeFutureSymbol(symbol, ExchangeFuture.BITGET_FUTURE);
            WebSocketClient client = new WebSocketClient(new URI(GetPremiumIndexWebsocket.WEBSOCKET_BIT_GET_LAST_PRICE)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info(" Connected to Bitget WebSocket for symbol: {}", symbol);

                    String subMessage = String.format("""
                            {
                              "op": "subscribe",
                              "args": [
                                {
                                  "instType": "SPOT",
                                  "channel": "ticker",
                                  "instId": "%s"
                                }
                              ]
                            }
                            """, futureSymbol);

                    send(subMessage);
                    log.info(" Subscribed to Bitget ticker: {}", symbol);
                }

                @Override
                public void onMessage(String message) {
                    try {
                        JsonNode json = objectMapper.readTree(message);
                        if (json.has("data")) {
                            JsonNode data = json.get("data");
                            if (data != null && data.isArray() && !data.isEmpty()) {
                                BigDecimal lastPrice = new BigDecimal(data.get(0).get("lastPr").asText());
                                lastPrices.put("bitget_" + symbol, lastPrice);
                            }
                        }
                    } catch (Exception e) {
                        log.debug("Failed to parse Bitget message for {}: {}", symbol, e.getMessage());
                    }
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn(" Bitget connection closed for {}: {} {}", symbol, code, reason);
                    connections.remove("bitget_" + symbol);
                    scheduleReconnect("bitget_" + symbol, () -> connectBitget(symbol));
                }

                @Override
                public void onError(Exception ex) {
                    log.error(" Bitget WebSocket error for {}: {}", symbol, ex.getMessage());
                }
            };

            client.connect();
            connections.put("bitget_" + symbol, client);

        } catch (Exception e) {
            log.error(" Failed to connect Bitget for {}: {}", symbol, e.getMessage());
        }
    }

    @Override
    public void connectBinance(String symbol) {
        try {
            String binanceSymbol = exchangeService.toExchangeFutureSymbol(symbol, ExchangeFuture.BINANCE_FUTURE);
            String endpoint = String.format(GetPremiumIndexWebsocket.WEBSOCKET_BINANCE_LAST_PRICE, binanceSymbol.toLowerCase());

            WebSocketClient client = new WebSocketClient(new URI(endpoint)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info(" Connected to Binance WebSocket for symbol: {}", symbol);
                    log.info(" Subscribed to Binance ticker: {}", symbol);
                }

                @Override
                public void onMessage(String message) {
                    try {
                        JsonNode json = objectMapper.readTree(message);
                        if (json != null && !json.isEmpty()) {
                            BigDecimal lastPrice = new BigDecimal(json.get("c").asText());
                            lastPrices.put("binance_" + symbol, lastPrice);

                        }

                    } catch (Exception e) {
                        log.debug("Failed to parse Binance message for {}: {}", symbol, e.getMessage());
                    }
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.warn("🔌 Binance connection closed for {}: {} {}", symbol, code, reason);
                    connections.remove("binance_" + symbol);
                    scheduleReconnect("binance_" + symbol, () -> connectBinance(symbol));
                }

                @Override
                public void onError(Exception ex) {
                    log.error(" Binance WebSocket error for {}: {}", symbol, ex.getMessage());
                }
            };

            client.connect();
            connections.put("binance_" + symbol, client);

        } catch (Exception e) {
            log.error(" Failed to connect Binance for {}: {}", symbol, e.getMessage());
        }
    }

    @Override
    public void updateContractCoinInfoWebSocket(String symbol, ContractCoinInfo coinInfo, BigDecimal indexPrice) {
        try {
            if (coinInfo == null) {
                log.warn("coinInfo is null for symbol: {}", symbol);
                return;
            }
            if (indexPrice == null) {
                log.warn("indexPrice is null for symbol: {}", symbol);
                return;
            }

            indexPrice = indexPrice.setScale(2, RoundingMode.DOWN);
            BigDecimal markPrice = indexPrice.setScale(2, RoundingMode.DOWN);
            Map<String, Object> orderBook = orderBookFutureStorage.getOrderBookMap().get(symbol);
            BigDecimal fundingRate = BigDecimal.ZERO;

            if (orderBook != null) {
                BigDecimal impactMidPrice = priceService.calculateImpactMidPrice(symbol, indexPrice, orderBook);
                if (ObjectUtils.isNotEmpty(impactMidPrice)) {
                    movingAverageService.addDataPoint(symbol, impactMidPrice, indexPrice);
                    markPrice = priceService.calculateMarkPrice(symbol, indexPrice, movingAverageService.getCurrentMovingAverage(symbol));
                }

                // Calculate Premium Index to save Funds
                BigDecimal premiumIndex = priceService.calculatePremiumIndex(orderBook, indexPrice, 18);
                if (premiumIndex != null) {
                    premiumIndexService.savePremiumIndex(symbol, premiumIndex);
                }

                // If there is PremiumIDEX → Calculate Funding Rate
                List<BigDecimal> premiums = premiumIndexService.getPremiumIndexes(symbol);
                if (ObjectUtils.isNotEmpty(premiums) && ObjectUtils.isNotEmpty(premiumIndex)) {
                    BigDecimal avgPremiumIndex = priceService.calculateAveragePremiumIndex(premiums, 8);
                    BigDecimal interestRate = priceService.calculateInterestRate(8);
                    fundingRate = priceService.calculateFundingRate(avgPremiumIndex, interestRate, premiumIndex, 8);
                }
            }

            BigDecimal oldPrice = coinInfo.getIndexPrice();

            // update giá
            coinInfo.setIndexPrice(indexPrice);
            coinInfo.setMarkPrice(markPrice);
            coinInfo.setSymbol(symbol);
            coinInfo.setFundingRate(fundingRate);

            String coinInfoKey = String.format(CommonConstants.FUTURE_CONTRACT_COIN_INFO_KEY_REDIS, symbol);
            setContractCoinInfoToRedis(coinInfoKey, coinInfo);

            setPairDetailToRedis(symbol, objectMapper.convertValue(coinInfo, new TypeReference<>() {
            }));
            log.debug("Price changed for {}: {} -> {}", symbol, oldPrice, indexPrice);

            // Send kafka to update position
            sendKafkaToUpdatePosition(symbol, coinInfo, indexPrice, markPrice);

            // Send kafka to real time wallet
            sendKafkaToRealtimeWallet(symbol);
        } catch (
                Exception e) {
            log.error("Failed to process websocket message for {}: {}", symbol, e.getMessage(), e);
        }
    }

    private void sendKafkaToRealtimeWallet(String symbol) throws JsonProcessingException {
        RealtimeWalletWhenChangeMarkPrice message = RealtimeWalletWhenChangeMarkPrice.builder()
                .symbol(symbol)
                .build();
        String payload = new ObjectMapper().writeValueAsString(message);
        log.info("[kafka-producer-liquidation-notification] Sending notification to topic {}: {}", walletRealtimeWithMarkPriceTopic, payload);
        kafkaTemplate.send(walletRealtimeWithMarkPriceTopic, symbol, payload);
    }

    private void sendKafkaToUpdatePosition(String symbol, ContractCoinInfo coinInfo,
                                           BigDecimal indexPrice, BigDecimal markPrice) throws JsonProcessingException {
        UpdatePositionWithMarkPrice message = UpdatePositionWithMarkPrice.builder()
                .symbol(symbol)
                .markPrice(markPrice)
                .indexPrice(indexPrice)
                .build();

        String payload = objectMapper.writeValueAsString(message);
        kafkaTemplate.send(positionUpdateWithMarkPriceTopic, symbol, payload);
        messagingTemplate.convertAndSend(
                "/topic/market-future/price-pair-detail/" + coinInfo.getSymbol(),
                coinInfo
        );
    }

    private void scheduleReconnect(String connectionKey, Runnable reconnectAction) {
        scheduler.schedule(() -> {
            log.info(" Attempting to reconnect: {}", connectionKey);
            try {
                reconnectAction.run();
            } catch (Exception e) {
                log.error(" Reconnection failed for {}: {}", connectionKey, e.getMessage());
                // Schedule another reconnect in 30 seconds
                scheduleReconnect(connectionKey, reconnectAction);
            }
        }, 5, TimeUnit.SECONDS);
    }

    // Method to disconnect all connections for a symbol
    public void disconnectSymbol(String symbol) {
        String[] exchanges = {"gate", "okx", "bitget", "binance"};
        for (String exchange : exchanges) {
            String key = exchange + "_" + symbol;
            WebSocketClient client = connections.remove(key);
            if (client != null && !client.isClosed()) {
                client.close();
                log.info(" Disconnected {} for symbol: {}", exchange, symbol);
            }
            // Remove price data
            lastPrices.remove(key);
        }
    }

    // Method to get current price data for a symbol
    public Map<String, BigDecimal> getPricesForSymbol(String symbol) {
        Map<String, BigDecimal> prices = new ConcurrentHashMap<>();
        String[] exchanges = {"gate", "okx", "bitget", "binance"};
        for (String exchange : exchanges) {
            String key = exchange + "_" + symbol;
            BigDecimal price = lastPrices.get(key);
            if (price != null) {
                prices.put(exchange, price);
            }
        }
        return prices;
    }

    // Cleanup method
    public void shutdown() {
        log.info(" Shutting down WebSocket connections...");
        connections.values().forEach(client -> {
            if (!client.isClosed()) {
                client.close();
            }
        });
        connections.clear();
        lastPrices.clear();
        scheduler.shutdown();
    }

    private void setContractCoinInfoToRedis(String key, ContractCoinInfo coinInfo) {
        redisEventPublisher.publishImmediate(key, coinInfo, null);
    }

    private void setPairDetailToRedis(String symbol, Map<String, Object> coinInfo) {
        redisTemplate.opsForValue().set(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS, coinInfo);
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getListEnabledSymbol() {
        Object redisValue = redisTemplate.opsForValue().get(CommonConstants.LIST_ENABLED_SYMBOL_KEY_REDIS);
        if (redisValue == null) {
            return new HashMap<>();
        }
        try {
            return (HashMap<String, String>) redisValue;
        } catch (Exception e) {
            // Log error and return empty list or handle appropriately
            log.error("Failed to parse Redis value as JSON list: " + redisValue, e);
            return new HashMap<>();
        }
    }
}
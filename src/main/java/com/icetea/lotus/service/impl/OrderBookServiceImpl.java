package com.icetea.lotus.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.component.PriceDataPoint;
import com.icetea.lotus.config.OrderBookFutureStorage;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.dto.ContractCoinInfo;
import com.icetea.lotus.job.ContractPushJob;
import com.icetea.lotus.service.*;
import com.icetea.lotus.util.OrderBookUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderBookServiceImpl implements OrderBookService {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RestTemplate restTemplate;

    private final ContractPushJob pushJob;

    private final PriceService priceService;

    private final MovingAverageService movingAverageService;
    private final ContractPushJob contractPushJob;
    private final OrderBookFutureStorage orderBookFutureStorage;

    @Value("${cex-services.matching-engine}")
    private String matchingEngine;

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public Map<String, ObjectNode> getFutureOrderBook(String symbol) {
        log.info("Fetching full future order book for symbol: {}", symbol);
        String url = "http://" + matchingEngine + "/matching-engine/monitor/future/plate-full?symbol=" + symbol;

        Map<String, ObjectNode> response;
        try {
            ResponseEntity<Map<String, Object>> matchingEngineResponse = restTemplate.exchange(url,
                    HttpMethod.GET, null, new ParameterizedTypeReference<>() {}
            );
            Map<String, Object> result = objectMapper.convertValue(matchingEngineResponse.getBody(), new TypeReference<>() {
            });
            log.info("Processing full future order book result with {} entries", result.size());
            response = OrderBookUtils.processOrderBookFromMatchingEngine(result, 100);
            Map<String, Object> orderBook = objectMapper.convertValue(
                    result.get(CommonConstants.FUTURE_MATCHING_ENGINE_ORDERBOOKSNAPSHOT),
                    Map.class
            );
            orderBookFutureStorage.getOrderBookMap()
                    .computeIfAbsent(symbol, k -> new ConcurrentHashMap<>())
                    .putAll(orderBook);
        } catch (Exception ex){
            response = new HashMap<>();
            response.put(CommonConstants.FUTURE_ORDER_BOOK_BID, OrderBookUtils.createEmptyItems());
            response.put(CommonConstants.FUTURE_ORDER_BOOK_ASK, OrderBookUtils.createEmptyItems());
        }
        return response;
    }

    @Override
    public Map<String, Object> testFutureOrderBook(String symbol, Integer action) {
        if(action == 1){
            pushJob.mockOrderBook(symbol);
        } else if (action == 2){
            clearPairDetailRedis(symbol);
        }
        log.info("testFutureOrderBook: " + symbol);
        log.info("testFutureOrderBook: " + getPairDetailRedis(symbol).toString());
        return getPairDetailRedis(symbol);
    }

    @Override
    public Queue<PriceDataPoint> getDataPoint(String symbol) {
        return movingAverageService.getDataPoint(symbol);
    }

    @Override
    public void testUpdateMarkPrice(String symbol, BigDecimal markPrice) {
        pushJob.setUpdateMarkPrice(markPrice.compareTo(BigDecimal.ZERO) == 0);
        if(markPrice.compareTo(BigDecimal.ZERO) != 0){
            Map<String, Object> pairDetails = objectMapper.convertValue(redisTemplate.opsForValue().get(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS), new TypeReference<>() {});
            if(pairDetails != null){
                pairDetails.put("markPrice", markPrice);
            } else {
                pairDetails = new HashMap<>();
            }
            redisTemplate.opsForValue().set(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS, pairDetails);
            redisTemplate.opsForValue().set(symbol + CommonConstants.SUFFIX_MARK_PRICE_KEY_REDIS, markPrice);
        }
    }

    private Map<String, Object> getPairDetailRedis(String symbol) {
        return objectMapper.convertValue(redisTemplate.opsForValue().get(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS), new TypeReference<>() {});
    }

    private void clearPairDetailRedis(String symbol) {
        redisTemplate.opsForValue().set(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS, new HashMap<>());
    }
}

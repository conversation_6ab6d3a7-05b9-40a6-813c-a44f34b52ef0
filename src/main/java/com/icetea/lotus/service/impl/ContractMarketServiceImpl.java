package com.icetea.lotus.service.impl;

import com.icetea.lotus.dto.ContractCoinInfo;
import com.icetea.lotus.service.ContractMarketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContractMarketServiceImpl implements ContractMarketService {
    @Override
    public List<ContractCoinInfo> getListContractSymbol() {
        return new ArrayList<>();
    }
}

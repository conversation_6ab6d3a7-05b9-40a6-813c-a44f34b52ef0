package com.icetea.lotus.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.service.ExchangeService;
import com.icetea.lotus.service.PriceService;
import com.icetea.lotus.util.OrderBookUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class PriceServiceImpl implements PriceService {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ExchangeService exchangeService;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public BigDecimal calculateIndexPrice(String symbol) {
        log.info("Starting index price calculation for symbol: {}", symbol);

        // Fetch prices from all exchanges sequentially
        List<BigDecimal> prices = new ArrayList<>();

        // Try to fetch from Binance
        try {
            BigDecimal binancePrice = exchangeService.fetchBinanceFuturesLastPrices(symbol);
            if (BigDecimal.ZERO.compareTo(binancePrice) < 0) {
                prices.add(binancePrice);
            }
            log.debug("Binance price fetched successfully: {}", binancePrice);
        } catch (Exception e) {
            log.warn("Failed to fetch Binance price: {}", e.getMessage());
        }

        // Try to fetch from KuCoin
        try {
            BigDecimal bitgetLastPrice = exchangeService.fetchBitgetFuturesLastPrices(symbol);
            if (BigDecimal.ZERO.compareTo(bitgetLastPrice) < 0) {
                prices.add(bitgetLastPrice);
            }
            log.debug("Bitget price fetched successfully: {}", bitgetLastPrice);
        } catch (Exception e) {
            log.warn("Failed to fetch Bitget price: {}", e.getMessage());
        }

        // Try to fetch from okx
        try {
            BigDecimal okxPrice = exchangeService.fetchOkxFuturesLastPrices(symbol);
            if (BigDecimal.ZERO.compareTo(okxPrice) < 0) {
                prices.add(okxPrice);
            }
            log.debug("Okx last price fetched successfully: {}", okxPrice);
        } catch (Exception e) {
            log.warn("Failed to fetch okx price: {}", e.getMessage());
        }

        // Try to fetch from Gate.io
        try {
            BigDecimal gateioPrice = exchangeService.fetchGateIoFuturesLastPrices(symbol);
            if (BigDecimal.ZERO.compareTo(gateioPrice) < 0) {
                prices.add(gateioPrice);
            }
            log.debug("Gate.io last price fetched successfully: {}", gateioPrice);
        } catch (Exception e) {
            log.warn("Failed to fetch Gate.io last price: {}", e.getMessage());
        }

        if (prices.isEmpty()) {
            log.info("No exchange prices could be fetched for symbol: {}", symbol);
            return BigDecimal.ZERO;
        }

        log.info("Successfully fetched prices from {} exchanges", prices.size());
        BigDecimal simpleAverage = calculateSimpleAverage(prices);

        log.info("Index price calculated - Simple Average: {}", simpleAverage);

        return simpleAverage;
    }

    @Override
    public BigDecimal calculateMarkPrice(String symbol, BigDecimal indexPrice, BigDecimal basisRate) {
        return indexPrice.multiply(basisRate.add(BigDecimal.ONE));
    }

    private BigDecimal calculateSimpleAverage(List<BigDecimal> prices) {
        BigDecimal sum = prices.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return sum.divide(BigDecimal.valueOf(prices.size()), 8, RoundingMode.HALF_UP);
    }

    private BigDecimal calculateImpactPrice(List<OrderBookUtils.PriceLevelDto> orderBook, BigDecimal targetVolume) {
        if (orderBook == null || orderBook.isEmpty()) {
            log.info("Order book is empty or null");
            return BigDecimal.ZERO;
        }

        BigDecimal remainingVolume = targetVolume;
        BigDecimal priceAtX1 = BigDecimal.ONE;
        BigDecimal cumulativeQuoteNationalAtX2 = BigDecimal.ZERO;
        BigDecimal cumulativeBaseQuantityAtX2 = BigDecimal.ZERO;
        BigDecimal cumulativeQuoteNationalAtX1 = BigDecimal.ZERO;
        BigDecimal cumulativeBaseQuantityAtX1 = BigDecimal.ZERO;
        for (OrderBookUtils.PriceLevelDto order : orderBook) {
            if (cumulativeQuoteNationalAtX1.compareTo(targetVolume) >= 0) {
                break;
            }
            BigDecimal volumeToTake = order.getVolume().multiply(order.getPrice());
            priceAtX1 = order.getPrice();
            cumulativeQuoteNationalAtX2 = cumulativeQuoteNationalAtX1;
            cumulativeBaseQuantityAtX2 = cumulativeBaseQuantityAtX1;
            cumulativeQuoteNationalAtX1 = cumulativeQuoteNationalAtX1.add(volumeToTake);
            cumulativeBaseQuantityAtX1 = cumulativeBaseQuantityAtX1.add(order.getVolume());
            remainingVolume = remainingVolume.subtract(volumeToTake);
        }

        if (remainingVolume.compareTo(BigDecimal.ZERO) > 0) {
            throw new IllegalArgumentException("Insufficient liquidity in order book for requested volume");
        }

        return targetVolume.divide(
                targetVolume
                        .subtract(cumulativeQuoteNationalAtX2)
                        .divide(priceAtX1, 18, RoundingMode.DOWN)
                        .add(cumulativeBaseQuantityAtX2), RoundingMode.DOWN
        );
    }

    @Override
    public BigDecimal calculateImpactMidPrice(String symbol, BigDecimal indexPrice, Map<String, Object> orderBook) {
        BigDecimal maxLeverage = getMaxLeverage(symbol);
        BigDecimal initialMarginRate = BigDecimal.ONE.divide(maxLeverage, 18, RoundingMode.DOWN);
        BigDecimal impactMarginNotional = CommonConstants.IMPACT_MARGIN_NOTIONAL_QUOTE_CURRENCY.multiply(initialMarginRate);
        List<OrderBookUtils.PriceLevelDto> bids = objectMapper.convertValue(orderBook.get("bids"), new TypeReference<>() {
        });
        List<OrderBookUtils.PriceLevelDto> asks = objectMapper.convertValue(orderBook.get("asks"), new TypeReference<>() {
        });

        if (ObjectUtils.isEmpty(bids) || ObjectUtils.isEmpty(asks)) {
            return null;
        }

        BigDecimal impact = calculateImpactPrice(bids, impactMarginNotional).add(calculateImpactPrice(asks, impactMarginNotional));
        return impact.divide(new BigDecimal(2), 8, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateAveragePremiumIndex(List<BigDecimal> premiumIndexes, int scale) {
        if (premiumIndexes == null || premiumIndexes.isEmpty()) {
            throw new IllegalArgumentException("Premium index list cannot be null or empty");
        }

        int size = premiumIndexes.size();
        BigDecimal numerator = BigDecimal.ZERO;
        BigDecimal denominator = BigDecimal.ZERO;

        for (int i = 0; i < size; i++) {
            int weight = i + 1;
            numerator = numerator.add(premiumIndexes.get(i).multiply(BigDecimal.valueOf(weight)));
            denominator = denominator.add(BigDecimal.valueOf(weight));
        }

        return numerator.divide(denominator, scale, RoundingMode.HALF_UP);
    }


    @Override
    public BigDecimal calculateFundingRate(BigDecimal avgPremiumIndex, BigDecimal interestRate, BigDecimal premiumIndex, int scale) {
        BigDecimal diff = interestRate.subtract(premiumIndex);
        BigDecimal clamped = clamp(diff,
                new BigDecimal("-0.0005"), // -0.05%
                new BigDecimal("0.0005")); // 0.05%
        return avgPremiumIndex.add(clamped).setScale(scale, RoundingMode.HALF_UP);
    }

    private BigDecimal clamp(BigDecimal value, BigDecimal min, BigDecimal max) {
        if (value.compareTo(min) < 0) return min;
        if (value.compareTo(max) > 0) return max;
        return value;
    }

    @Override
    public BigDecimal calculatePremiumIndex(Map<String, Object> orderBook, BigDecimal indexPrice, int scale) {
        List<OrderBookUtils.PriceLevelDto> bids = objectMapper.convertValue(orderBook.get("bids"), new TypeReference<>() {
        });
        List<OrderBookUtils.PriceLevelDto> asks = objectMapper.convertValue(orderBook.get("asks"), new TypeReference<>() {
        });
        if (ObjectUtils.isEmpty(bids) || ObjectUtils.isEmpty(asks)) {
            return null;
        }

        BigDecimal impactBidPrice = calculateImpactPrice(bids, CommonConstants.IMPACT_MARGIN_NOTIONAL_QUOTE_CURRENCY);
        BigDecimal impactAskPrice = calculateImpactPrice(asks, CommonConstants.IMPACT_MARGIN_NOTIONAL_QUOTE_CURRENCY);

        if (impactBidPrice == null || impactAskPrice == null) {
            throw new IllegalArgumentException("Impact Bid/Ask Price cannot be null");
        }

        // max(0, Impact Bid Price – Index Price)
        BigDecimal part1 = impactBidPrice.subtract(indexPrice).max(BigDecimal.ZERO);

        // max(0, Index Price – Impact Ask Price)
        BigDecimal part2 = indexPrice.subtract(impactAskPrice).max(BigDecimal.ZERO);

        // (part1 - part2) / Index Price
        return part1.subtract(part2)
                .divide(indexPrice, scale, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal calculateInterestRate(int fundingIntervalHours) {
        if (fundingIntervalHours <= 0) {
            throw new IllegalArgumentException("Funding interval hours must be greater than 0");
        }

        // Interest rate per day
        BigDecimal dailyInterestRate = new BigDecimal("0.0003"); // 0.03% = 0.0003
        // Số kỳ funding mỗi ngày
        BigDecimal fundingIntervalsPerDay = new BigDecimal(24).divide(
                new BigDecimal(fundingIntervalHours), 8, RoundingMode.HALF_UP
        );

        // Interest rate cho mỗi kỳ funding
        return dailyInterestRate.divide(fundingIntervalsPerDay, 8, RoundingMode.HALF_UP);
    }


    @SuppressWarnings("unchecked")
    private BigDecimal getMaxLeverage(String symbol) {
        Object redisValue = redisTemplate.opsForValue().get(CommonConstants.MAX_LEVERAGE_KEY_REDIS);
        if (redisValue == null) {
            return CommonConstants.DEFAULT_MAX_LEVERAGE;
        }
        try {
            Map<String, BigDecimal> maxLeverageMap = (HashMap<String, BigDecimal>) redisValue;
            return maxLeverageMap.get(symbol);
        } catch (Exception e) {
            // Log error and return empty list or handle appropriately
            log.error("Failed to parse Redis value as JSON list: " + redisValue, e);
            return CommonConstants.DEFAULT_MAX_LEVERAGE;
        }
    }

}

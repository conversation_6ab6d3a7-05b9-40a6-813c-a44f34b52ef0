package com.icetea.lotus.service.impl;

import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.service.PremiumIndexService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PremiumIndexServiceImpl implements PremiumIndexService {

    private final StringRedisTemplate stringRedisTemplate;
    private static final int WINDOW_SIZE = 480;

    @Override
    public void savePremiumIndex(String symbol, BigDecimal premiumIndex) {
        String key = String.format(CommonConstants.LIST_PREMIUM_INDEX_KEY_REDIS, symbol);
        String lastSaveKey = key + ":lastSaveMinute";

        long nowMinute = System.currentTimeMillis() / 60000;
        String lastSaveMinute = stringRedisTemplate.opsForValue().get(lastSaveKey);

        if (lastSaveMinute != null && Long.parseLong(lastSaveMinute) == nowMinute) {
            return;
        }

        stringRedisTemplate.opsForList().leftPush(key, premiumIndex.toPlainString());
        stringRedisTemplate.opsForList().trim(key, 0, WINDOW_SIZE - 1L);

        // update lastSaveMinute
        stringRedisTemplate.opsForValue().set(lastSaveKey, String.valueOf(nowMinute));
    }

    @Override
    public List<BigDecimal> getPremiumIndexes(String symbol) {
        String key = String.format(CommonConstants.LIST_PREMIUM_INDEX_KEY_REDIS, symbol);
        List<String> values = stringRedisTemplate.opsForList().range(key, -WINDOW_SIZE, -1);

        if (values == null || values.isEmpty()) {
            log.warn("No premium index data found for symbol {}", symbol);
            return Collections.emptyList();
        }

        return values.stream().map(BigDecimal::new).toList();
    }

}

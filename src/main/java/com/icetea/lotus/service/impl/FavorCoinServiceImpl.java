package com.icetea.lotus.service.impl;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.dto.CoinListResponseDto;
import com.icetea.lotus.entity.spot.FavorSymbol;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.CoinThumbService;
import com.icetea.lotus.service.FavorCoinService;
import com.icetea.lotus.service.FavorSymbolService;
import com.icetea.lotus.service.LocaleMessageSourceService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class FavorCoinServiceImpl implements FavorCoinService {
    private final Logger logger = org.slf4j.LoggerFactory.getLogger(FavorCoinServiceImpl.class);

    private final FavorSymbolService favorSymbolService;
    private final LocaleMessageSourceService msService;
    private final CoinThumbService coinThumbService;
    @Override
    public Page<CoinListResponseDto> findFavorCoin(@CurrentUser AuthMember member, int pageNo, int pageSize, String symbol, String coinBase, String sortType) {
        Page<FavorSymbol> favorSymbols = favorSymbolService.findByMemberIdByFilter(member.getId(), pageNo, pageSize, symbol, coinBase);
        List<CoinListResponseDto> result = new ArrayList<>();
        logger.info("Finding coin list with pageNo: {}, pageSize: {}, symbol: {}", pageNo, pageSize, symbol);
        for (FavorSymbol favorSymbol : favorSymbols) {
            logger.info("Processing favorite symbol: {}", favorSymbol.getSymbol());
            Page<CoinListResponseDto> coinList = coinThumbService.findCoinList(pageNo, pageSize, favorSymbol.getSymbol(), coinBase, sortType);
            if (coinList != null && !coinList.isEmpty()) {
                logger.info("Found {} coins for symbol {}", coinList.getContent().size(), favorSymbol.getSymbol());
                result.addAll(coinList.getContent());
            }
        }

        int start = (int) Math.min((long) pageNo * pageSize, result.size());
        int end = (int) Math.min((long) (pageNo + 1) * pageSize, result.size());

        List<CoinListResponseDto> pageContent = result.subList(start, end);
        logger.info("Returning {} coins for page {} with size {}", pageContent.size(), pageNo, pageSize);
        return new PageImpl<>(pageContent, PageRequest.of(pageNo, pageSize), result.size());
    }
    
    @Override
    public MessageResult addFavor(@CurrentUser AuthMember member, String symbol) {
        if (StringUtils.isEmpty(symbol)) {
            return MessageResult.error(500, msService.getMessage("SYMBOL_CANNOT_BE_EMPTY"));
        }
        FavorSymbol favorSymbol = favorSymbolService.findByMemberIdAndSymbol(member.getId(), symbol);
        if (favorSymbol != null) {
            return MessageResult.error(500, msService.getMessage("SYMBOL_ALREADY_FAVORED"));
        }
        FavorSymbol favor = favorSymbolService.add(member.getId(), symbol);
        if (favor != null) {
            return MessageResult.success(msService.getMessage("EXAPI_SUCCESS"));
        }
        return MessageResult.error(msService.getMessage("EXAPI_ERROR"));
    }
    
    @Override
    public MessageResult deleteFavor(@CurrentUser AuthMember member, String symbol) {
        if (StringUtils.isEmpty(symbol)) {
            return MessageResult.error(msService.getMessage("SYMBOL_CANNOT_BE_EMPTY"));
        }
        FavorSymbol favorSymbol = favorSymbolService.findByMemberIdAndSymbol(member.getId(), symbol);
        if (favorSymbol == null) {
            return MessageResult.error(msService.getMessage("FAVOR_NOT_EXISTS"));
        }
        favorSymbolService.delete(member.getId(), symbol);
        return MessageResult.success(msService.getMessage("EXAPI_SUCCESS"));
    }
}

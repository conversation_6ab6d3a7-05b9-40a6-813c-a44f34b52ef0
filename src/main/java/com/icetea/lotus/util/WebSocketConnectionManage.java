package com.icetea.lotus.util;

import com.icetea.lotus.client.Client;
import com.icetea.lotus.socket.ws.WebSocketHuobi;
import lombok.Getter;

public class WebSocketConnectionManage {

    private WebSocketConnectionManage() {

    }

    @Getter
    private static Client client;
    private static WebSocketHuobi ws; // Price monitoring websocket

    public static void setClient(Client client) {
        WebSocketConnectionManage.client = client;
    }

    public static WebSocketHuobi getWebSocket() {
        return ws;
    }

    public static void setWebSocket(WebSocketHuobi ws) {
        WebSocketConnectionManage.ws = ws;
    }
}

package com.icetea.lotus.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class RedisEventPublisher {

    private final ObjectMapper objectMapper;
    private final RedisTemplate<String, String> redisTemplate;


    /**
     * Publishes immediately without waiting for transaction commit.
     *
     * @param key   Redis key
     * @param value Object to serialize and store
     * @param ttl   Time to live for the key
     */
    public void publishImmediate(String key, Object value, Duration ttl) {
        validateParameters(key, value);
        publishToRedis(key, value, ttl);
    }

    private void publishToRedis(String key, Object value, Duration ttl) {
        try {
            String jsonValue = objectMapper.writeValueAsString(value);
            if (ObjectUtils.isEmpty(ttl)) {
                redisTemplate.opsForValue().set(key, jsonValue);
                return;
            }

            redisTemplate.opsForValue().set(key, jsonValue, ttl);
        } catch (RedisConnectionFailureException e) {
            log.error("Redis connection failed while publishing key: {}", key, e);
            throw new RedisPublishingException("Redis connection failed", e);

        } catch (Exception e) {
            log.error("Unexpected error while publishing to Redis for key: {}", key, e);
            throw new RedisPublishingException("Redis publishing failed", e);
        }
    }

    private void validateParameters(String key, Object value) {
        if (!StringUtils.hasText(key)) {
            throw new IllegalArgumentException("Redis key must not be null or empty");
        }
        if (value == null) {
            throw new IllegalArgumentException("Value must not be null");
        }
    }


    /**
     * Gets a string value directly from Redis without JSON deserialization.
     *
     * @param key Redis key
     * @return Optional containing the string value, or empty if key doesn't exist
     */
    public Optional<String> getStringValue(String key) {
        validateKey(key);

        try {
            String value = redisTemplate.opsForValue().get(key);

            if (value == null) {
                log.debug("No string value found in Redis for key: {}", key);
                return Optional.empty();
            }

            log.debug("Successfully retrieved string value from Redis - Key: {}", key);
            return Optional.of(value);

        } catch (RedisConnectionFailureException e) {
            log.error("Redis connection failed while retrieving string for key: {}", key, e);
            throw new RedisRetrievalException("Redis connection failed", e);

        } catch (Exception e) {
            log.error("Unexpected error while retrieving string from Redis for key: {}", key, e);
            throw new RedisRetrievalException("Redis retrieval failed", e);
        }
    }


    /**
     * Deletes a key from Redis.
     *
     * @param key Redis key
     * @return true if key was deleted, false if key didn't exist
     */
    public boolean deleteKey(String key) {
        validateKey(key);

        try {
            Boolean deleted = redisTemplate.delete(key);
            boolean result = Boolean.TRUE.equals(deleted);

            log.debug("Key deletion for '{}': {}", key, result ? "success" : "key not found");
            return result;

        } catch (RedisConnectionFailureException e) {
            log.error("Redis connection failed while deleting key: {}", key, e);
            throw new RedisRetrievalException("Redis connection failed", e);

        } catch (Exception e) {
            log.error("Unexpected error while deleting key: {}", key, e);
            throw new RedisRetrievalException("Redis key deletion failed", e);
        }
    }

    private void validateKey(String key) {
        if (!StringUtils.hasText(key)) {
            throw new IllegalArgumentException("Redis key must not be null or empty");
        }
    }

    /**
     * Custom exception for Redis retrieval failures
     */
    public static class RedisRetrievalException extends RuntimeException {
        public RedisRetrievalException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * Custom exception for Redis publishing failures
     */
    public static class RedisPublishingException extends RuntimeException {
        public RedisPublishingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
package com.icetea.lotus.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BigDecimalUtils {

    private BigDecimalUtils() {

    }

    /**
     * Default division operation accuracy
     */
    private static final int DEFAULT_DIV_SCALE = 8;

    /**
     * Initializes the string constructor of BigDecimal.
     *
     * @param v double value
     * @return BigDecimal Object
     */
    private static BigDecimal createBigDecimal(double v) {
        return new BigDecimal(Double.toString(v));
    }

    /**
     * Provides accurate addition operations.
     *
     * @param v1 Being added
     * @param v2 Addition
     * @return The sum of two parameters
     */
    public static BigDecimal add(BigDecimal v1, BigDecimal v2) {
        return v1.add(v2);
    }

    /**
     * Provides accurate addition operations.
     *
     * @param v1 Being added
     * @param v2 Addition
     * @return The sum of two parameters
     */
    public static BigDecimal add(double v1, double v2) {
        BigDecimal b1 = createBigDecimal(v1);
        BigDecimal b2 = createBigDecimal(v2);
        return b1.add(b2);
    }

    /**
     * Provides accurate addition operations.
     *
     * @param v1 Being added
     * @param v2 Addition
     * @return The sum of two parameters
     */
    public static BigDecimal add(BigDecimal v1, double v2) {
        BigDecimal b2 = createBigDecimal(v2);
        return v1.add(b2);
    }

    /**
     * Provides accurate subtraction operations.
     *
     * @param v1 Subtracted
     * @param v2 Subtraction
     * @return The difference between two parameters
     */
    public static BigDecimal sub(double v1, double v2) {
        BigDecimal b1 = createBigDecimal(v1);
        BigDecimal b2 = createBigDecimal(v2);
        return b1.subtract(b2);
    }

    /**
     * Provides accurate subtraction operations.
     *
     * @param v1 Subtracted
     * @param v2 Subtraction
     * @return The difference between two parameters
     */
    public static BigDecimal sub(BigDecimal v1, double v2) {
        BigDecimal b2 = createBigDecimal(v2);
        return v1.subtract(b2);
    }

    /**
     * Provides accurate subtraction operations.
     *
     * @param v1 Subtracted
     * @param v2 Subtraction
     * @return The difference between two parameters
     */
    public static BigDecimal sub(BigDecimal v1, BigDecimal v2) {
        return v1.subtract(v2);
    }

    /**
     * Provides accurate decimal rounding.
     *
     * @param v     Counts that need to be rounded
     * @param scale How many digits are retained after the decimal point
     * @return Rounding results
     */
    public static BigDecimal round(double v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b = createBigDecimal(v);
        return b.divide(BigDecimal.ONE, scale, RoundingMode.HALF_UP);
    }

    /**
     * Provides accurate decimal rounding.
     *
     * @param v     Counts that need to be rounded
     * @param scale How many digits are retained after the decimal point
     * @return Rounding results
     */
    public static BigDecimal round(BigDecimal v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        return v.divide(BigDecimal.ONE, scale, RoundingMode.HALF_UP);
    }

    /**
     * Provides accurate multiplication operations.
     *
     * @param v1 Being multiplied
     * @param v2 multiplier
     * @return The product of two parameters
     */
    public static BigDecimal mul(double v1, double v2) {
        BigDecimal b1 = createBigDecimal(v1);
        BigDecimal b2 = createBigDecimal(v2);
        return b1.multiply(b2);
    }

    /**
     * Provides accurate multiplication operations.
     *
     * @param v1 Being multiplied
     * @param v2 multiplier
     * @return The product of two parameters
     */
    public static BigDecimal mul(BigDecimal v1, double v2) {
        BigDecimal b2 = createBigDecimal(v2);
        return v1.multiply(b2);
    }

    /**
     * Provides accurate multiplication operations.
     *
     * @param v1 Being multiplied
     * @param v2 multiplier
     * @return The product of two parameters
     */
    public static BigDecimal mul(BigDecimal v1, BigDecimal v2) {
        return v1.multiply(v2);
    }

    public static BigDecimal mulDown(BigDecimal v1, BigDecimal v2, int x) {
        return v1.multiply(v2).setScale(x, RoundingMode.DOWN);
    }

    /**
     * Provides relatively accurate multiplication operations, rounding and retaining eight decimal places.
     *
     * @param v1 Being multiplied
     * @param v2 multiplier
     * @return The product of two parameters
     */
    public static BigDecimal mulRound(BigDecimal v1, BigDecimal v2) {
        return mulRound(v1, v2, DEFAULT_DIV_SCALE);
    }

    /**
     * Provides relatively accurate multiplication operations, rounding and retaining v3 decimals.
     *
     * @param v1 Being multiplied
     * @param v2 multiplier
     * @param v3 Number of retained digits
     * @return The product of two parameters
     */
    public static BigDecimal mulRound(BigDecimal v1, BigDecimal v2, int v3) {
        return round(v1.multiply(v2), v3);
    }

    /**
     * Provides (relatively) accurate division operations.When there is no end to complete the separation, the accuracy is specified by the scale parameter, and the subsequent numbers are rounded.
     *
     * @param v1    Divided
     * @param v2    divisor
     * @param scale The representation needs to be accurate to several digits after the decimal point.
     * @return Two parameters quotient
     */
    public static BigDecimal div(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b1 = createBigDecimal(v1);
        BigDecimal b2 = createBigDecimal(v2);
        return b1.divide(b2, scale, RoundingMode.HALF_UP);
    }

    /**
     * Provides (relatively) accurate division operations.When there is no end to complete the separation, the accuracy is specified by the scale parameter, and the subsequent numbers are rounded.
     *
     * @param v1    Divided
     * @param v2    divisor
     * @param scale The representation needs to be accurate to several digits after the decimal point.
     * @return Two parameters quotient
     */
    public static BigDecimal div(BigDecimal v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b2 = createBigDecimal(v2);
        return v1.divide(b2, scale, RoundingMode.HALF_UP);
    }

    /**
     * Provides (relatively) accurate division operations.When there is no end to complete the separation, the accuracy is specified by the scale parameter, and the subsequent numbers are rounded.
     *
     * @param v1    Divided
     * @param v2    divisor
     * @param scale The representation needs to be accurate to several digits after the decimal point.
     * @return Two parameters quotient
     */
    public static BigDecimal div(BigDecimal v1, BigDecimal v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        return v1.divide(v2, scale, RoundingMode.HALF_UP);
    }

    /**
     * Provides (relatively) accurate division operations.When there is no end to complete the situation, eight digits are retained by default, and subsequent numbers are rounded.
     *
     * @param v1 Divided
     * @param v2 divisor
     * @return Two parameters quotient
     */
    public static BigDecimal div(BigDecimal v1, BigDecimal v2) {
        return v1.divide(v2, DEFAULT_DIV_SCALE, RoundingMode.HALF_UP);
    }

    public static BigDecimal divDown(BigDecimal v1, BigDecimal v2) {
        return v1.divide(v2, DEFAULT_DIV_SCALE, RoundingMode.DOWN);
    }

    /**
     * Get interest rates
     *
     * @param v1
     * @return
     */
    public static BigDecimal getRate(BigDecimal v1) {
        BigDecimal hundred = new BigDecimal("100");
        return div(v1, hundred);
    }

    /**
     * Get multiples
     *
     * @param v1
     * @return
     */
    public static BigDecimal rate(BigDecimal v1) {
        return add(getRate(v1), BigDecimal.ONE);
    }

    /**
     * Compare size, v1>=v2 returns true, otherwise return false
     *
     * @param v1
     * @param v2
     * @return
     */
    public static boolean compare(BigDecimal v1, BigDecimal v2) {
        return v1.compareTo(v2) >= 0;
    }

    /**
     * Compare size, (v1+v2)>=v2 returns true, otherwise return false
     *
     * @param v1
     * @param v2
     * @param v3
     * @return
     */
    public static boolean compare(BigDecimal v1, BigDecimal v2, BigDecimal v3) {
        return compare(add(v1, v2), v3);
    }

    /**
     * Determine whether the two values are equal
     *
     * @param v1
     * @param v2
     * @return
     */
    public static boolean isEqual(BigDecimal v1, BigDecimal v2) {
        return v1.compareTo(v2) == 0;
    }
}

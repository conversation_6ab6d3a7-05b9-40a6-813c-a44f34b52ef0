package com.icetea.lotus.util;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.List;

public class PaginationUtils {

    private PaginationUtils() {

    }

    public static <T> Page<T> toPage(List<T> list, Pageable pageable) {
        int pageSize = pageable.getPageSize();
        int currentPage = pageable.getPageNumber();
        int startItem = currentPage * pageSize;

        List<T> subList;

        if (startItem >= list.size()) {
            subList = List.of(); // Tr<PERSON> về danh sách rỗng nếu page vượt quá size
        } else {
            int toIndex = Math.min(startItem + pageSize, list.size());
            subList = list.subList(startItem, toIndex);
        }

        return new PageImpl<>(subList, pageable, list.size());
    }
}

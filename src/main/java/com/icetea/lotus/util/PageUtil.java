package com.icetea.lotus.util;

import com.icetea.lotus.pagenation.EntityPage;
import org.springframework.data.domain.Page;

@SuppressWarnings("all")
public class PageUtil {

    public static EntityPage page(Page page, int pageNo, int pageSize) {
        EntityPage entityPage = new EntityPage();
        entityPage.setCount(page.getTotalElements());
        entityPage.setTotalPage(page.getTotalPages());
        entityPage.setList(page.getContent());
        entityPage.setCurrentPage(pageNo);
        entityPage.setPageSize(pageSize);
        return entityPage;
    }
}

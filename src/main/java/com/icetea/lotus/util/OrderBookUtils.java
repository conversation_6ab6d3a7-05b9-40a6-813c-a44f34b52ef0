package com.icetea.lotus.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.constants.CommonConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrderBookUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private OrderBookUtils() {
    }

    public static Map<String, ObjectNode> processOrderBookFromMatchingEngine(Map<String, Object> result) {
        Map<String, Object> map = getOrderBookSnapshot(result);
        Map<String, ObjectNode> response = new HashMap<>();
        response.put(CommonConstants.FUTURE_ORDER_BOOK_BID, convertToResponse(map, CommonConstants.FUTURE_MATCHING_ENGINE_BID));
        response.put(CommonConstants.FUTURE_ORDER_BOOK_ASK, convertToResponse(map, CommonConstants.FUTURE_MATCHING_ENGINE_ASK));
        return response;
    }

    public static Map<String, ObjectNode> processOrderBookFromMatchingEngine(Map<String, Object> result, int limit) {
        Map<String, Object> map = getOrderBookSnapshot(result);
        Map<String, ObjectNode> response = new HashMap<>();
        response.put(CommonConstants.FUTURE_ORDER_BOOK_BID, convertToResponse(map, CommonConstants.FUTURE_MATCHING_ENGINE_BID, limit));
        response.put(CommonConstants.FUTURE_ORDER_BOOK_ASK, convertToResponse(map, CommonConstants.FUTURE_MATCHING_ENGINE_ASK, limit));
        return response;
    }

    public static ObjectNode convertToResponse(Map<String, Object> map, String keyInMap) {
        return objectMapper.createObjectNode().set(CommonConstants.FUTURE_ORDER_BOOK_ITEM, objectMapper.valueToTree(map.get(keyInMap)));
    }

    public static ObjectNode createEmptyItems() {
        return objectMapper.createObjectNode().set(CommonConstants.FUTURE_ORDER_BOOK_ITEM, objectMapper.createArrayNode());
    }

    public static ObjectNode convertToResponse(Map<String, Object> map, String keyInMap, int limit) {
        List<PriceLevelDto> items = objectMapper.convertValue(map.get(keyInMap), new TypeReference<>() {
        });
        items.sort(Comparator.comparing(PriceLevelDto::getPrice).reversed());
        return objectMapper.createObjectNode().set(CommonConstants.FUTURE_ORDER_BOOK_ITEM, objectMapper.valueToTree(items.size() > limit ? items.subList(1, 100) : items));
    }

    private static Map<String, Object> getOrderBookSnapshot(Map<String, Object> result) {
        return objectMapper.convertValue(result.get(CommonConstants.FUTURE_MATCHING_ENGINE_ORDERBOOKSNAPSHOT), Map.class);
    }

    public static Map<String, ObjectNode> processOrderBook(Map<String, Object> result, int limit) {
        Map<String, ObjectNode> response = new HashMap<>();
        response.put(CommonConstants.FUTURE_ORDER_BOOK_BID, convertToResponse(result, CommonConstants.FUTURE_MATCHING_ENGINE_BID, limit));
        response.put(CommonConstants.FUTURE_ORDER_BOOK_ASK, convertToResponse(result, CommonConstants.FUTURE_MATCHING_ENGINE_ASK, limit));
        return response;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PriceLevelDto implements Serializable {
        /**
         * Giá
         */
        private BigDecimal price;

        /**
         * Khối lượng (volume thay vì amount để tương thích với future-core)
         */
        private BigDecimal volume;

        /**
         * Số lượng lệnh
         */
        private Integer orderCount;
    }
}

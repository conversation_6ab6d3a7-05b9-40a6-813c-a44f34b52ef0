package com.icetea.lotus.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JSONUtils {

    private JSONUtils() {

    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static boolean isJsonObject(String content) {
        if ("".equals(content) || null == content) {
            return false;
        }
        try {
            JsonNode jsonNode = objectMapper.readTree(content);
            return jsonNode.isObject();
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isJsonArray(String content) {
        if ("".equals(content) || null == content) {
            return false;
        }
        try {
            JsonNode jsonNode = objectMapper.readTree(content);
            return jsonNode.isArray();
        } catch (Exception e) {
            return false;
        }
    }
}

package com.icetea.lotus.component;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PriceDataPoint {
    private LocalDateTime timestamp;
    private BigDecimal impactMidPrice;
    private BigDecimal indexPrice;
    private BigDecimal normalizedDifference; // ((Impact Mid - Index) / Index)
    
    public PriceDataPoint() {}
    
    public PriceDataPoint(LocalDateTime timestamp, BigDecimal impactMidPrice, BigDecimal indexPrice) {
        this.timestamp = timestamp;
        this.impactMidPrice = impactMidPrice;
        this.indexPrice = indexPrice;
        this.normalizedDifference = calculateNormalizedDifference(impactMidPrice, indexPrice);
    }
    
    private BigDecimal calculateNormalizedDifference(BigDecimal impactMid, BigDecimal index) {
        if (index.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("Index price cannot be zero");
        }
        return impactMid.subtract(index).divide(index, 8, java.math.RoundingMode.HALF_UP);
    }
}
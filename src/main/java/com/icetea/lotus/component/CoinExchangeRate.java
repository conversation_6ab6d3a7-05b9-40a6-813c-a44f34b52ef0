package com.icetea.lotus.component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.ExchangeCoinService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Currency Exchange Rate Management
 */
@Component
@Slf4j
@ToString
@RequiredArgsConstructor
public class CoinExchangeRate {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CoinService coinService;
    private final ExchangeCoinService exCoinService;
    @Getter
    @Setter
    private BigDecimal usdCnyRate = new BigDecimal("6.45");
    @Getter
    @Setter
    private BigDecimal usdtCnyRate = new BigDecimal("6.42");
    @Getter
    @Setter
    private BigDecimal usdJpyRate = new BigDecimal("110.02");
    @Getter
    @Setter
    private BigDecimal usdHkdRate = new BigDecimal("7.8491");
    @Getter
    @Setter
    private BigDecimal sgdCnyRate = new BigDecimal("5.08");
    @Setter
    private CoinProcessorFactory coinProcessorFactory;


    private static final Map<String, BigDecimal> ratesMap = Map.of(
            CommonConstants.RatesMap.CNY, CommonConstants.RatesMap.CNY_VALUE,
            CommonConstants.RatesMap.JPY, CommonConstants.RatesMap.JPY_VALUE,
            CommonConstants.RatesMap.TWD, CommonConstants.RatesMap.TWD_VALUE,
            CommonConstants.RatesMap.USD, CommonConstants.RatesMap.USD_VALUE,
            CommonConstants.RatesMap.EUR, CommonConstants.RatesMap.EUR_VALUE,
            CommonConstants.RatesMap.HKD, CommonConstants.RatesMap.HKD_VALUE,
            CommonConstants.RatesMap.SGD, CommonConstants.RatesMap.SGD_VALUE,
            CommonConstants.RatesMap.INR, CommonConstants.RatesMap.INR_VALUE
    );

    public BigDecimal getUsdRate(String symbol) {
        log.info("CoinExchangeRate getUsdRate unit = {}", symbol);
        String upperSymbol = symbol.toUpperCase();

        switch (upperSymbol) {
            case "USDT":
                log.info("CoinExchangeRate getUsdRate unit = USDT, result = ONE");
                return BigDecimal.ONE;
            case "CNY":
                log.info("CoinExchangeRate getUsdRate unit = CNY, result : 1 divide {}", usdtCnyRate);
                return divideRate(usdtCnyRate);
            case "BITCNY", "ET":
                return divideRate(usdCnyRate);
            case "JPY":
                return divideRate(usdJpyRate);
            case "HKD":
                return divideRate(usdHkdRate);
            default:
                return getRateFromProcessor(upperSymbol);
        }
    }

    private BigDecimal divideRate(BigDecimal rate) {
        return BigDecimal.ONE.divide(rate, 4, RoundingMode.DOWN).setScale(4, RoundingMode.DOWN);
    }

    private BigDecimal getRateFromProcessor(String symbol) {
        if (coinProcessorFactory == null) {
            return getDefaultUsdRate(symbol);
        }

        List<String> suffixes = Arrays.asList("USDT", "BTC", "ETH");

        for (String suffix : suffixes) {
            String symbolWithSuffix = symbol + "/" + suffix;
            if (coinProcessorFactory.hasProcessor(symbolWithSuffix)) {
                log.info("Support exchange coin = {}", symbolWithSuffix);
                CoinProcessor processor = coinProcessorFactory.getProcessor(symbolWithSuffix);
                if (processor == null) return BigDecimal.ZERO;

                CoinThumb thumb = processor.getThumb();
                if (thumb == null) {
                    log.info("Support exchange coin thumb is null for {}", symbolWithSuffix);
                    return BigDecimal.ZERO;
                }

                return thumb.getUsdRate();
            }
        }

        return getDefaultUsdRate(symbol);
    }

    public BigDecimal getDefaultUsdRate(String symbol) {
        Coin coin = coinService.findByUnit(symbol);
        if (coin != null) {
            return BigDecimal.valueOf(coin.getUsdRate());
        } else {
            return BigDecimal.ZERO;
        }
    }

    public BigDecimal getCnyRate(String symbol) {
        if ("CNY".equalsIgnoreCase(symbol) || "ET".equalsIgnoreCase(symbol)) {
            return BigDecimal.ONE;
        }

        return getUsdRate(symbol).multiply(usdtCnyRate).setScale(2, RoundingMode.DOWN);
    }

    public BigDecimal getJpyRate(String symbol) {
        if ("JPY".equalsIgnoreCase(symbol)) {
            return BigDecimal.ONE;
        }
        return getUsdRate(symbol).multiply(usdJpyRate).setScale(2, RoundingMode.DOWN);
    }

    public BigDecimal getHkdRate(String symbol) {
        if ("HKD".equalsIgnoreCase(symbol)) {
            return BigDecimal.ONE;
        }
        return getUsdRate(symbol).multiply(usdHkdRate).setScale(2, RoundingMode.DOWN);
    }

    @Scheduled(cron = "0 */5 * * * *")
    public void syncUsdtCnyPrice() {
        log.info("Start synchronizing OTC");

        ObjectNode jsonObject = objectMapper.createObjectNode();
        List<String> coins = new ArrayList<>();
        coins.add("USDT");
        jsonObject.putPOJO("assets", coins);

        jsonObject.put("tradeType", "BUY");
        jsonObject.put("fromUserRole", "USER");

        Set<String> currencies = ratesMap.keySet();
        for (String currency : currencies) {
            jsonObject.put("fiatCurrency", currency);
            // binance
            String urlOk = "https://p2p.binance.com/bapi/c2c/v2/public/c2c/adv/quoted-price";
            try {
                String jsonBody = objectMapper.writeValueAsString(jsonObject);
                HttpResponse<com.mashape.unirest.http.JsonNode> resp = Unirest.post(urlOk)
                        .header("accept", "application/json")
                        .header("content-type", "application/json")
                        .body(jsonBody)
                        .asJson();

                handleResponseFromUnirest(currency, resp);
            } catch (UnirestException | JsonProcessingException e) {
                log.info("Start syncing OTC errors");
                log.error(e.toString());
            }
        }
    }

    private void handleResponseFromUnirest(String currency, HttpResponse<com.mashape.unirest.http.JsonNode> resp) throws JsonProcessingException {
        if (resp.getStatus() == 200) { // Return correctly
            JsonNode ret = objectMapper.readTree(resp.getBody().toString());
            if ("000000".equals(ret.path("code").asText())) {
                JsonNode data = ret.path("data");
                if (data != null && data.isArray() && !data.isEmpty()) {
                    JsonNode res = data.get(0);
                    BigDecimal referencePrice = new BigDecimal(res.path("referencePrice").asText());
                    ratesMap.put(currency, referencePrice);
                    if (CommonConstants.RatesMap.CNY.equals(currency)) {
                        usdCnyRate = referencePrice;
                        usdtCnyRate = referencePrice;
                    } else if (CommonConstants.RatesMap.JPY.equals(currency)) {
                        usdJpyRate = referencePrice;
                    } else if (CommonConstants.RatesMap.HKD.equals(currency)) {
                        usdHkdRate = referencePrice;
                    } else if (CommonConstants.RatesMap.SGD.equals(currency)) {
                        sgdCnyRate = referencePrice;
                    }
                }
            }
        }
    }

    @Scheduled(cron = "0 */30 * * * *")
    public void syncPrice() throws UnirestException {

        String url = "http://web.juhe.cn:8080/finance/exchange/frate?key=0330f6e51631ee1c0c4696a49201cb94";
        // If there is any error, please apply for the exchange rate on your own website or write it to death by default.
        HttpResponse<com.mashape.unirest.http.JsonNode> resp = Unirest.get(url)
                .queryString("key", "0330f6e51631ee1c0c4696a49201cb94")
                .asJson();
        log.info("forex result:{}", resp.getBody());

        try {
            JsonNode ret = objectMapper.readTree(resp.getBody().toString());

            if (ret.path("resultcode").asInt() == 200) {
                JsonNode result = ret.path("result");
                if (result.isArray()) {
                    for (JsonNode json : result) {
                        String code = json.path("code").asText();
                        if ("USDCNY".equals(code)) {
                            setUsdCnyRate(BigDecimal.valueOf(json.path(CommonConstants.PRICE).asDouble()).setScale(2, RoundingMode.DOWN));
                            log.info(json.toString());
                        } else if ("USDJPY".equals(code)) {
                            setUsdJpyRate(BigDecimal.valueOf(json.path(CommonConstants.PRICE).asDouble()).setScale(2, RoundingMode.DOWN));
                            log.info(json.toString());
                        } else if ("USDHKD".equals(code)) {
                            setUsdHkdRate(BigDecimal.valueOf(json.path(CommonConstants.PRICE).asDouble()).setScale(2, RoundingMode.DOWN));
                            log.info(json.toString());
                        }
                    }
                }
            }
        } catch (JsonProcessingException e) {
            log.error("Error parsing forex response: {}", e.getMessage());
        }
    }

    public Map<String, BigDecimal> getAllRate(String symbol) {
        Map<String, BigDecimal> result = new HashMap<>();
        for (Map.Entry<String, BigDecimal> entry : ratesMap.entrySet()) {
            String currency = entry.getKey();
            BigDecimal usdtRate = entry.getValue();

            if ("CNY".equalsIgnoreCase(symbol) || "ET".equalsIgnoreCase(symbol)) {
                result.put(currency, BigDecimal.ONE);
                continue;
            }

            BigDecimal rate = getUsdRate(symbol).multiply(usdtRate).setScale(2, RoundingMode.DOWN);
            result.put(currency, rate);
        }

        return result;
    }
}

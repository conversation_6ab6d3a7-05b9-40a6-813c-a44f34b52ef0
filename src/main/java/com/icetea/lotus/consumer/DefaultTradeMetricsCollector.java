package com.icetea.lotus.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Default implementation of TradeMetricsCollector
 * Collects and reports metrics for trade processing
 */
@Slf4j
@Component
public class DefaultTradeMetricsCollector implements TradeMetricsCollector {

    private final ExecutorService executor;

    // Monitoring metrics
    private final AtomicLong totalTradesProcessed = new AtomicLong(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong lastProcessingTime = new AtomicLong(0);

    public DefaultTradeMetricsCollector(ExecutorService executor) {
        this.executor = executor;
    }

    @Override
    public void updateProcessingMetrics(int tradeCount, long processingTime) {
        totalTradesProcessed.addAndGet(tradeCount);
        totalProcessingTime.addAndGet(processingTime);
        lastProcessingTime.set(processingTime);
    }

    @Override
    public String getProcessingStats() {
        ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
        double avgProcessingTime = totalTradesProcessed.get() > 0 ? (double) totalProcessingTime.get() / totalTradesProcessed.get() : 0;

        return String.format(
                "TradeProcessing Stats - Total Trades: %d, Avg Processing Time: %.2fms, " +
                        "Last Processing Time: %dms, Thread Pool - Active: %d/%d, Queue Size: %d/%d, Completed Tasks: %d",
                totalTradesProcessed, avgProcessingTime, lastProcessingTime,
                tpe.getActiveCount(), tpe.getPoolSize(), tpe.getQueue().size(), tpe.getMaximumPoolSize(),
                tpe.getCompletedTaskCount()
        );
    }

    @Override
    public boolean isHealthy() {
        ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
        // Consider healthy if:
        // 1. Thread pool is not shut down
        // 2. Queue is not completely full
        // 3. There are available threads or queue space
        return !tpe.isShutdown()
                && tpe.getQueue().size() < tpe.getQueue().remainingCapacity() + tpe.getQueue().size() * 0.9
                && (tpe.getActiveCount() < tpe.getMaximumPoolSize() || tpe.getQueue().remainingCapacity() > 0);
    }

    /**
     * Log processing statistics periodically for monitoring
     * This method can be scheduled to run at regular intervals
     */
    public void logProcessingStats() {
        if (totalTradesProcessed.get() > 0) {
            log.info("Trade Processing Statistics: {}", getProcessingStats());
        }
    }
}
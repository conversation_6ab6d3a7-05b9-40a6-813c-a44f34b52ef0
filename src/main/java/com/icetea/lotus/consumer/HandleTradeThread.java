package com.icetea.lotus.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.icetea.lotus.entity.ExchangeOrderStatus;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.spot.ExchangeOrder;
import com.icetea.lotus.exception.BusinessException;
import com.icetea.lotus.job.ExchangePushJob;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.ExchangeOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Thread for handling trade records from Kafka
 * This class processes trade data, updates K-lines, and pushes notifications
 */
@Slf4j
@RequiredArgsConstructor
public class HandleTradeThread implements Runnable {
    private final ConsumerRecord<String, String> recordValue;
    private final ObjectMapper objectMapper;
    private final CoinProcessorFactory coinProcessorFactory;
    private final ExchangeOrderService exchangeOrderService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ExchangePushJob pushJob;
    private final boolean secondReferrerAward;
    private final TradeMetricsCollector metricsCollector;
    private final ReentrantLock tradesLock = new ReentrantLock();

    @Override
    public void run() {
        long startTime = System.currentTimeMillis();
        String symbol = null;
        int tradeCount = 0;

        try {
            // Parse trades from Kafka record - handle both single object and array formats
            List<ExchangeTrade> trades;

            trades = getExchangeTrades();

            if (trades == null || trades.isEmpty()) {
                log.warn("Received empty or null trades list from Kafka record: {}", recordValue.key());
                return;
            }

            symbol = trades.get(0).getSymbol();
            tradeCount = trades.size();

            log.debug("Processing {} trades for symbol: {} from Kafka partition: {}, offset: {}",
                    tradeCount, symbol, recordValue.partition(), recordValue.offset());

            // Get coin processor for the symbol
            CoinProcessor coinProcessor = coinProcessorFactory.getProcessor(symbol);
            if (coinProcessor == null) {
                log.error("No CoinProcessor found for symbol: {}. K-line processing will be skipped. " +
                        "This indicates a configuration issue - processor should be initialized for all active symbols.", symbol);
                // Continue with order processing even if K-line processing fails
            }

            // Process each trade for order management
            for (ExchangeTrade trade : trades) {
                processExchangeTrade(trade, symbol);
            }

            // Process K-line market data (Event-Driven Architecture)
            if (coinProcessor != null) {
                processTrade(tradeCount, symbol, coinProcessor, trades);
            } else {
                log.warn("Skipping K-line processing for symbol: {} due to missing CoinProcessor", symbol);
            }

            // Add trades to push job for market data distribution
            pushJobTradeForSymbol(symbol, trades, tradeCount);

            long processingTime = System.currentTimeMillis() - startTime;
            log.debug("Completed trade processing for symbol: {} with {} trades in {}ms",
                    symbol, tradeCount, processingTime);

            // Update monitoring metrics
            metricsCollector.updateProcessingMetrics(tradeCount, processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            log.error("Critical error in trade processing thread for symbol: {}, trades: {}, " +
                            "processingTime: {}ms, record: {}, error: {}",
                    symbol, tradeCount, processingTime, recordValue.key(), e.getMessage(), e);

            // Re-throw critical errors to trigger Kafka retry mechanism
            throw new BusinessException("Failed to process trade batch for symbol: " + symbol);
        }
    }

    private void processExchangeTrade(ExchangeTrade trade, String symbol) {
        try {
            // Process exchange trade details
            exchangeOrderService.processExchangeTrade(trade, secondReferrerAward);

            // Retrieve and push order transaction subscriptions
            ExchangeOrder buyOrder = exchangeOrderService.findOne(trade.getBuyOrderId());
            ExchangeOrder sellOrder = exchangeOrderService.findOne(trade.getSellOrderId());

            // Handle partially filled orders if indicated by trade
            if (trade.getIsPartiallyFilled() != null && trade.getIsPartiallyFilled()) {
                handlePartiallyFilledOrdersFromTrade(trade, buyOrder, sellOrder, exchangeOrderService);

                // Refresh order states after partial fill processing
                buyOrder = exchangeOrderService.findOne(trade.getBuyOrderId());
                sellOrder = exchangeOrderService.findOne(trade.getSellOrderId());
            }

            // FIXED: Send notifications AFTER all processing is complete with final order states
            sendOrderNotifications(buyOrder, sellOrder, symbol);
        } catch (Exception e) {
            log.error("Error processing individual trade for symbol: {}, buyOrderId: {}, sellOrderId: {}, error: {}",
                    symbol, trade.getBuyOrderId(), trade.getSellOrderId(), e.getMessage(), e);
            // Continue processing other trades even if one fails
        }
    }

    private static void processTrade(int tradeCount, String symbol, CoinProcessor coinProcessor, List<ExchangeTrade> trades) {
        try {
            log.debug("Processing {} trades for K-line updates in symbol: {}", tradeCount, symbol);
            coinProcessor.process(trades);
            log.debug("Successfully processed K-line updates for symbol: {} with {} trades", symbol, tradeCount);
        } catch (Exception e) {
            log.error("Critical error processing K-line updates for symbol: {}, trades: {}, error: {}",
                    symbol, tradeCount, e.getMessage(), e);
            // K-line processing failure shouldn't stop other processing
        }
    }

    @SuppressWarnings("java:S125")
    private void pushJobTradeForSymbol(String symbol, List<ExchangeTrade> trades, int tradeCount) {
        try {
//            pushJob.addTrades(symbol, trades);
            if (CollectionUtils.isNotEmpty(trades)) {
                tradesLock.lock();
                try {
                    // Original topic format
                    messagingTemplate.convertAndSend("/topic/market/trade/" + symbol, new ArrayList<>(trades));

                    trades.clear();
                } finally {
                    tradesLock.unlock();
                }
            }
            log.debug("Added {} trades to push job for symbol: {}", tradeCount, symbol);
        } catch (Exception e) {
            log.error("Error adding trades to push job for symbol: {}, error: {}", symbol, e.getMessage(), e);
        }
    }

    private List<ExchangeTrade> getExchangeTrades() throws JsonProcessingException {
        List<ExchangeTrade> trades;
        try {
            // First try to parse as array
            trades = objectMapper.readValue(recordValue.value(), new TypeReference<>() {
            });
        } catch (MismatchedInputException e) {
            // If array parsing fails, try parsing as single object and wrap in list
            log.debug("Failed to parse as array, attempting single object parsing for record: {}", recordValue.key());
            ExchangeTrade singleTrade = objectMapper.readValue(recordValue.value(), ExchangeTrade.class);
            trades = java.util.Collections.singletonList(singleTrade);
            log.debug("Successfully parsed single ExchangeTrade object and wrapped in list for record: {}", recordValue.key());
        }
        return trades;
    }

    /**
     * Handle partially filled orders from trade data - update status to PARTIAL_FILLED with traded amount
     */
    private void handlePartiallyFilledOrdersFromTrade(
            ExchangeTrade trade,
            ExchangeOrder buyOrder,
            ExchangeOrder sellOrder,
            ExchangeOrderService exchangeOrderService) {
        try {
            // Enhanced null safety with validation
            if (!isValidTradeForPartialFillProcessing(trade)) {
                log.warn("Invalid trade data for partial fill processing: buyOrderId={}, sellOrderId={}, amount={}",
                        trade != null ? trade.getBuyOrderId() : "null",
                        trade != null ? trade.getSellOrderId() : "null",
                        trade != null ? trade.getAmount() : "null");
                return;
            }

            // Calculate traded amounts from trade data with null safety
            BigDecimal tradeAmount = Optional.ofNullable(trade.getAmount()).orElse(BigDecimal.ZERO);
            BigDecimal buyTurnover = Optional.ofNullable(trade.getBuyTurnover()).orElse(BigDecimal.ZERO);
            BigDecimal sellTurnover = Optional.ofNullable(trade.getSellTurnover()).orElse(BigDecimal.ZERO);

            // Process buy order with enhanced null safety
            if (isValidOrderForPartialFillProcessing(buyOrder)) {
                processPartialFillForOrder(buyOrder, tradeAmount, buyTurnover, exchangeOrderService, "BUY");
            }

            // Process sell order with enhanced null safety
            if (isValidOrderForPartialFillProcessing(sellOrder)) {
                processPartialFillForOrder(sellOrder, tradeAmount, sellTurnover, exchangeOrderService, "SELL");
            }
        } catch (Exception e) {
            log.error("Error handling partially filled orders from trade: buyOrderId={}, sellOrderId={}, error={}",
                    trade != null ? trade.getBuyOrderId() : "null",
                    trade != null ? trade.getSellOrderId() : "null", e.getMessage(), e);
        }
    }

    /**
     * Check if order is partially filled with specific traded amount
     */
    private boolean isOrderPartiallyFilledWithAmounts(ExchangeOrder order, BigDecimal newTradedAmount) {
        if (order == null || newTradedAmount == null || order.getAmount() == null) {
            return false;
        }

        return newTradedAmount.compareTo(BigDecimal.ZERO) > 0
                && newTradedAmount.compareTo(order.getAmount()) < 0
                && (order.getStatus() == ExchangeOrderStatus.TRADING || order.getStatus() == ExchangeOrderStatus.PARTIAL_FILLED || order.getStatus() == ExchangeOrderStatus.TRIGGER);
    }

    /**
     * Send order notifications with final order states after all processing is complete
     */
    private void sendOrderNotifications(ExchangeOrder buyOrder, ExchangeOrder sellOrder, String symbol) {
        try {
            // Send notifications with final order states after all processing
            if (buyOrder != null && isValidOrderForNotification(buyOrder)) {
                messagingTemplate.convertAndSend(
                        "/topic/market/order-trade/" + symbol + "/" + buyOrder.getMemberId(), buyOrder);
                log.debug("Sent buy order notification: orderId={}, status={}, tradedAmount={}",
                        buyOrder.getOrderId(), buyOrder.getStatus(), buyOrder.getTradedAmount());
            }

            if (sellOrder != null && isValidOrderForNotification(sellOrder)) {
                messagingTemplate.convertAndSend(
                        "/topic/market/order-trade/" + symbol + "/" + sellOrder.getMemberId(), sellOrder);
                log.debug("Sent sell order notification: orderId={}, status={}, tradedAmount={}",
                        sellOrder.getOrderId(), sellOrder.getStatus(), sellOrder.getTradedAmount());
            }
        } catch (Exception e) {
            log.error("Error sending order notifications for symbol: {}, error: {}", symbol, e.getMessage(), e);
        }
    }

    /**
     * Validate trade data for partial fill processing
     */
    private boolean isValidTradeForPartialFillProcessing(ExchangeTrade trade) {
        return trade != null
                && trade.getAmount() != null
                && trade.getBuyOrderId() != null
                && trade.getSellOrderId() != null;
    }

    /**
     * Validate order data for partial fill processing
     */
    private boolean isValidOrderForPartialFillProcessing(ExchangeOrder order) {
        return order != null
                && order.getOrderId() != null
                && order.getAmount() != null
                && order.getStatus() != null;
    }

    /**
     * Process partial fill for a single order with enhanced error handling
     * ✅ FIXED: Properly calculates cumulative tradedAmount and turnover for each trade
     */
    private void processPartialFillForOrder(ExchangeOrder order, BigDecimal tradeAmount,
                                            BigDecimal tradeTurnover, ExchangeOrderService service, String orderType) {
        try {
            // ✅ CORRECT: Calculate cumulative amounts by adding current trade to existing amounts
            BigDecimal newCumulativeTradedAmount = order.getTradedAmount().add(tradeAmount);
            BigDecimal newCumulativeTurnover = order.getTurnover().add(tradeTurnover);

            if (isOrderPartiallyFilledWithAmounts(order, newCumulativeTradedAmount)) {
                log.info("Updating {} order {} status to PARTIAL_FILLED: newCumulativeTradedAmount={}, newCumulativeTurnover={}, totalAmount={}",
                        orderType, order.getOrderId(), newCumulativeTradedAmount, newCumulativeTurnover, order.getAmount());

                // ✅ FIXED: Pass cumulative values to service method (which now correctly uses them directly)
                service.updateOrderStatusToPartiallyFilled(order.getOrderId(), newCumulativeTradedAmount, newCumulativeTurnover);
            }
        } catch (Exception e) {
            log.error("Error processing partial fill for {} order {}: {}", orderType, order.getOrderId(), e.getMessage(), e);
        }
    }

    /**
     * Validate order for WebSocket notification
     */
    private boolean isValidOrderForNotification(ExchangeOrder order) {
        return order.getOrderId() != null
                && order.getMemberId() != null
                && order.getStatus() != null;
    }
}

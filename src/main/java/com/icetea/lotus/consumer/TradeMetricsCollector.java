package com.icetea.lotus.consumer;

/**
 * Interface for collecting trade processing metrics
 * This allows for separation of metrics collection logic from trade processing
 */
public interface TradeMetricsCollector {

    /**
     * Update processing metrics with new trade data
     * 
     * @param tradeCount Number of trades processed
     * @param processingTime Time taken to process trades in milliseconds
     */
    void updateProcessingMetrics(int tradeCount, long processingTime);

    /**
     * Get current processing statistics as a formatted string
     * 
     * @return Formatted string with processing statistics
     */
    String getProcessingStats();

    /**
     * Check if the trade processing system is healthy
     * 
     * @return true if the system is healthy, false otherwise
     */
    boolean isHealthy();

    /**
     * Log processing statistics
     * This method logs the current processing statistics
     */
    void logProcessingStats();
}

package com.icetea.lotus.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.dto.CancelOrdersRequest;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.TradePlate;
import com.icetea.lotus.entity.spot.ExchangeOrder;
import com.icetea.lotus.exception.BusinessException;
import com.icetea.lotus.job.ContractPushJob;
import com.icetea.lotus.job.ExchangePushJob;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.ExchangeOrderService;
import com.icetea.lotus.util.MessageResult;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@SuppressWarnings("java:S1068")
public class ExchangeTradeConsumer {
    private final Logger logger = LoggerFactory.getLogger(ExchangeTradeConsumer.class);
    private final CoinProcessorFactory coinProcessorFactory;
    private final SimpMessagingTemplate messagingTemplate;
    private final ExchangeOrderService exchangeOrderService;
    private final ExecutorService executor;
    private final ExchangePushJob pushJob;
    private final ContractPushJob contractPushJob;
    private final TradeMetricsCollector metricsCollector;
    private final ConcurrentHashMap<String, ExecutorService> symbolExecutors = new java.util.concurrent.ConcurrentHashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    @Value("${second.referrer.award}")
    private boolean secondReferrerAward;

    @Value("${handle-trade.use-old-version}")
    private boolean useOldVersionHandleTrade;

    public ExchangeTradeConsumer(
            CoinProcessorFactory coinProcessorFactory,
            SimpMessagingTemplate messagingTemplate,
            ExchangeOrderService exchangeOrderService,
            @Qualifier("tradeProcessorExecutor") ExecutorService executor,
            ExchangePushJob pushJob, ContractPushJob contractPushJob,
            TradeMetricsCollector metricsCollector) {
        this.coinProcessorFactory = coinProcessorFactory;
        this.messagingTemplate = messagingTemplate;
        this.exchangeOrderService = exchangeOrderService;
        this.executor = executor;
        this.pushJob = pushJob;
        this.contractPushJob = contractPushJob;
        this.metricsCollector = metricsCollector;
    }

    /**
     * Handle transaction details
     */
    @KafkaListener(topics = "${topic-kafka.exchange.trade}", containerFactory = "kafkaListenerContainerFactory", concurrency = "20")
    public void handleTrade(List<ConsumerRecord<String, String>> records) {
        try{
            for (ConsumerRecord<String, String> recordValue : records) {
                if(useOldVersionHandleTrade){
                    executor.submit(new HandleTradeThread(
                            recordValue,
                            objectMapper,
                            coinProcessorFactory,
                            exchangeOrderService,
                            messagingTemplate,
                            pushJob,
                            secondReferrerAward,
                            metricsCollector
                    ));
                }else{
                    String symbol = getSymbolFromTrade(recordValue);
                    ExecutorService symbolExecutor = getSymbolExecutor(symbol);
                    symbolExecutor.submit(new HandleTradeThread(
                            recordValue,
                            objectMapper,
                            coinProcessorFactory,
                            exchangeOrderService,
                            messagingTemplate,
                            pushJob,
                            secondReferrerAward,
                            metricsCollector
                    ));
                }
            }
        }catch (Exception ex){
            log.error("Error processing trade records: " + ex.getMessage(), ex);
            throw new BusinessException("Error processing trade records: " + ex.getMessage());
        }
    }

    @PreDestroy
    public void shutdownSymbolExecutors() {
        log.info("Shutting down symbol-specific trade processors...");
        symbolExecutors.forEach((symbol, executorService) -> {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Symbol executor for {} did not terminate in time, forcing shutdown.", symbol);
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.error("Interrupted while shutting down symbol executor for {}.", symbol, e);
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        });
        log.info("All symbol-specific trade processors shut down.");
    }

    private ExecutorService getSymbolExecutor(String symbol) {
        return symbolExecutors.computeIfAbsent(symbol, k -> {
            ThreadPoolExecutor symbolExecutor = new ThreadPoolExecutor(
                    0, // core = 0
                    1, // max 1 thread per symbol
                    60L, TimeUnit.SECONDS, // thread tự chết sau 60s idle
                    new LinkedBlockingQueue<>(),
                    r -> {
                        Thread t = new Thread(r, "trade-processor-symbol-" + k);
                        t.setDaemon(true);
                        return t;
                    }
            );
            symbolExecutor.allowCoreThreadTimeOut(true);
            return symbolExecutor;
        });
    }

    private String getSymbolFromTrade(ConsumerRecord<String, String> recordValue) throws JsonProcessingException {
        List<ExchangeTrade> trades;
        try {
            // First try to parse as array
            trades = objectMapper.readValue(recordValue.value(), new TypeReference<>() {
            });
        } catch (MismatchedInputException e) {
            // If array parsing fails, try parsing as single object and wrap in list
            log.debug("Failed to parse as array, attempting single object parsing for record: {}", recordValue.key());
            ExchangeTrade singleTrade = objectMapper.readValue(recordValue.value(), ExchangeTrade.class);
            trades = java.util.Collections.singletonList(singleTrade);
            log.debug("Successfully parsed single ExchangeTrade object and wrapped in list for record: {}", recordValue.key());
        }
        return trades.get(0).getSymbol();
    }

    @KafkaListener(topics = "${topic-kafka.exchange.order-completed}", containerFactory = "kafkaListenerContainerFactory")
    public void handleOrderCompleted(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> recordValue : records) {
                List<ExchangeOrder> orders = objectMapper.readValue(recordValue.value(), new TypeReference<List<ExchangeOrder>>() {
                });
                for (ExchangeOrder order : orders) {
                    String symbol = order.getSymbol();
                    log.info("Processing order completion: orderId={}, symbol={}, tradedAmount={}, turnover={}",
                            order.getOrderId(), symbol, order.getTradedAmount(), order.getTurnover());

                    handlePushMessageWhenOrderCompleted(order, symbol);
                }
            }
        } catch (Exception e) {
            log.error("Critical error processing order completion batch: error={}, recordCount={}",
                    e.getMessage(), records.size(), e);
            // CRITICAL FIX: Don't acknowledge the message if there's a critical error
            // This allows Kafka to retry the message
            throw new BusinessException("Failed to process order completion batch");
        }
    }

    private void handlePushMessageWhenOrderCompleted(ExchangeOrder order, String symbol) {
        try {
            // CRITICAL FIX: Handle completion result and log appropriately
            MessageResult result = exchangeOrderService.tradeCompleted(order.getOrderId(), order.getTradedAmount(), order.getTurnover(), order.getTriggered(), order.getTriggerTime());

            if (result.getCode() == 0) {
                log.info("Order completion successful: orderId={}, message={}", order.getOrderId(), result.getMessage());

                // Push order delivery only on successful completion
                messagingTemplate.convertAndSend("/topic/market/order-completed/" + symbol + "/" + order.getMemberId(), order);
                log.debug("Pushed order completion notification: orderId={}, memberId={}", order.getOrderId(), order.getMemberId());
            } else {
                // CRITICAL FIX: Log completion failures with details
                log.warn("Order completion failed: orderId={}, code={}, message={}, tradedAmount={}, turnover={}",
                        order.getOrderId(), result.getCode(), result.getMessage(), order.getTradedAmount(), order.getTurnover());

                // CRITICAL FIX: Don't push notification for failed completions
                // This prevents user from receiving incorrect completion notifications
            }
        } catch (Exception e) {
            log.error("Exception during order completion processing: orderId={}, error={}",
                    order.getOrderId(), e.getMessage(), e);
            // Continue processing other orders even if one fails
        }
    }

    /**
     * Consumer trading information
     */
    @KafkaListener(topics = "${topic-kafka.exchange.trade-plate}", containerFactory = "kafkaListenerContainerFactory")
    public void handleTradePlate(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> recordValue : records) {
                logger.info("Push the topic information={},value={},size={}", recordValue.topic(), recordValue.value(), records.size());
                TradePlate plate = objectMapper.readValue(recordValue.value(), TradePlate.class);

                String symbol = plate.getSymbol();
                pushJob.addPlates(symbol, plate);
                // REMOVED: pushJob.pushPlate() - gây ra duplicate processing và clear queue sớm
                // Chỉ dựa vào @Scheduled pushPlate() để xử lý theo batch
                pushJob.pushPlate();
            }
        } catch (Exception e) {
            log.info("====Trade plate exception, error={}", e.getMessage());
        }
    }

    /**
     * Order cancellation successfully
     */
    @KafkaListener(topics = "${topic-kafka.exchange.order-cancel-success}", containerFactory = "kafkaListenerContainerFactory")
    public void handleOrderCanceled(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> recordValue : records) {
                logger.info("Cancel order message topic={},value={},size={}", recordValue.topic(), recordValue.value(), records.size());
                ExchangeOrder order = objectMapper.readValue(recordValue.value(), ExchangeOrder.class);
                //  Calling service processing
                MessageResult result = exchangeOrderService.cancelOrder(order.getOrderId(), order.getTradedAmount(), order.getTurnover(), order.getTriggered(), order.getTriggerTime(), order.getCancelReason());
                //  Push real-time transactions
                messagingTemplate.convertAndSend("/topic/market/order-canceled/" + order.getMemberId(), result);
            }
        } catch (Exception e) {
            log.info("Get issue on consume Kafka topic | exchange-order-cancel-success : {}", e.getMessage());
        }
    }

    @KafkaListener(topics = "${topic-kafka.exchange.order-cancel-all-completed}", containerFactory = "kafkaListenerContainerFactory")
    public void handleOrderCanceledAll(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> recordValue : records) {
                CancelOrdersRequest request = objectMapper.readValue(recordValue.value(), CancelOrdersRequest.class);
                Long memberId = request.getMemberId();

                log.info("Cancel all order message member={}", memberId);

                List<ExchangeOrder> orders = request.getOrders();
                int failed = 0;

                for (ExchangeOrder order : orders) {
                    // Service call
                    MessageResult result = exchangeOrderService.cancelOrder(
                            order.getOrderId(),
                            order.getTradedAmount(),
                            order.getTurnover(),
                            order.getTriggered(),
                            order.getTriggerTime(),
                            order.getCancelReason()
                    );

                    // Track failed cancels per member
                    if (result.getCode() == 0) {
                        log.info("Order cancellation successful: orderId={}", order.getOrderId());
                    } else {
                        failed++;
                        log.warn("Order cancellation failed: orderId={}, code={}, message={}",
                                order.getOrderId(), result.getCode(), result.getMessage());
                    }
                }

                MessageResult result;
                if (failed == 0 && Boolean.TRUE.equals(!request.getHasInvalidOrder())) {
                    result = MessageResult.success("Cancel successfully");
                } else if (failed < orders.size()) {
                    result = MessageResult.error("Some orders could not be cancelled");
                } else {
                    result = MessageResult.error("Cancel failed. Please try again");
                }

                pushMessageToCanceledOrder(memberId, result);
            }
        } catch (Exception ex) {
            log.error("Failed to process record from exchange-order-cancel-all-completed topic: {}", ex.getMessage());
        }
    }

    private void pushMessageToCanceledOrder(Long memberId, MessageResult result) {
        try {
            messagingTemplate.convertAndSend("/topic/market/order-canceled/" + memberId, result);
        } catch (Exception ex) {
            log.error("Failed to send WebSocket message for memberId={}: {}", memberId, ex.getMessage(), ex);
        }
    }

    @KafkaListener(topics = "${topic-kafka.exchange.stop-order-triggered}", containerFactory = "kafkaListenerContainerFactory")
    public void handleTriggeredOrder(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> recordValue : records) {
                logger.info("Triggered order message topic={},value={},size={}", recordValue.topic(), recordValue.value(), records.size());
                ExchangeOrder order = objectMapper.readValue(recordValue.value(), ExchangeOrder.class);
                //  Calling service processing
                exchangeOrderService.triggeredOrder(order);
                //  Push real-time transactions

            }
        } catch (Exception e) {
            log.info("Get issue on consume Kafka topic | exchange-stop-order-triggered : {}", e.getMessage());
        }
    }

    @KafkaListener(topics = "${topic-kafka.contract.trade-plate}", containerFactory = "kafkaListenerContainerFactory")
    public void handleContractTradePlate(List<ConsumerRecord<String, String>> records) {
        try {
            log.info("====Start handleContractTradePlate====");
            for (ConsumerRecord<String, String> recordValue : records) {
                log.info("Push the topic information={},value={},size={}", recordValue.topic(), recordValue.value(), records.size());
                Map<String, Object> result = objectMapper.readValue(recordValue.value(), new TypeReference<>() {
                });

                String symbol = objectMapper.convertValue(result.get(CommonConstants.FUTURE_ORDER_BOOK_SYMBOL), String.class);
                Map<String, Object> orderBook = objectMapper.convertValue(result.get(CommonConstants.FUTURE_MATCHING_ENGINE_ORDERBOOKSNAPSHOT), Map.class);
                contractPushJob.updateOrderBook(symbol, orderBook);
                contractPushJob.pushFutureOrderBook();
            }
        } catch (Exception e) {
            log.info("====Trade plate exception, error={}", e.getMessage());
        }
        log.info("====End handleContractTradePlate====");
    }


    /**
     * Log processing statistics periodically for monitoring
     */
    @Scheduled(fixedRate = 60000) // Every minute
    public void logProcessingStats() {
        metricsCollector.logProcessingStats();
    }

    /**
     * Health check method for monitoring systems
     */
    public boolean isHealthy() {
        return metricsCollector.isHealthy();
    }
}

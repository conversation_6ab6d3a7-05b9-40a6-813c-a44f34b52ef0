package com.icetea.lotus.consumer;

import com.icetea.lotus.constant.SysConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.kafka.annotation.KafkaListener;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class DataDictionarySaveUpdateConsumer {

    private final RedisTemplate<String, String> redisTemplate;

    @KafkaListener(topics = "data-dictionary-save-update", groupId = "group-handle", containerFactory = "kafkaListenerContainerFactory", autoStartup = "true")
    public void handleDataDictionarySaveUpdate(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> recordValue : records) {
            log.info("topic={}, key={}, value={}, size={}", recordValue.topic(), recordValue.key(), recordValue.value(), records.size());

            String bond = recordValue.key();
            String value = recordValue.value();
            log.info(">>>> Dictionary data modified >>> Updating cache >>> bond={} >>> value={}", bond, value);

            String key = SysConstant.DATA_DICTIONARY_BOUND_KEY + bond;
            ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
            String bondValue = valueOperations.get(key);

            if (bondValue == null) {
                log.info(">>>> No data in cache >>>>");
                valueOperations.set(key, value);
            } else {
                log.info(">>>> Data found in cache >>>>");
                redisTemplate.delete(key);
                valueOperations.set(key, value);
            }
        }
    }
}
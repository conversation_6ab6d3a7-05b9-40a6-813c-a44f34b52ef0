package com.icetea.lotus.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.dto.MinusAmountWalletSpotDto;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.MemberWallet;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.MemberWalletService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class WalletConsumer {
    private final ObjectMapper objectMapper;
    private final MemberWalletService memberWalletService;
    private final CoinService coinService;

//    @KafkaListener(topics = "minus-balance-wallet-spot", groupId = "group-handle")
    @SneakyThrows
    public void minusBalanceSpot(String message) {
        log.info("minusBalanceSpot message: {}", message);
        MinusAmountWalletSpotDto dto = objectMapper.readValue(message, MinusAmountWalletSpotDto.class);
        log.info("minusBalanceSpot: {}", dto.toString());
        Coin coin = coinService.findByUnit(dto.getCoin());
        log.info("minusBalanceSpot coin: {}", coin.toString());
        MemberWallet memberWallet = memberWalletService.findByCoinAndMemberId(coin, dto.getMemberId());
        log.info("minusBalanceSpot memberWallet: {}", memberWallet.toString());
        memberWalletService.decreaseBalance(memberWallet.getId(), dto.getAmount());
    }
}

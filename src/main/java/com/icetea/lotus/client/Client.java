package com.icetea.lotus.client;


import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

import static org.springframework.beans.factory.config.BeanDefinition.SCOPE_PROTOTYPE;

@Service
@Scope(SCOPE_PROTOTYPE)
@Slf4j
public class Client {

    public void connect(WebSocketClient ws) {
        try {
            // Request a connection
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[]{};
                }

                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    throw new UnsupportedOperationException("Not implemented yet");
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    throw new UnsupportedOperationException("Not implemented yet");
                }
            }};
            SSLContext sc = SSLContext.getInstance("TLS");
            // Create a WebSocket factory
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            SSLSocketFactory factory = sc.getSocketFactory();
            ws.setSocketFactory(factory);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.info("Unexpected error occurred", e);
        }
        // Make a connection
        ws.connect();
    }
}

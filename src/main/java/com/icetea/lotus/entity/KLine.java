package com.icetea.lotus.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class KLine {
    public KLine() {

    }

    public KLine(String period) {
        this.period = period;
    }

    /**
     * Trading pair symbol (e.g., "BTC/USDT")
     */
    private String symbol;

    /**
     * Timestamp in milliseconds marking the start of the period
     */
    private long time;

    /**
     * Time period identifier (e.g., "1min", "5min", "1day")
     */
    private String period;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal openPrice = BigDecimal.ZERO;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal highestPrice = BigDecimal.ZERO;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal lowestPrice = BigDecimal.ZERO;

    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal closePrice = BigDecimal.ZERO;

    /**
     * Number of transactions
     */
    private int count;
    /**
     * Trading volume
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal volume = BigDecimal.ZERO;

    /**
     * Transaction volume
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal turnover = BigDecimal.ZERO;

    /**
     * Indicates if the K-line is still being updated (current period)
     * Used for real-time streaming
     */
    private Boolean incomplete = false;

    /**
     * Timestamp of the last update to this K-line
     * Used for real-time tracking
     */
    private Long lastUpdateTime;
}

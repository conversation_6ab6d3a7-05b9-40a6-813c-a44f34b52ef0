package com.icetea.lotus.enums;

import lombok.Getter;

@Getter
public enum PeriodType {
    ONE_MIN("1min"),
    FIVE_MIN("5min"),
    FIFTEEN_MIN("15min"),
    THIRTY_MIN("30min"),
    ONE_HOUR("1hour"),
    FOUR_HOUR("4hour"),
    ONE_DAY("1day"),
    ONE_MONTH("1mon"),
    ONE_WEEK("1week");

    private final String value;

    PeriodType(String value) {
        this.value = value;
    }

    // Optional: convert string to enum
    public static PeriodType fromValue(String value) {
        for (PeriodType period : values()) {
            if (period.value.equalsIgnoreCase(value)) {
                return period;
            }
        }
        throw new IllegalArgumentException("Unknown period value: " + value);
    }
}

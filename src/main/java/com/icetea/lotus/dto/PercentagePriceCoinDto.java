package com.icetea.lotus.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PercentagePriceCoinDto {
    private String symbol;
    private BigDecimal chg1h;
    private BigDecimal chg24h;
    private BigDecimal chg7d;
}

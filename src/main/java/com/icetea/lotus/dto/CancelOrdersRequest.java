package com.icetea.lotus.dto;

import com.icetea.lotus.entity.spot.ExchangeOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CancelOrdersRequest {
    private Long memberId;
    private Boolean hasInvalidOrder;
    private List<ExchangeOrder> orders;
}

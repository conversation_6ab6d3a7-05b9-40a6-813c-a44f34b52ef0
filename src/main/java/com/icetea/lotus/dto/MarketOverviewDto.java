package com.icetea.lotus.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MarketOverviewDto {
    private String symbol;
    private BigDecimal chg1h;
    private BigDecimal chg24h;
    private BigDecimal chg7d;
    private BigDecimal marketCap;
    private BigDecimal volume24h;
    private BigDecimal allTimeHigh;
    private BigDecimal circulationSupply;
    private BigDecimal maxSupplyLevel;
    private String description;
    private BigDecimal high24h;
    private BigDecimal low24h;
    private BigDecimal lastPrice;
}

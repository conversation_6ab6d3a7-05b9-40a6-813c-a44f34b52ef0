package com.icetea.lotus.dto;

import com.icetea.lotus.entity.CoinThumb;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CoinListResponseDto {
    private BigDecimal marketCap;
    private String iconUrl;
    private CoinThumb coinThumb;
}

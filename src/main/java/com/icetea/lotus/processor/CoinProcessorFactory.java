package com.icetea.lotus.processor;


import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Getter
@Slf4j
public class CoinProcessorFactory {

    private final Map<String, CoinProcessor> processorMap;

    public CoinProcessorFactory() {
        processorMap = new ConcurrentHashMap<>();
    }

    public void addProcessor(String symbol, CoinProcessor processor) {
        if (symbol == null || processor == null) {
            throw new IllegalArgumentException("Symbol and processor cannot be null");
        }
        log.info("CoinProcessorFactory addProcessor = {}", symbol);
        processorMap.put(symbol, processor);
    }

    public boolean hasProcessor(String symbol) {
        return processorMap.containsKey(symbol);
    }

    public CoinProcessor getProcessor(String symbol) {
        return processorMap.get(symbol);
    }
}

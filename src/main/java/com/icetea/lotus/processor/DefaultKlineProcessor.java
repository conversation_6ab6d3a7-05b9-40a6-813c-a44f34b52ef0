package com.icetea.lotus.processor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * Default implementation of KlineProcessor that handles all K-line related operations
 */
public class DefaultKlineProcessor implements KlineProcessor {
    private static final Logger logger = LoggerFactory.getLogger(DefaultKlineProcessor.class);
    private static final String PERIOD_1MIN = "1min";
    public static final String K_LINE_GENERATION_IS_STOPPED = "K-line generation is stopped for {}";
    public static final String USING_PREVIOUS_K_LINE_CLOSING_PRICE_AS_OPENING_PRICE_FOR_PERIOD = "Using previous K-line closing price as opening price: {} for period {}";
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Getter
    private final String symbol;
    private MarketHandlerService marketHandlerService;
    private final ReentrantLock kLineLock = new ReentrantLock();

    private KLine currentKLine;
    private MarketService service;
    private volatile boolean stopKLine = false;

    /**
     * -- SETTER --
     * Sets the suppliers to get data from CoinProcessor
     */
    // Suppliers to get data from CoinProcessor
    @Setter
    private Supplier<CoinThumb> coinThumbSupplier;
    @Setter
    private Supplier<ReentrantLock> thumbLockSupplier;

    public DefaultKlineProcessor(String symbol) {
        this.symbol = symbol;
        createNewKLine();
    }

    @Override
    public void setIsStopKLine(boolean stop) {
        this.stopKLine = stop;
        if (stop) {
            logger.info("K-line generation stopped for {}", symbol);
        } else {
            logger.info("K-line generation resumed for {}", symbol);
        }
    }

    @Override
    public boolean isStopKline() {
        return stopKLine;
    }

    @Override
    public void generateKLine(long time, int minute, int hour) {
        logger.info("generateKLine for symbol: {}", symbol);
        if (stopKLine) {
            logger.debug(K_LINE_GENERATION_IS_STOPPED, symbol);
            return;
        }

        logger.info("Generating K-lines for {} (time represents period START)", symbol);
        long startTime = System.currentTimeMillis();

        // Generate a 1-minute K-line (always generated)
        this.autoGenerate();
        this.generateKLine1min(1, Calendar.MINUTE, time);

        // Update 24H transaction volume
        this.update24HVolume(time);

        // Generate K-lines for different time periods based on standard trading timeframes
        // Following specification: 1min, 5min, 15min, 30min, 1hour, 4hour, 1day, 1week, 1month

        if (minute % 5 == 0) {
            this.generateKLine(5, Calendar.MINUTE, time);
        }
        if (minute % 15 == 0) {
            this.generateKLine(15, Calendar.MINUTE, time);
        }
        if (minute % 30 == 0) {
            this.generateKLine(30, Calendar.MINUTE, time);
        }

        // Generate 1-hour K-line at the beginning of each hour (minute == 0)
        if (minute == 0) {
            this.generateKLine(1, Calendar.HOUR_OF_DAY, time);
        }

        long duration = System.currentTimeMillis() - startTime;
        logger.info("Completed K-line generation for {} in {}ms", symbol, duration);
    }

    @Override
    public void generateKLine1min(int range, int field, long time) {
        if (stopKLine) {
            logger.debug(K_LINE_GENERATION_IS_STOPPED, symbol);
            return;
        }

        // Generate the K-line and store it
        KLine kLine = generateKLineData(range, field, time);

        // Ensure the K-line has the latest trade price
        if (kLine.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
            CoinThumb coinThumb = coinThumbSupplier.get();
            ReentrantLock thumbLock = thumbLockSupplier.get();

            try {
                thumbLock.lock();
                if (coinThumb != null && coinThumb.getClose().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal closePrice = coinThumb.getClose();
                    kLine.setOpenPrice(closePrice);
                    kLine.setClosePrice(closePrice);
                    kLine.setHighestPrice(closePrice);
                    kLine.setLowestPrice(closePrice);
                } else {
                    kLine.setClosePrice(BigDecimal.ZERO);
                    kLine.setHighestPrice(BigDecimal.ZERO);
                    kLine.setLowestPrice(BigDecimal.ZERO);
                    kLine.setOpenPrice(BigDecimal.ZERO);
                }
            } finally {
                thumbLock.unlock();
            }
        }

        handleKLineStorage(kLine);
    }

    @Override
    public void generateKLine(int range, int field, long time) {
        if (stopKLine) {
            logger.debug(K_LINE_GENERATION_IS_STOPPED, symbol);
            return;
        }

        KLine kLine = generateKLineData(range, field, time);
        handleKLineStorage(kLine);
    }

    @Override
    public KLine getKLine() {
        try {
            kLineLock.lock();
            return currentKLine;
        } finally {
            kLineLock.unlock();
        }
    }

    @Override
    public void autoGenerate() {
        CoinThumb coinThumb = coinThumbSupplier.get();
        if (coinThumb == null) {
            logger.warn("Cannot auto-generate K-line: coinThumb is null for {}", symbol);
            return;
        }

        try {
            kLineLock.lock();

            DateFormat df = new SimpleDateFormat("HH:mm:ss");
            if (logger.isInfoEnabled()) {
                logger.info("Auto-generating 1min K-line at {} for {}",
                        df.format(new Date(currentKLine.getTime())), symbol);
            }

            // Store the previous transaction price when there is no transaction price
            if (currentKLine.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal closePrice = coinThumb.getClose();
                currentKLine.setOpenPrice(closePrice);
                currentKLine.setLowestPrice(closePrice);
                currentKLine.setHighestPrice(closePrice);
                currentKLine.setClosePrice(closePrice);
            }
            // Set the current time
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            currentKLine.setTime(calendar.getTimeInMillis());
            // Create a new K-line for the next minute
            createNewKLine();
            if (logger.isDebugEnabled()) {
                try {
                    logger.debug("Generated K-line data: {}", objectMapper.writeValueAsString(currentKLine));
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing currentKLine", e);
                }
            }
        } finally {
            kLineLock.unlock();
        }
    }

    @Override
    public void processTrade(KLine kLine, ExchangeTrade exchangeTrade) {
        BigDecimal tradePrice = exchangeTrade.getPrice();
        BigDecimal tradeAmount = exchangeTrade.getAmount();

        // CRITICAL FIX: Only set openPrice if it's zero (not set yet)
        // Use previous K-line's closing price, not the first trade price
        if (kLine.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
            if (service != null) {
                KLine previousKline = service.findKlineLatest(symbol, kLine.getPeriod());
                if (previousKline != null && previousKline.getClosePrice() != null) {
                    kLine.setOpenPrice(previousKline.getClosePrice());
                    logger.debug(USING_PREVIOUS_K_LINE_CLOSING_PRICE_AS_OPENING_PRICE_FOR_PERIOD,
                            previousKline.getClosePrice(), kLine.getPeriod());
                } else {
                    // Only for the first K-line ever, use first trade price
                    kLine.setOpenPrice(tradePrice);
                    logger.debug("No previous K-line found, using first trade price as opening price: {} for period {}",
                            tradePrice, kLine.getPeriod());
                }
            } else {
                // Fallback if service is null
                kLine.setOpenPrice(tradePrice);
                logger.warn("MarketService is null, using first trade price as opening price: {} for period {}",
                        tradePrice, kLine.getPeriod());
            }
        }

        // Always update close price
        kLine.setClosePrice(tradePrice);

        // Update high and low prices
        if (kLine.getHighestPrice().compareTo(BigDecimal.ZERO) == 0 || tradePrice.compareTo(kLine.getHighestPrice()) > 0) {
            kLine.setHighestPrice(tradePrice);
        }

        if (kLine.getLowestPrice().compareTo(BigDecimal.ZERO) == 0 || tradePrice.compareTo(kLine.getLowestPrice()) < 0) {
            kLine.setLowestPrice(tradePrice);
        }

        // Update count, volume, and turnover
        kLine.setCount(kLine.getCount() + 1);
        kLine.setVolume(kLine.getVolume().add(tradeAmount));

        BigDecimal turnover = tradePrice.multiply(tradeAmount);
        kLine.setTurnover(kLine.getTurnover().add(turnover));
    }

    @Override
    public void handleKLineStorage(KLine kLine) {
        if (marketHandlerService == null) {
            return;
        }

        marketHandlerService.handleKLine(symbol, kLine);
    }

    @Override
    public void setMarketHandlerService(MarketHandlerService marketHandlerService) {
        this.marketHandlerService = marketHandlerService;
        logger.info("MarketHandlerService set for KlineProcessor {}", symbol);
    }

    @Override
    public void setMarketService(MarketService service) {
        this.service = service;
        logger.info("MarketService set for KlineProcessor {}", symbol);
    }

    @Override
    public void createNewKLine() {
        try {
            kLineLock.lock();
            currentKLine = new KLine();
            currentKLine.setPeriod(PERIOD_1MIN);
            currentKLine.setOpenPrice(BigDecimal.ZERO);
            currentKLine.setHighestPrice(BigDecimal.ZERO);
            currentKLine.setLowestPrice(BigDecimal.ZERO);
            currentKLine.setClosePrice(BigDecimal.ZERO);
            currentKLine.setVolume(BigDecimal.ZERO);
            currentKLine.setTurnover(BigDecimal.ZERO);
            currentKLine.setCount(0);
        } finally {
            kLineLock.unlock();
        }
    }

    @Override
    public void update24HVolume(long time) {
        // Implementation for updating 24H volume
        // This would typically involve querying the service for volume data
        if (service != null) {
            logger.debug("Updating 24H volume for {} at time {}", symbol, time);
            // Implementation would go here based on business logic
        }
    }

    /**
     * Push current K-line updates for all supported timeframes when a trade occurs
     * This implements Event-Driven Architecture for real-time kline updates
     *
     * @param exchangeTrade The trade that triggered the update
     */
    @Override
    public void pushCurrentKLineForAllTimeframes(ExchangeTrade exchangeTrade) {
        if (marketHandlerService == null) {
            return;
        }

        // Supported timeframes for current K-line updates
        String[] periods = {"1min", "5min", "15min", "30min", "1hour", "4hour", "1day", "1week"};

        for (String period : periods) {
            try {
                KLine currentKLineForPeriod = generateCurrentKLineForPeriod(period, exchangeTrade);
                if (currentKLineForPeriod != null) {
                    marketHandlerService.handleCurrentKLine(symbol, currentKLineForPeriod);
                }
            } catch (Exception e) {
                logger.error("Error generating current K-line for period {} and symbol {}: {}",
                        period, symbol, e.getMessage(), e);
            }
        }
    }

    /**
     * Generate current K-line data for a specific period based on the latest trade
     *
     * @param period        The time period (1min, 5min, etc.)
     * @param exchangeTrade The latest trade
     * @return Current K-line for the specified period
     */
    @Override
    public KLine generateCurrentKLineForPeriod(String period, ExchangeTrade exchangeTrade) {
        if (service == null) {
            logger.warn("Cannot generate current K-line: MarketService is null for {}", symbol);
            return null;
        }

        try {
            // Get the current time aligned to the period boundary
            long currentTime = System.currentTimeMillis();
            long periodStartTime = alignTimeToPeriod(currentTime, period);

            // Query existing trades for this period to build current K-line
            List<ExchangeTrade> periodTrades = service.findTradeByTimeRange(symbol, periodStartTime, currentTime);

            if (periodTrades.isEmpty()) {
                // If no trades in this period, create K-line with current trade data
                return createKLineFromSingleTrade(period, periodStartTime, exchangeTrade);
            } else {
                // Build K-line from all trades in the period
                return buildKLineFromTrades(period, periodStartTime, periodTrades);
            }
        } catch (Exception e) {
            logger.error("Error generating current K-line for period {} and symbol {}: {}",
                    period, symbol, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Align timestamp to period boundary
     *
     * @param timestamp The timestamp to align
     * @param period    The period to align to
     * @return The aligned timestamp
     */
    @Override
    public long alignTimeToPeriod(long timestamp, String period) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);

        // Reset seconds and milliseconds
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        switch (period) {
            case "1min":
                // Already aligned to a minute
                break;
            case "5min":
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 5) * 5);
                break;
            case "15min":
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 15) * 15);
                break;
            case "30min":
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 30) * 30);
                break;
            case "1hour":
                calendar.set(Calendar.MINUTE, 0);
                break;
            case "4hour":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, (calendar.get(Calendar.HOUR_OF_DAY) / 4) * 4);
                break;
            case "1day":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                break;
            case "1week":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                break;
            case "1month":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                break;
            default:
                break;
        }

        return calendar.getTimeInMillis();
    }

    /**
     * Create K-line from a single trade (when no other trades exist in the period)
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trade           The trade data
     * @return The created K-line
     */
    @Override
    public KLine createKLineFromSingleTrade(String period, long periodStartTime, ExchangeTrade trade) {
        KLine kLine = new KLine();
        kLine.setPeriod(period);
        kLine.setTime(periodStartTime);

        BigDecimal tradePrice = trade.getPrice();
        BigDecimal amount = trade.getAmount();

        // ✅ CORRECT FIX: Get opening price from immediately previous K-line
        BigDecimal openPrice;
        if (service != null) {
            KLine previousKline = service.findPreviousKline(symbol, period, periodStartTime);
            if (previousKline != null && previousKline.getClosePrice() != null) {
                openPrice = previousKline.getClosePrice();
                logger.debug(USING_PREVIOUS_K_LINE_CLOSING_PRICE_AS_OPENING_PRICE_FOR_PERIOD, openPrice, period);
            } else {
                // Only for the first K-line ever, use trade price
                openPrice = tradePrice;
                logger.debug("No previous K-line found, using trade price as opening price: {} for period {}", openPrice, period);
            }
        } else {
            // Fallback if service is null
            openPrice = tradePrice;
            logger.warn("MarketService is null, using trade price as opening price: {} for period {}", openPrice, period);
        }

        kLine.setOpenPrice(openPrice);
        // CRITICAL FIX: High and low should consider both opening price and trade price
        BigDecimal highPrice = openPrice.compareTo(tradePrice) > 0 ? openPrice : tradePrice;
        BigDecimal lowPrice = openPrice.compareTo(tradePrice) < 0 ? openPrice : tradePrice;
        kLine.setHighestPrice(highPrice);
        kLine.setLowestPrice(lowPrice);
        kLine.setClosePrice(tradePrice);
        kLine.setVolume(amount);
        kLine.setTurnover(tradePrice.multiply(amount));
        kLine.setCount(1);

        return kLine;
    }

    /**
     * Build K-line from multiple trades in the period
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trades          The list of trades
     * @return The built K-line
     */
    @Override
    public KLine buildKLineFromTrades(String period, long periodStartTime, List<ExchangeTrade> trades) {
        if (trades.isEmpty()) {
            return null;
        }

        KLine kLine = new KLine();
        kLine.setPeriod(period);
        kLine.setTime(periodStartTime);

        // ✅ CORRECT FIX: Get opening price from immediately previous K-line
        BigDecimal openPrice;
        if (service != null) {
            KLine previousKline = service.findPreviousKline(symbol, period, periodStartTime);
            if (previousKline != null && previousKline.getClosePrice() != null) {
                openPrice = previousKline.getClosePrice();
                logger.debug(USING_PREVIOUS_K_LINE_CLOSING_PRICE_AS_OPENING_PRICE_FOR_PERIOD, openPrice, period);
            } else {
                // Only for the first K-line ever, use first trade price
                ExchangeTrade firstTrade = trades.get(0);
                openPrice = firstTrade.getPrice();
                logger.debug("No previous K-line found, using first trade price as opening price: {} for period {}", openPrice, period);
            }
        } else {
            // Fallback if service is null
            ExchangeTrade firstTrade = trades.get(0);
            openPrice = firstTrade.getPrice();
            logger.warn("MarketService is null, using first trade price as opening price: {} for period {}", openPrice, period);
        }

        BigDecimal highPrice = openPrice;
        BigDecimal lowPrice = openPrice;
        BigDecimal closePrice = openPrice;
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal totalTurnover = BigDecimal.ZERO;
        int tradeCount = 0;

        // Process all trades
        for (ExchangeTrade trade : trades) {
            BigDecimal price = trade.getPrice();
            BigDecimal amount = trade.getAmount();

            // Update high and low
            if (price.compareTo(highPrice) > 0) {
                highPrice = price;
            }
            if (price.compareTo(lowPrice) < 0) {
                lowPrice = price;
            }

            // Last trade becomes close price
            closePrice = price;

            // Accumulate volume and turnover
            totalVolume = totalVolume.add(amount);
            totalTurnover = totalTurnover.add(price.multiply(amount));
            tradeCount++;
        }

        kLine.setOpenPrice(openPrice);
        kLine.setHighestPrice(highPrice);
        kLine.setLowestPrice(lowPrice);
        kLine.setClosePrice(closePrice);
        kLine.setVolume(totalVolume);
        kLine.setTurnover(totalTurnover);
        kLine.setCount(tradeCount);

        return kLine;
    }

    /**
     * Helper method to generate K-line data for a specific time range
     *
     * @param range The range value (e.g., 1, 5, 15, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param time  The START time of the period (aligned to period boundary)
     */
    private KLine generateKLineData(int range, int field, long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // The input time represents the START of the period
        long periodStartTime = calendar.getTimeInMillis();

        // Calculate the END time of the period by adding the range
        calendar.add(field, range);
        long periodEndTime = calendar.getTimeInMillis();

        if (logger.isDebugEnabled()) {
            String startTimeStr = df.format(new Date(periodStartTime));
            String endTimeStr = df.format(new Date(periodEndTime));
            logger.debug("Time range for {} K-line: {} to {} (START time semantics)",
                    symbol, startTimeStr, endTimeStr);
        }

        // Create and configure the K-line with START time (industry standard)
        KLine kLine = new KLine();
        kLine.setTime(periodStartTime);

        // Determine the range unit based on the field
        String rangeUnit = determineRangeUnit(field);
        kLine.setPeriod(range + rangeUnit);

        // Ensure new Kline have latest close price
        KLine latestKline = service.findKlineLatest(symbol, range + determineRangeUnit(field));
        if (latestKline != null && latestKline.getClosePrice() != null) {
            kLine.setOpenPrice(latestKline.getClosePrice());
            kLine.setHighestPrice(latestKline.getClosePrice());
            kLine.setClosePrice(latestKline.getClosePrice());
            kLine.setLowestPrice(latestKline.getClosePrice());
        } else {
            // If no previous KLine or its closePrice is null, use coinThumb's close price
            CoinThumb coinThumb = coinThumbSupplier.get();
            ReentrantLock thumbLock = thumbLockSupplier.get();

            try {
                thumbLock.lock();
                if (coinThumb != null && coinThumb.getClose().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal closePrice = coinThumb.getClose();
                    kLine.setOpenPrice(closePrice);
                    kLine.setHighestPrice(closePrice);
                    kLine.setClosePrice(closePrice);
                    kLine.setLowestPrice(closePrice);
                    logger.debug("Using coinThumb's close price for new KLine: {}", closePrice);
                }
            } finally {
                thumbLock.unlock();
            }
        }

        // Process trades for the K-line
        if (field == Calendar.MINUTE || field == Calendar.HOUR_OF_DAY || field == Calendar.DAY_OF_YEAR) {
            // For minute, hour, and day lines, query trade details directly
            List<ExchangeTrade> exchangeTrades = service.findTradeByTimeRange(this.symbol, periodStartTime, periodEndTime);
            // Process each trade to update the K-line
            for (ExchangeTrade exchangeTrade : exchangeTrades) {
                processTrade(kLine, exchangeTrade);
            }
        } else {
            // For weekly and monthly lines, use a more efficient approach
            processKline(kLine, periodStartTime, periodEndTime, field);
        }

        String kLineJson = "";
        if (logger.isDebugEnabled()) {
            try {
                kLineJson = objectMapper.writeValueAsString(kLine);
                logger.debug("Generated K-line: {}", kLineJson);
            } catch (JsonProcessingException e) {
                logger.error("Error serializing K-line", e);
            }
        }

        return kLine;
    }

    /**
     * Determines the range unit based on the calendar field
     */
    @Override
    public String determineRangeUnit(int field) {
        return switch (field) {
            case Calendar.MINUTE -> "min";
            case Calendar.HOUR_OF_DAY -> "hour";
            case Calendar.DAY_OF_YEAR -> "day";
            case Calendar.WEEK_OF_YEAR -> "week";
            case Calendar.MONTH -> "month";
            default -> {
                logger.warn("Unknown field type: {}, defaulting to 'min'", field);
                yield "min";
            }
        };
    }

    /**
     * Processes K-line data for weekly and monthly periods
     */
    @Override
    public void processKline(KLine kline, long fromTime, long endTime, int field) {
        String period = convertFieldToPeriod(field, 1);
        List<KLine> kLines = service.findAllKLine(symbol, fromTime, endTime, period);

        for (KLine k : kLines) {
            if (k.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            if (kline.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
                kline.setOpenPrice(k.getOpenPrice());
            }

            if (kline.getHighestPrice().compareTo(k.getHighestPrice()) < 0) {
                kline.setHighestPrice(k.getHighestPrice());
            }

            if (k.getLowestPrice().compareTo(BigDecimal.ZERO) > 0 &&
                    (kline.getLowestPrice().compareTo(BigDecimal.ZERO) == 0 ||
                            kline.getLowestPrice().compareTo(k.getLowestPrice()) > 0)) {
                kline.setLowestPrice(k.getLowestPrice());
            }

            if (k.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
                kline.setClosePrice(k.getClosePrice());
            }

            kline.setVolume(kline.getVolume().add(k.getVolume()));
            kline.setTurnover(kline.getTurnover().add(k.getTurnover()));
            kline.setCount(kline.getCount() + k.getCount());
        }
    }

    /**
     * Converts calendar field to period string
     */
    @Override
    public String convertFieldToPeriod(int field, int range) {
        switch (field) {
            case Calendar.MINUTE:
                return range + "min";
            case Calendar.HOUR_OF_DAY:
                return range + "hour";
            case Calendar.DAY_OF_YEAR:
                return range + "day";
            case Calendar.WEEK_OF_YEAR:
                return range + "week";
            case Calendar.MONTH:
                return range + "month";
            default:
                logger.warn("Unknown field type: {}, defaulting to '{}min'", field, range);
                return range + "min";
        }
    }
}

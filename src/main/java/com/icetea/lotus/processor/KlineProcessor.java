package com.icetea.lotus.processor;

import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;

import java.util.List;

/**
 * Interface for processing K-line data and operations.
 * This processor handles all K-line-related functionality, including generation,
 * processing trades, and managing K-line storage.
 */
public interface KlineProcessor {

    /**
     * Sets whether K-line generation should be stopped
     *
     * @param stop true to stop K-line generation, false to continue
     */
    void setIsStopKLine(boolean stop);

    /**
     * Checks if K-line generation is currently stopped
     *
     * @return true if K-line generation is stopped, false otherwise
     */
    boolean isStopKline();

    /**
     * Generates K-lines for different time periods based on the current time
     *
     * @param time   Current time in milliseconds
     * @param minute Current minute
     * @param hour   Current hour
     */
    void generateKLine(long time, int minute, int hour);

    /**
     * Generates a 1-minute K-line for the specified time range
     *
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param time  The end time in milliseconds
     */
    void generateKLine1min(int range, int field, long time);

    /**
     * Generates a K-line for the specified time range and field
     *
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param time  The end time in milliseconds
     */
    void generateKLine(int range, int field, long time);

    /**
     * Gets the current K-line
     *
     * @return The current KLine object
     */
    KLine getKLine();

    /**
     * Automatically generates a 1-minute K-line using the current data
     */
    void autoGenerate();

    /**
     * Updates a K-line with trade data
     *
     * @param kLine         The K-line to update
     * @param exchangeTrade The trade data
     */
    void processTrade(KLine kLine, ExchangeTrade exchangeTrade);

    /**
     * Stores and pushes K-line information to all registered handlers
     *
     * @param kLine The K-line data to store
     */
    void handleKLineStorage(KLine kLine);

    /**
     * Sets the MarketHandlerService for the KlineProcessor
     *
     * @param marketHandlerService the MarketHandlerService instance to be set
     */
    void setMarketHandlerService(MarketHandlerService marketHandlerService);

    /**
     * Sets the MarketService for data access operations
     *
     * @param service the MarketService instance
     */
    void setMarketService(MarketService service);

    /**
     * Creates a new KLine for the next minute
     */
    void createNewKLine();

    /**
     * Updates the 24-hour volume data
     *
     * @param time the current time in milliseconds
     */
    void update24HVolume(long time);

    /**
     * Processes K-line data for weekly and monthly periods by aggregating daily K-lines
     *
     * @param kline    The K-line to populate
     * @param fromTime The start time
     * @param endTime  The end time
     * @param field    The calendar field
     */
    void processKline(KLine kline, long fromTime, long endTime, int field);

    /**
     * Push current K-line updates for all supported timeframes when a trade occurs
     * This implements Event-Driven Architecture for real-time kline updates
     *
     * @param exchangeTrade The trade that triggered the update
     */
    void pushCurrentKLineForAllTimeframes(ExchangeTrade exchangeTrade);

    /**
     * Generate current K-line data for a specific period based on the latest trade
     *
     * @param period        The time period (1min, 5min, etc.)
     * @param exchangeTrade The latest trade
     * @return Current K-line for the specified period
     */
    KLine generateCurrentKLineForPeriod(String period, ExchangeTrade exchangeTrade);

    /**
     * Align timestamp to period boundary
     *
     * @param timestamp The timestamp to align
     * @param period    The period to align to
     * @return The aligned timestamp
     */
    long alignTimeToPeriod(long timestamp, String period);

    /**
     * Create K-line from a single trade (when no other trades exist in the period)
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trade           The trade data
     * @return The created K-line
     */
    KLine createKLineFromSingleTrade(String period, long periodStartTime, ExchangeTrade trade);

    /**
     * Build K-line from multiple trades in the period
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trades          The list of trades
     * @return The built K-line
     */
    KLine buildKLineFromTrades(String period, long periodStartTime, List<ExchangeTrade> trades);

    /**
     * Determines the range unit string based on the calendar field
     *
     * @param field The calendar field
     * @return The range unit string
     */
    String determineRangeUnit(int field);

    /**
     * Converts calendar field to period string
     *
     * @param field The calendar field
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @return The period string (e.g., "1min", "5hour", etc.)
     */
    String convertFieldToPeriod(int field, int range);
}

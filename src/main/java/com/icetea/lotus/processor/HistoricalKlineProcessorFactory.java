package com.icetea.lotus.processor;

import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Factory for creating and managing HistoricalKlineProcessor instances
 * Used by KLineGeneratorJob for scheduled historical K-line generation
 */
@Slf4j
@Component
public class HistoricalKlineProcessorFactory {

    private final MarketService marketService;
    private final MarketHandlerService marketHandlerService;

    /**
     * -- GETTER --
     * Get all processor instances
     */
    // Cache of HistoricalKlineProcessor instances by symbol
    @Getter
    private final Map<String, HistoricalKlineProcessor> processorMap = new ConcurrentHashMap<>();

    public HistoricalKlineProcessorFactory(MarketService marketService, MarketHandlerService marketHandlerService) {
        this.marketService = marketService;
        this.marketHandlerService = marketHandlerService;
    }

    /**
     * Get or create HistoricalKlineProcessor for a symbol
     *
     * @param symbol The trading symbol
     * @return HistoricalKlineProcessor instance
     */
    public HistoricalKlineProcessor getProcessor(String symbol) {
        return processorMap.computeIfAbsent(symbol, this::createProcessor);
    }

    /**
     * Create a new HistoricalKlineProcessor for a symbol
     *
     * @param symbol The trading symbol
     * @return New HistoricalKlineProcessor instance
     */
    private HistoricalKlineProcessor createProcessor(String symbol) {
        log.info("Creating HistoricalKlineProcessor for symbol: {}", symbol);
        return new HistoricalKlineProcessor(symbol, marketService, marketHandlerService);
    }

    /**
     * Remove processor for a symbol (cleanup)
     *
     * @param symbol The trading symbol
     */
    public void removeProcessor(String symbol) {
        HistoricalKlineProcessor removed = processorMap.remove(symbol);
        if (removed != null) {
            log.info("Removed HistoricalKlineProcessor for symbol: {}", symbol);
        }
    }

    /**
     * Clear all processors (cleanup)
     */
    public void clearAll() {
        processorMap.clear();
        log.info("Cleared all HistoricalKlineProcessor instances");
    }
}
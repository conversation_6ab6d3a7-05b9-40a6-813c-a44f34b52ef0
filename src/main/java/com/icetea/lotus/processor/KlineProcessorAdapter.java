package com.icetea.lotus.processor;

import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * Adapter that connects KlineProcessor with CoinProcessor.
 * This adapter implements the Adapter pattern to bridge the gap between
 * the separated KlineProcessor and the main CoinProcessor.
 */
@Getter
public class KlineProcessorAdapter {

    private static final Logger logger = LoggerFactory.getLogger(KlineProcessorAdapter.class);

    /**
     * -- GETTER --
     * Gets the underlying KlineProcessor instance
     */
    private final KlineProcessor klineProcessor;
    /**
     * -- GETTER --
     * Gets the symbol this adapter is handling
     */
    private final String symbol;

    private MarketService marketService;
    private MarketHandlerService marketHandlerService;

    public KlineProcessorAdapter(String symbol) {
        this.symbol = symbol;
        this.klineProcessor = new DefaultKlineProcessor(symbol);
        logger.info("Created KlineProcessorAdapter for symbol: {}", symbol);
    }

    /**
     * Initializes the adapter with suppliers from CoinProcessor
     */
    public void initialize(Supplier<CoinThumb> coinThumbSupplier,
                           Supplier<ReentrantLock> thumbLockSupplier,
                           MarketService marketService) {
        if (klineProcessor instanceof DefaultKlineProcessor defaultProcessor) {
            defaultProcessor.setCoinThumbSupplier(coinThumbSupplier);
            defaultProcessor.setThumbLockSupplier(thumbLockSupplier);
        }

        this.marketService = marketService;
        klineProcessor.setMarketService(marketService);
        logger.info("Initialized KlineProcessorAdapter for symbol: {}", symbol);
    }

    /**
     * Delegates K-line generation to the KlineProcessor
     */
    public void generateKLine(long time, int minute, int hour) {
        klineProcessor.generateKLine(time, minute, hour);
    }

    /**
     * Delegates 1-minute K-line generation to the KlineProcessor
     */
    public void generateKLine1min(int range, int field, long time) {
        klineProcessor.generateKLine1min(range, field, time);
    }

    /**
     * Delegates K-line generation with range and field to the KlineProcessor
     */
    public void generateKLine(int range, int field, long time) {
        klineProcessor.generateKLine(range, field, time);
    }

    /**
     * Delegates auto-generation to the KlineProcessor
     */
    public void autoGenerate() {
        klineProcessor.autoGenerate();
    }

    /**
     * Delegates K-line retrieval to the KlineProcessor
     */
    public KLine getKLine() {
        return klineProcessor.getKLine();
    }

    /**
     * Delegates stop K-line setting to the KlineProcessor
     */
    public void setIsStopKLine(boolean stop) {
        klineProcessor.setIsStopKLine(stop);
    }

    /**
     * Delegates stop K-line checking to the KlineProcessor
     */
    public boolean isStopKline() {
        return klineProcessor.isStopKline();
    }

    /**
     * Delegates trade processing to the KlineProcessor
     */
    public void processTrade(KLine kLine, ExchangeTrade exchangeTrade) {
        klineProcessor.processTrade(kLine, exchangeTrade);
    }

    /**
     * Delegates K-line storage handling to the KlineProcessor
     */
    public void handleKLineStorage(KLine kLine) {
        klineProcessor.handleKLineStorage(kLine);
    }

    /**
     * Delegates MarketHandlerService setting to the KlineProcessor
     */
    public void setMarketHandlerService(MarketHandlerService marketHandlerService) {
        this.marketHandlerService = marketHandlerService;
        klineProcessor.setMarketHandlerService(marketHandlerService);
    }

    /**
     * Delegates new K-line creation to the KlineProcessor
     */
    public void createNewKLine() {
        klineProcessor.createNewKLine();
    }

    /**
     * Delegates 24H volume update to the KlineProcessor
     */
    public void update24HVolume(long time) {
        klineProcessor.update24HVolume(time);
    }

    /**
     * A more efficient way to process weekly and monthly K-lines by aggregating daily K-lines
     * Delegates to KlineProcessor
     *
     * @param kline    The K-line to populate
     * @param fromTime The start time
     * @param endTime  The end time
     * @param field    The calendar field
     */
    public void processKline(KLine kline, long fromTime, long endTime, int field) {
        klineProcessor.processKline(kline, fromTime, endTime, field);
    }

    /**
     * Push current K-line updates for all supported timeframes when a trade occurs
     * Delegates to KlineProcessor
     *
     * @param exchangeTrade The trade that triggered the update
     */
    public void pushCurrentKLineForAllTimeframes(ExchangeTrade exchangeTrade) {
        klineProcessor.pushCurrentKLineForAllTimeframes(exchangeTrade);
    }

    /**
     * Generate current K-line data for a specific period based on the latest trade
     * Delegates to KlineProcessor
     *
     * @param period        The time period (1min, 5min, etc.)
     * @param exchangeTrade The latest trade
     * @return Current K-line for the specified period
     */
    @SuppressWarnings("all")
    private KLine generateCurrentKLineForPeriod(String period, ExchangeTrade exchangeTrade) {
        return klineProcessor.generateCurrentKLineForPeriod(period, exchangeTrade);
    }

    /**
     * Align timestamp to period boundary
     * Delegates to KlineProcessor
     *
     * @param timestamp The timestamp to align
     * @param period    The period to align to
     * @return The aligned timestamp
     */
    @SuppressWarnings("all")
    private long alignTimeToPeriod(long timestamp, String period) {
        return klineProcessor.alignTimeToPeriod(timestamp, period);
    }

    /**
     * Create K-line from a single trade (when no other trades exist in the period)
     * Delegates to KlineProcessor
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trade           The trade data
     * @return The created K-line
     */
    @SuppressWarnings("all")
    private KLine createKLineFromSingleTrade(String period, long periodStartTime, ExchangeTrade trade) {
        return klineProcessor.createKLineFromSingleTrade(period, periodStartTime, trade);
    }

    /**
     * Build K-line from multiple trades in the period
     * Delegates to KlineProcessor
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trades          The list of trades
     * @return The built K-line
     */
    @SuppressWarnings("all")
    private KLine buildKLineFromTrades(String period, long periodStartTime, List<ExchangeTrade> trades) {
        return klineProcessor.buildKLineFromTrades(period, periodStartTime, trades);
    }

    /**
     * Determines the range unit string based on the calendar field
     * Delegates to KlineProcessor
     *
     * @param field The calendar field
     * @return The range unit string
     */
    @SuppressWarnings("all")
    private String determineRangeUnit(int field) {
        return klineProcessor.determineRangeUnit(field);
    }

    /**
     * Converts calendar field to period string
     * Delegates to KlineProcessor
     *
     * @param field The calendar field
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @return The period string (e.g., "1min", "5hour", etc.)
     */
    public String convertFieldToPeriod(int field, int range) {
        return klineProcessor.convertFieldToPeriod(field, range);
    }
}

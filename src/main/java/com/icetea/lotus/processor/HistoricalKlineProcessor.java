package com.icetea.lotus.processor;

import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;

/**
 * Historical K-line processor for database-based K-line generation
 * This processor handles persisted K-line data for historical charts
 * Used by KLineGeneratorJob for scheduled K-line generation
 */
@Slf4j
public class HistoricalKlineProcessor {

    private final String symbol;
    private final MarketService marketService;
    private final MarketHandlerService marketHandlerService;

    public HistoricalKlineProcessor(String symbol, MarketService marketService, MarketHandlerService marketHandlerService) {
        this.symbol = symbol;
        this.marketService = marketService;
        this.marketHandlerService = marketHandlerService;
    }

    /**
     * Generate K-line for a specific time period by querying stored trades
     * This method is called by KLineGeneratorJob for historical data generation
     *
     * @param periodStartTime The start time of the period
     * @param minute          The minute value (for minute-based periods)
     * @param hour            The hour value (for hour-based periods)
     */
    public void generateKLine(long periodStartTime, int minute, int hour) {
        try {
            // Generate 1-minute K-line (base unit)
            generateKLineForPeriod("1min", periodStartTime, periodStartTime + 60000);

            // Generate other periods based on minute/hour values
            if (minute % 5 == 0) {
                long period5Start = alignTimeToPeriod(periodStartTime, "5min");
                generateKLineForPeriod("5min", period5Start, period5Start + 300000); // 5 minutes
            }

            if (minute % 15 == 0) {
                long period15Start = alignTimeToPeriod(periodStartTime, "15min");
                generateKLineForPeriod("15min", period15Start, period15Start + 900000); // 15 minutes
            }

            if (minute % 30 == 0) {
                long period30Start = alignTimeToPeriod(periodStartTime, "30min");
                generateKLineForPeriod("30min", period30Start, period30Start + 1800000); // 30 minutes
            }

            if (minute == 0) {
                // Generate hourly K-line
                long hourStart = alignTimeToPeriod(periodStartTime, "1hour");
                generateKLineForPeriod("1hour", hourStart, hourStart + 3600000); // 1 hour

                // Generate 4-hour K-line if it's a 4-hour boundary
                if (hour % 4 == 0) {
                    long hour4Start = alignTimeToPeriod(periodStartTime, "4hour");
                    generateKLineForPeriod("4hour", hour4Start, hour4Start + 14400000); // 4 hours
                }
            }

        } catch (Exception e) {
            log.error("Error generating historical K-line for symbol {} at time {}: {}",
                    symbol, periodStartTime, e.getMessage(), e);
        }
    }

    /**
     * Generate K-line for larger periods (daily, weekly, monthly)
     *
     * @param range           The range value (1 for single unit)
     * @param field           The calendar field (DAY_OF_YEAR, WEEK_OF_MONTH, MONTH)
     * @param periodStartTime The start time of the period
     */
    public void generateKLine(int range, int field, long periodStartTime) {
        try {
            String period = convertFieldToPeriod(field, range);
            long periodEndTime = calculatePeriodEndTime(periodStartTime, field, range);

            generateKLineForPeriod(period, periodStartTime, periodEndTime);

        } catch (Exception e) {
            log.error("Error generating historical K-line for symbol {} field {} range {} at time {}: {}",
                    symbol, field, range, periodStartTime, e.getMessage(), e);
        }
    }

    /**
     * Generate K-line for a specific period by querying trades from database
     *
     * @param period    The time period (e.g., "1min", "1hour", "1day")
     * @param startTime The start time of the period
     * @param endTime   The end time of the period
     */
    private void generateKLineForPeriod(String period, long startTime, long endTime) {
        try {
            // Query trades from database for the specified time range
            List<ExchangeTrade> trades = marketService.findTradeByTimeRange(symbol, startTime, endTime);

            if (trades.isEmpty()) {
                log.debug("No trades found for symbol {} period {} from {} to {}",
                        symbol, period, startTime, endTime);

                // Create K-line with previous closing price if no trades
                KLine kline = createKLineWithNoPriceChange(period, startTime);
                if (kline != null) {
                    persistKLine(kline);
                }
                return;
            }

            // Build K-line from trades
            KLine kline = buildKLineFromTrades(period, startTime, trades);
            if (kline != null) {
                // Mark as complete (historical data)
                kline.setIncomplete(false);

                // Persist to database
                persistKLine(kline);

                log.debug("Generated historical K-line for symbol {} period {} with {} trades",
                        symbol, period, trades.size());
            }

        } catch (Exception e) {
            log.error("Error generating K-line for symbol {} period {} from {} to {}: {}",
                    symbol, period, startTime, endTime, e.getMessage(), e);
        }
    }

    /**
     * Build K-line from a list of trades
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trades          The list of trades
     * @return The built K-line
     */
    private KLine buildKLineFromTrades(String period, long periodStartTime, List<ExchangeTrade> trades) {
        if (trades.isEmpty()) {
            return null;
        }

        KLine kline = new KLine();
        kline.setPeriod(period);
        kline.setTime(periodStartTime);
        kline.setSymbol(symbol);

        // Get opening price from previous K-line's closing price (proper continuity)
        BigDecimal openPrice = getPreviousClosingPrice(period, periodStartTime);
        if (openPrice == null) {
            // First K-line ever, use first trade price
            openPrice = trades.get(0).getPrice();
        }

        BigDecimal highPrice = openPrice;
        BigDecimal lowPrice = openPrice;
        BigDecimal closePrice = openPrice;
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal totalTurnover = BigDecimal.ZERO;
        int tradeCount = 0;

        // Process all trades
        for (ExchangeTrade trade : trades) {
            BigDecimal price = trade.getPrice();
            BigDecimal amount = trade.getAmount();

            // Update high and low
            if (price.compareTo(highPrice) > 0) {
                highPrice = price;
            }
            if (price.compareTo(lowPrice) < 0) {
                lowPrice = price;
            }

            // Last trade becomes close price
            closePrice = price;

            // Accumulate volume and turnover
            totalVolume = totalVolume.add(amount);
            totalTurnover = totalTurnover.add(price.multiply(amount));
            tradeCount++;
        }

        kline.setOpenPrice(openPrice);
        kline.setHighestPrice(highPrice);
        kline.setLowestPrice(lowPrice);
        kline.setClosePrice(closePrice);
        kline.setVolume(totalVolume);
        kline.setTurnover(totalTurnover);
        kline.setCount(tradeCount);
        kline.setIncomplete(false);

        return kline;
    }

    /**
     * Create K-line when no trades occurred (maintains price continuity)
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @return K-line with no price change or null if no previous data
     */
    private KLine createKLineWithNoPriceChange(String period, long periodStartTime) {
        BigDecimal previousClosePrice = getPreviousClosingPrice(period, periodStartTime);
        KLine kline = new KLine();
        if (previousClosePrice == null) {
            //return new kline in case no one data in database
            kline.setPeriod(period);
            kline.setTime(periodStartTime);
            kline.setSymbol(symbol);
            kline.setOpenPrice(BigDecimal.ZERO);
            kline.setHighestPrice(BigDecimal.ZERO);
            kline.setLowestPrice(BigDecimal.ZERO);
            kline.setClosePrice(BigDecimal.ZERO);
            kline.setVolume(BigDecimal.ZERO);
            kline.setTurnover(BigDecimal.ZERO);
            kline.setCount(0);
            kline.setIncomplete(false);
            return kline; // No previous data available
        }
        kline.setPeriod(period);
        kline.setTime(periodStartTime);
        kline.setSymbol(symbol);
        kline.setOpenPrice(previousClosePrice);
        kline.setHighestPrice(previousClosePrice);
        kline.setLowestPrice(previousClosePrice);
        kline.setClosePrice(previousClosePrice);
        kline.setVolume(BigDecimal.ZERO);
        kline.setTurnover(BigDecimal.ZERO);
        kline.setCount(0);
        kline.setIncomplete(false);

        return kline;
    }

    /**
     * Get the closing price from the previous K-line for price continuity
     *
     * @param period             The time period
     * @param currentPeriodStart The start time of the current period
     * @return Previous closing price or null if not found
     */
    private BigDecimal getPreviousClosingPrice(String period, long currentPeriodStart) {
        try {
            // ✅ CORRECT: Use findPreviousKline with timestamp
            KLine previousKline = marketService.findPreviousKline(symbol, period, currentPeriodStart);
            if (previousKline != null && previousKline.getClosePrice() != null) {
                return previousKline.getClosePrice();
            }
        } catch (Exception e) {
            log.warn("Error getting previous closing price for symbol {} period {}: {}",
                    symbol, period, e.getMessage());
        }
        return null;
    }

    /**
     * Persist K-line to database using only MongoMarketHandler
     *
     * @param kline The K-line to persist
     */
    private void persistKLine(KLine kline) {
        if (marketHandlerService != null) {
            try {
                // Store K-line in database using only MongoMarketHandler
                marketHandlerService.handlePersistKLine(symbol, kline);
                log.debug("Persisted historical K-line for symbol {} period {} at time {}",
                        symbol, kline.getPeriod(), kline.getTime());
            } catch (Exception e) {
                log.error("Error persisting K-line for symbol {} period {}: {}",
                        symbol, kline.getPeriod(), e.getMessage(), e);
            }
        }
    }

    /**
     * Align timestamp to period boundary
     *
     * @param timestamp The timestamp to align
     * @param period    The period to align to
     * @return The aligned timestamp
     */
    private long alignTimeToPeriod(long timestamp, String period) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);

        // Reset seconds and milliseconds
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        switch (period) {
            case CommonConstants.PERIOD.ONE_MIN:
                break;
            case CommonConstants.PERIOD.FIVE_MIN:
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 5) * 5);
                break;
            case CommonConstants.PERIOD.FIFTEEN_MIN:
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 15) * 15);
                break;
            case CommonConstants.PERIOD.THIRTY_MIN:
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 30) * 30);
                break;
            case CommonConstants.PERIOD.ONE_HOUR:
                calendar.set(Calendar.MINUTE, 0);
                break;
            case CommonConstants.PERIOD.FOUR_HOUR:
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, (calendar.get(Calendar.HOUR_OF_DAY) / 4) * 4);
                break;
            case CommonConstants.PERIOD.ONE_DAY:
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                break;
            case CommonConstants.PERIOD.ONE_WEEK:
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                break;
            default:
                break;
        }

        return calendar.getTimeInMillis();
    }

    /**
     * Calculate period end time based on field and range
     *
     * @param startTime The start time
     * @param field     The calendar field
     * @param range     The range value
     * @return The end time
     */
    private long calculatePeriodEndTime(long startTime, int field, int range) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startTime);

        switch (field) {
            case Calendar.MINUTE:
                calendar.add(Calendar.MINUTE, range);
                break;
            case Calendar.HOUR_OF_DAY:
                calendar.add(Calendar.HOUR_OF_DAY, range);
                break;
            case Calendar.DAY_OF_YEAR:
                calendar.add(Calendar.DAY_OF_YEAR, range);
                break;
            case Calendar.WEEK_OF_MONTH:
                calendar.add(Calendar.WEEK_OF_YEAR, range);
                break;
            case Calendar.MONTH:
                calendar.add(Calendar.MONTH, range);
                break;
            default:
                break;
        }

        return calendar.getTimeInMillis();
    }

    /**
     * Convert calendar field to period string
     *
     * @param field The calendar field
     * @param range The range value
     * @return The period string
     */
    private String convertFieldToPeriod(int field, int range) {
        return switch (field) {
            case Calendar.MINUTE -> range + "min";
            case Calendar.HOUR_OF_DAY -> range + "hour";
            case Calendar.DAY_OF_YEAR -> range + "day";
            case Calendar.WEEK_OF_MONTH -> range + "week";
            case Calendar.MONTH -> range + "mon";
            default -> "1min";
        };
    }
}

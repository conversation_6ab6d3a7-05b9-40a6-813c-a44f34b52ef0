package com.icetea.lotus.processor;

import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;

import java.util.List;

public interface CoinProcessor {

    void setIsHalt(boolean status);

    /**
     * Processes a list of exchange trades. This method is expected to apply
     * some processing logic to the provided trades, such as updating coin data or
     * performing calculations based on trade details.
     *
     * @param trades the list of ExchangeTrade objects to be processed. Each trade
     *               contains information about a specific transaction, such as
     *               price, amount, and other relevant data. The input list must
     *               not be null to ensure proper execution.
     */
    void process(List<ExchangeTrade> trades);

    /**
     * Sets the MarketHandlerService for the CoinProcessor. The service will
     * handle tasks such as storing and processing trade and K-line data related to
     * all markets through centralized handler management.
     *
     * @param marketHandlerService the MarketHandlerService instance to be set. This service will be used
     *                             to process and store information such as trades and K-lines.
     *                             Must not be null.
     */
    void setMarketHandlerService(MarketHandlerService marketHandlerService);

    CoinThumb getThumb();

    void setMarketService(MarketService service);

    void initializeThumb();

    void resetThumb();

    void setExchangeRate(CoinExchangeRate coinExchangeRate);

    void initializeUsdRate();

    /**
     * Gets the KlineProcessorAdapter for handling K-line operations
     *
     * @return the KlineProcessorAdapter instance
     */
    KlineProcessorAdapter getKlineAdapter();

    /**
     * Clean up resources when the processor is no longer needed
     * This should be called when the processor is being shut down or removed
     */
    void cleanup();
}

package com.icetea.lotus.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Default transaction processor generates 1m K-line information
 */
@ToString
@Getter
@Setter
public class DefaultCoinProcessor implements CoinProcessor {

    private static final Logger logger = LoggerFactory.getLogger(DefaultCoinProcessor.class);
    private static final int SCALE = 4;
    private static final String PERIOD_1MIN = "1min";
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final String symbol;
    private final String baseCoin;
    private final ReentrantLock thumbLock = new ReentrantLock();
    private final RedisTemplate<String, Object> redisTemplate;
    private MarketHandlerService marketHandlerService;
    private CoinThumb coinThumb;
    private MarketService service;
    private CoinExchangeRate coinExchangeRate;
    private KlineProcessorAdapter klineAdapter;
    private RealtimeKlineProcessor realtimeKlineProcessor;
    // Whether to deal with it temporarily
    private volatile boolean isHalt = true;

    public DefaultCoinProcessor(String symbol, String baseCoin, RedisTemplate<String, Object> redisTemplate) {
        this.baseCoin = baseCoin;
        this.symbol = symbol;

        // Initialize KlineProcessorAdapter
        this.klineAdapter = new KlineProcessorAdapter(symbol);

        this.coinThumb = new CoinThumb();
        this.coinThumb.setSymbol(symbol);
        this.redisTemplate = redisTemplate;
    }

    // convertFieldToPeriod method has been moved to KlineProcessorAdapter

    @Override
    public void initializeThumb() {
        Calendar calendar = Calendar.getInstance();
        // Set the seconds and microsecond fields to 0
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long nowTime = calendar.getTimeInMillis();
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        long firstTimeOfToday = calendar.getTimeInMillis();

        logger.info("initializeThumb from {} to {}", firstTimeOfToday, nowTime);
        List<KLine> lines = service.findAllKLine(this.symbol, firstTimeOfToday, nowTime, PERIOD_1MIN);

        try {
            thumbLock.lock();
            // Reset thumb values
            coinThumb.setOpen(BigDecimal.ZERO);
            coinThumb.setHigh(BigDecimal.ZERO);
            coinThumb.setLow(BigDecimal.ZERO);
            coinThumb.setClose(BigDecimal.ZERO);
            coinThumb.setVolume(BigDecimal.ZERO);
            coinThumb.setTurnover(BigDecimal.ZERO);

            // Process all K-lines to build the thumb
            for (KLine kline : lines) {
                if (kline.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                // Set open price if not set yet
                if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) == 0) {
                    coinThumb.setOpen(kline.getOpenPrice());
                }

                // Update high price if the current is higher
                if (coinThumb.getHigh().compareTo(kline.getHighestPrice()) < 0) {
                    coinThumb.setHigh(kline.getHighestPrice());
                }

                // Update low price if current is lower and valid
                if (kline.getLowestPrice().compareTo(BigDecimal.ZERO) > 0 &&
                        (coinThumb.getLow().compareTo(BigDecimal.ZERO) == 0 ||
                                coinThumb.getLow().compareTo(kline.getLowestPrice()) > 0)) {
                    coinThumb.setLow(kline.getLowestPrice());
                }

                // Update close price if valid
                if (kline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
                    coinThumb.setClose(kline.getClosePrice());
                }

                // Accumulate volume and turnover
                coinThumb.setVolume(coinThumb.getVolume().add(kline.getVolume()));
                coinThumb.setTurnover(coinThumb.getTurnover().add(kline.getTurnover()));
            }

            // Calculate change and percentage
            BigDecimal change = coinThumb.getClose().subtract(coinThumb.getOpen());
            coinThumb.setChange(change);

            // Calculate percentage change if open price is valid
            if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) > 0) {
                coinThumb.setChg(change.divide(coinThumb.getOpen(), SCALE, RoundingMode.UP));
            }
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Reset CoinThumb at 00:00:00 for the new day
     */
    @Override
    public void resetThumb() {
        logger.info("Resetting coin thumb for {}", symbol);
        try {
            thumbLock.lock();
            // Save the last day's closing price
            BigDecimal lastClose = coinThumb.getClose();

            // Reset values for the new day
            coinThumb.setOpen(BigDecimal.ZERO);
            coinThumb.setHigh(BigDecimal.ZERO);
            coinThumb.setLow(BigDecimal.ZERO);
            coinThumb.setChg(BigDecimal.ZERO);
            coinThumb.setChange(BigDecimal.ZERO);

            // Set yesterday's closing price
            coinThumb.setLastDayClose(lastClose);
        } finally {
            thumbLock.unlock();
        }
    }

    @Override
    public void setExchangeRate(CoinExchangeRate coinExchangeRate) {
        this.coinExchangeRate = coinExchangeRate;
    }

    /**
     * Initializes USD exchange rate for the coin
     */
    @Override
    public void initializeUsdRate() {
        if (coinExchangeRate == null) {
            logger.warn("Cannot initialize USD rate: coinExchangeRate is null for {}", symbol);
            return;
        }

        try {
            thumbLock.lock();
            // Get base coin USD rate
            BigDecimal baseUsdRate = coinExchangeRate.getUsdRate(baseCoin);
            coinThumb.setBaseUsdRate(baseUsdRate);

            // Calculate and set USD rate for this coin
            BigDecimal closePrice = coinThumb.getClose();
            BigDecimal usdRate = closePrice.multiply(baseUsdRate);
            coinThumb.setUsdRate(usdRate);

            logger.debug("Initialized USD rate for {}: base={}, close={}, usdRate={}",
                    symbol, baseUsdRate, closePrice, usdRate);
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Sets whether processing should be halted
     *
     * @param status True to halt processing, false to enable it
     */
    @Override
    public void setIsHalt(boolean status) {
        this.isHalt = status;
    }

    /**
     * Processes a list of trades, updating K-lines and thumbs
     * Now uses RealtimeKlineProcessor by default
     *
     * @param trades The list of trades to process
     */
    @Override
    public void process(List<ExchangeTrade> trades) {
        if (isHalt) {
            logger.debug("Processing is halted for {}", symbol);
            return;
        }

        if (trades == null || trades.isEmpty()) {
            return;
        }

        try {
            realtimeKlineProcessor.processTrades(trades);
            logger.debug("Processed {} trades for real-time K-line updates in symbol: {}", trades.size(), symbol);
        } catch (Exception e) {
            logger.error("Error processing real-time K-line updates for symbol {}: {}", symbol, e.getMessage(), e);
            // Continue processing even if K-line processing fails
        }


        for (ExchangeTrade exchangeTrade : trades) {
            // Process today's overview information
            if (logger.isDebugEnabled()) {
                logger.debug("Processing overview information for {} with trade: {}",
                        symbol, exchangeTrade.getPrice());
            }

            // Update the thumb with the latest trade
            handleThumb(exchangeTrade);

            // Store and push transaction information
            handleTradeStorage(exchangeTrade);
        }
    }

    /**
     * Stores and pushes trade information using the MarketHandlerService
     *
     * @param exchangeTrade The trade data to store
     */
    public void handleTradeStorage(ExchangeTrade exchangeTrade) {
        if (marketHandlerService == null) {
            return;
        }

        marketHandlerService.handleTrade(symbol, exchangeTrade, coinThumb);
    }

    /**
     * Stores and pushes K-line information using the MarketHandlerService
     * Delegates to KlineProcessorAdapter
     *
     * @param kLine The K-line data to store
     */
    public void handleKLineStorage(KLine kLine) {
        if (klineAdapter != null) {
            klineAdapter.handleKLineStorage(kLine);
        } else {
            logger.error("Cannot handle K-line storage: KlineProcessorAdapter is null for {}", symbol);
        }
    }

    /**
     * Updates the coin thumb with the latest trade data
     *
     * @param exchangeTrade The trade data
     */
    public void handleThumb(ExchangeTrade exchangeTrade) {
        if (logger.isDebugEnabled()) {
            logger.debug("Updating thumb for {}", symbol);
        }

        try {
            thumbLock.lock();

            BigDecimal tradePrice = exchangeTrade.getPrice();
            BigDecimal tradeAmount = exchangeTrade.getAmount();

            // Set open price if not set yet
            if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) == 0) {
                coinThumb.setOpen(tradePrice);
            }

            // Update high price
            coinThumb.setHigh(tradePrice.max(coinThumb.getHigh()));

            // Update low price
            if (coinThumb.getLow().compareTo(BigDecimal.ZERO) == 0) {
                coinThumb.setLow(tradePrice);
            } else {
                coinThumb.setLow(tradePrice.min(coinThumb.getLow()));
            }

            // Update close price
            coinThumb.setClose(tradePrice);

            // Update volume and turnover
            coinThumb.setVolume(coinThumb.getVolume().add(tradeAmount).setScale(SCALE, RoundingMode.UP));
            BigDecimal turnover = tradePrice.multiply(tradeAmount).setScale(SCALE, RoundingMode.UP);
            coinThumb.setTurnover(coinThumb.getTurnover().add(turnover));

            // Calculate and update change and percentage
            BigDecimal change = coinThumb.getClose().subtract(coinThumb.getOpen());
            coinThumb.setChange(change);

            if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) > 0) {
                coinThumb.setChg(change.divide(coinThumb.getOpen(), SCALE, RoundingMode.UP));
            }

            // Update USD rates
            if (coinExchangeRate != null) {
                BigDecimal baseUsdRate = coinExchangeRate.getUsdRate(baseCoin);
                coinThumb.setBaseUsdRate(baseUsdRate);

                if ("USDT".equalsIgnoreCase(baseCoin)) {
                    coinThumb.setUsdRate(tradePrice);
                } else {
                    coinThumb.setUsdRate(tradePrice.multiply(baseUsdRate));
                }

                if (logger.isDebugEnabled()) {
                    logger.debug("Updated USD rate for {}: {}", symbol, coinThumb.getUsdRate());
                }
            }

            if (logger.isTraceEnabled()) {
                logger.trace("Updated thumb for {}: {}", symbol, coinThumb);
            }
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Sets the MarketHandlerService to process and store market data
     *
     * @param marketHandlerService The MarketHandlerService to set
     */
    @Override
    public void setMarketHandlerService(MarketHandlerService marketHandlerService) {
        this.marketHandlerService = marketHandlerService;
        logger.info("MarketHandlerService set for processor {}", symbol);

        // Initialize RealtimeKlineProcessor for real-time K-line processing
        if (marketHandlerService != null) {
            // Check if MarketService is available
            if (service != null) {
                // Create RealtimeKlineProcessor with MarketService for historical data initialization
                this.realtimeKlineProcessor = new RealtimeKlineProcessor(symbol, marketHandlerService, service, redisTemplate);
                logger.info("RealtimeKlineProcessor initialized with MarketService for symbol: {}", symbol);
            } else {
                // MarketService is not available yet, create with empty initialization for now
                // It will be recreated with MarketService when setMarketService is called
                logger.info("Creating RealtimeKlineProcessor without MarketService for now (will be recreated when MarketService is set): {}", symbol);
                this.realtimeKlineProcessor = new RealtimeKlineProcessor(symbol, marketHandlerService, redisTemplate);
            }
        }

        // Also set MarketHandlerService on KlineAdapter if it exists (for backward compatibility)
        if (klineAdapter != null) {
            klineAdapter.setMarketHandlerService(marketHandlerService);
            logger.info("MarketHandlerService set on KlineAdapter during MarketHandlerService initialization for {}", symbol);
        }
    }

    /**
     * Gets the current coin thumb (market overview)
     *
     * @return The current coin thumb
     */
    @Override
    public CoinThumb getThumb() {
        return coinThumb;
    }

    /**
     * Sets the market service for database operations
     *
     * @param service The market service to use
     */
    @Override
    public void setMarketService(MarketService service) {
        this.service = service;

        // Initialize the KlineProcessorAdapter with dependencies
        if (klineAdapter != null) {
            klineAdapter.initialize(
                    () -> coinThumb,
                    () -> thumbLock,
                    service
            );

            // Set MarketHandlerService if it's already available
            if (marketHandlerService != null) {
                klineAdapter.setMarketHandlerService(marketHandlerService);
                logger.info("MarketHandlerService set on KlineAdapter during MarketService initialization for {}", symbol);
            }
        }

        // If MarketHandlerService was set before MarketService, we need to initialize or reinitialize
        // the RealtimeKlineProcessor with the MarketService to load historical data
        if (marketHandlerService != null) {
            if (realtimeKlineProcessor == null) {
                // Create new RealtimeKlineProcessor with MarketService
                this.realtimeKlineProcessor = new RealtimeKlineProcessor(symbol, marketHandlerService, service, redisTemplate);
                logger.info("RealtimeKlineProcessor initialized with MarketService during MarketService initialization for {}", symbol);
            } else {
                // RealtimeKlineProcessor was already created without MarketService
                // We should recreate it with MarketService to load historical data
                logger.info("Recreating RealtimeKlineProcessor with MarketService for {}", symbol);

                // Stop the existing empty K-line generator
                realtimeKlineProcessor.stopEmptyKlineGenerator();

                // Create new RealtimeKlineProcessor with MarketService
                this.realtimeKlineProcessor = new RealtimeKlineProcessor(symbol, marketHandlerService, service, redisTemplate);
                logger.info("RealtimeKlineProcessor recreated with MarketService for {}", symbol);
            }
        }
    }

    // determineRangeUnit method has been moved to KlineProcessorAdapter

    /**
     * A more efficient way to process weekly and monthly K-lines by aggregating daily K-lines
     * Delegates to KlineProcessorAdapter
     *
     * @param kline    The K-line to populate
     * @param fromTime The start time
     * @param endTime  The end time
     * @param field    The calendar field
     */
    public void processKline(KLine kline, long fromTime, long endTime, int field) {
        if (klineAdapter != null) {
            klineAdapter.processKline(kline, fromTime, endTime, field);
        } else {
            logger.error("Cannot process K-line: KlineProcessorAdapter is null for {}", symbol);
        }
    }

    @Override
    public KlineProcessorAdapter getKlineAdapter() {
        return klineAdapter;
    }

    /**
     * Push current K-line updates for all supported timeframes when a trade occurs
     * Delegates to KlineProcessorAdapter
     *
     * @param exchangeTrade The trade that triggered the update
     */
    @SuppressWarnings("all")
    private void pushCurrentKLineForAllTimeframes(ExchangeTrade exchangeTrade) {
        if (klineAdapter != null) {
            klineAdapter.pushCurrentKLineForAllTimeframes(exchangeTrade);
        } else {
            logger.error("Cannot push current K-line updates: KlineProcessorAdapter is null for {}", symbol);
        }
    }

    // Kline-related methods have been moved to KlineProcessorAdapter

    /**
     * Validates that all required dependencies are properly initialized
     * This helps detect configuration issues early
     */
    @SuppressWarnings("all")
    public void validateInitialization() {
        List<String> missingDependencies = new ArrayList<>();

        if (marketHandlerService == null) {
            missingDependencies.add("MarketHandlerService");
        }

        if (service == null) {
            missingDependencies.add("MarketService");
        }

        if (klineAdapter == null) {
            missingDependencies.add("KlineProcessorAdapter");
        } else {
            // Check if KlineAdapter has its dependencies
            try {
                KLine testKline = klineAdapter.getKLine();
                // If we can get a kline, the adapter is likely properly initialized
            } catch (Exception e) {
                missingDependencies.add("KlineProcessorAdapter initialization");
            }
        }

        if (!missingDependencies.isEmpty()) {
            String errorMessage = String.format(
                    "DefaultCoinProcessor for symbol '%s' is missing dependencies: %s",
                    symbol, String.join(", ", missingDependencies)
            );
            logger.error(errorMessage);
            throw new IllegalStateException(errorMessage);
        }

        logger.info("DefaultCoinProcessor for symbol '{}' is properly initialized", symbol);
    }

    /**
     * Clean up resources when the processor is no longer needed
     * This should be called when the processor is being shut down or removed
     */
    public void cleanup() {
        logger.info("Cleaning up resources for DefaultCoinProcessor: {}", symbol);

        // Stop the empty K-line generator in RealtimeKlineProcessor
        if (realtimeKlineProcessor != null) {
            try {
                realtimeKlineProcessor.stopEmptyKlineGenerator();
                logger.info("Stopped empty K-line generator for symbol: {}", symbol);
            } catch (Exception e) {
                logger.error("Error stopping empty K-line generator for symbol: {}: {}",
                        symbol, e.getMessage(), e);
            }
        }

        // Add any other cleanup logic here

        logger.info("Cleanup completed for DefaultCoinProcessor: {}", symbol);
    }
}

package com.icetea.lotus.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import com.icetea.lotus.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Real-time K-line processor for in-memory K-line updates
 * This processor handles live K-line data for WebSocket streaming via WebsocketMarketHandler
 * without persisting to database (persistence is handled by HistoricalKlineProcessor via MongoMarketHandler)
 */
@Slf4j
public class RealtimeKlineProcessor {

    private final String symbol;
    private final MarketHandlerService marketHandlerService;
    private final MarketService marketService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper = new  ObjectMapper();

    // In-memory cache for current K-lines by period
    private final Map<String, KLine> currentKlines = new ConcurrentHashMap<>();

    // Track the last time a K-line was pushed for each period
    private final Map<String, Long> lastPushTimes = new ConcurrentHashMap<>();

    // Scheduled executor for generating empty K-lines
    private ScheduledExecutorService scheduler;

    // Supported periods for real-time updates
    private static final String[] SUPPORTED_PERIODS = {
            CommonConstants.PERIOD.ONE_MIN, CommonConstants.PERIOD.FIVE_MIN, CommonConstants.PERIOD.FIFTEEN_MIN, CommonConstants.PERIOD.THIRTY_MIN,
            CommonConstants.PERIOD.ONE_HOUR, CommonConstants.PERIOD.FOUR_HOUR, CommonConstants.PERIOD.ONE_DAY, CommonConstants.PERIOD.ONE_WEEK
    };

    // Interval for generating empty K-lines (in milliseconds)
    private static final long EMPTY_KLINE_GENERATION_INTERVAL = 10000; // 10 seconds

    /**
     * Constructor with MarketService for historical data initialization
     */
    public RealtimeKlineProcessor(String symbol, MarketHandlerService marketHandlerService, MarketService marketService, RedisTemplate<String, Object> redisTemplate) {
        this.symbol = symbol;
        this.marketHandlerService = marketHandlerService;
        this.marketService = marketService;
        this.redisTemplate = redisTemplate;

        if (marketService != null) {
            // Initialize with historical data if MarketService is available
            initializeCurrentKlinesFromHistory();
        } else {
            // Fall back to empty initialization if MarketService is not available
            log.warn("MarketService is null, initializing with empty K-lines for symbol: {}", symbol);
            initializeCurrentKlines();
        }

        initializeLastPushTimes();
        startEmptyKlineGenerator();
    }

    /**
     * Constructor without MarketService (for backward compatibility)
     * This will initialize with empty K-lines
     */
    public RealtimeKlineProcessor(String symbol, MarketHandlerService marketHandlerService, RedisTemplate<String, Object> redisTemplate) {
        this.symbol = symbol;
        this.marketHandlerService = marketHandlerService;
        this.redisTemplate = redisTemplate;
        this.marketService = null;

        // Initialize with empty K-lines
        initializeCurrentKlines();
        initializeLastPushTimes();
        startEmptyKlineGenerator();

        log.warn("Created RealtimeKlineProcessor without MarketService, using empty K-lines for symbol: {}", symbol);
    }

    /**
     * Start the scheduled task to generate empty K-lines
     * This ensures continuous K-line generation, especially for the 1m timeframe
     * when there are no trades, preventing gaps in TradingView charts
     */
    private void startEmptyKlineGenerator() {
        log.info("Starting empty K-line generator for symbol: {}", symbol);
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r);
            thread.setName("EmptyKlineGenerator-" + symbol);
            thread.setDaemon(true); // Allow JVM to exit if this thread is running
            return thread;
        });

        scheduler.scheduleAtFixedRate(
                this::generateEmptyKlines,
                0, // Start immediately
                EMPTY_KLINE_GENERATION_INTERVAL,
                TimeUnit.MILLISECONDS
        );

        log.info("Empty K-line generator started for symbol: {}", symbol);
    }

    /**
     * Stop the scheduled task to generate empty K-lines
     * This should be called when the processor is no longer needed
     */
    public void stopEmptyKlineGenerator() {
        if (scheduler != null && !scheduler.isShutdown()) {
            log.info("Stopping empty K-line generator for symbol: {}", symbol);
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                log.info("Empty K-line generator stopped for symbol: {}", symbol);
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
                log.error("Error stopping empty K-line generator for symbol: {}: {}",
                        symbol, e.getMessage(), e);
            }
        }
    }

    /**
     * Initialize current K-lines from historical data for all supported periods
     * This ensures that when the service starts up, we have the latest K-line data
     * from the database for each period, preventing gaps in TradingView charts
     */
    private void initializeCurrentKlinesFromHistory() {
        log.info("Initializing current K-lines from historical data for symbol: {}", symbol);

        for (String period : SUPPORTED_PERIODS) {
            try {
                // Try to get the latest K-line from the database for this period
                KLine latestKline = marketService.findKlineLatest(symbol, period);

                if (latestKline != null && latestKline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
                    // Create a new K-line for the current period with data from the latest historical K-line
                    long currentTime = System.currentTimeMillis();
                    long periodStartTime = DateUtil.alignTimeToPeriod(currentTime, period);

                    KLine currentKline = new KLine();
                    currentKline.setPeriod(period);
                    currentKline.setTime(periodStartTime);
                    currentKline.setSymbol(symbol);

                    // Use the close price from the latest historical K-line for price continuity
                    BigDecimal closePrice = latestKline.getClosePrice();
                    currentKline.setOpenPrice(closePrice);
                    currentKline.setHighestPrice(closePrice);
                    currentKline.setLowestPrice(closePrice);
                    currentKline.setClosePrice(closePrice);

                    // Set volume, turnover, and count to zero (no trades yet in this period)
                    currentKline.setVolume(BigDecimal.ZERO);
                    currentKline.setTurnover(BigDecimal.ZERO);
                    currentKline.setCount(0);
                    currentKline.setIncomplete(true);

                    // Add to current K-lines map
                    currentKlines.put(period, currentKline);

                    log.info("Initialized current K-line for period {} with historical data, close price: {}",
                            period, closePrice);
                } else {
                    // If no historical data or invalid close price, create an empty K-line
                    currentKlines.put(period, createEmptyKline(period));
                    log.warn("No valid historical data found for period {}, using empty K-line", period);
                }
            } catch (Exception e) {
                // If there's an error, fall back to an empty K-line
                currentKlines.put(period, createEmptyKline(period));
                log.error("Error initializing current K-line for period {}: {}", period, e.getMessage(), e);
            }
        }

        log.info("Completed initialization of current K-lines from historical data for symbol: {}", symbol);
    }

    /**
     * Initialize current K-lines with empty values (fallback method)
     */
    private void initializeCurrentKlines() {
        for (String period : SUPPORTED_PERIODS) {
            currentKlines.put(period, createEmptyKline(period));
        }
        log.info("Initialized real-time K-line processor with empty K-lines for symbol: {}", symbol);
    }

    /**
     * Initialize last push times for all supported periods
     */
    private void initializeLastPushTimes() {
        long currentTime = System.currentTimeMillis();
        for (String period : SUPPORTED_PERIODS) {
            lastPushTimes.put(period, currentTime);
        }
        log.info("Initialized last push times for symbol: {}", symbol);
    }

    /**
     * Process a trade and update all current K-lines
     * This method updates in-memory K-line data and pushes real-time updates
     *
     * @param trades List of trades to process
     */
    public void processTrades(List<ExchangeTrade> trades) {
        if (trades == null || trades.isEmpty()) {
            return;
        }

        for (ExchangeTrade trade : trades) {
            processSingleTrade(trade);
        }
    }

    /**
     * Process a single trade and update current K-lines for all periods
     *
     * @param trade The trade to process
     */
    private void processSingleTrade(ExchangeTrade trade) {
        long tradeTime = trade.getTime();

        for (String period : SUPPORTED_PERIODS) {
            updateCurrentKlineForPeriod(period, trade, tradeTime);
        }
    }

    /**
     * Update current K-line for a specific period
     *
     * @param period    The time period
     * @param trade     The trade data
     * @param tradeTime The trade timestamp
     */
    @SuppressWarnings("java:S125")
    private void updateCurrentKlineForPeriod(String period, ExchangeTrade trade, long tradeTime) {
        long periodStartTime = DateUtil.alignTimeToPeriod(tradeTime, period);
//        KLine currentKline;
//        currentKline = getKlineFromRedis(symbol, period);
//        if (currentKline == null) {
//            currentKline = currentKlines.get(period);
//        }
        KLine currentKline = currentKlines.get(period);
        // Check if we need to start a new period
        if (currentKline == null || currentKline.getTime() != periodStartTime) {
            // Create a new K-line for the new period
            currentKline = createKlineForPeriod(period, periodStartTime, trade);
            currentKlines.put(period, currentKline);
            log.debug("Created new real-time K-line for period {} at time {}", period, periodStartTime);
        } else {
            // Update existing K-line
            updateKlineWithTrade(currentKline, trade);
        }

        // Mark as incomplete (real-time data)
        currentKline.setIncomplete(true);

        // Push real-time update via WebSocket
        pushCurrentKlineUpdate(currentKline);
    }

    @SuppressWarnings("java:S1144")
    private KLine getKlineFromRedis(String symbol, String period){
        try{
            String redisKey = String.format(CommonConstants.CURRENT_KLINE_KEY_REDIS, symbol, period);
            Object dataRedis =  redisTemplate.opsForValue().get(redisKey);
            return objectMapper.convertValue(dataRedis, KLine.class);
        }catch (Exception ex){
            log.error("getKlineFromRedis ERROR: ", ex);
            return null;
        }
    }

    /**
     * Create a new K-line for a specific period and trade
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @param trade           The first trade in the period
     * @return The created K-line
     */
    private KLine createKlineForPeriod(String period, long periodStartTime, ExchangeTrade trade) {
        KLine kline = new KLine();
        kline.setPeriod(period);
        kline.setTime(periodStartTime);
        kline.setSymbol(symbol);

        BigDecimal tradePrice = trade.getPrice();
        BigDecimal tradeAmount = trade.getAmount();

        // FIX: Use previous period's close price for opening price continuity
        BigDecimal openPrice = findLatestNonZeroClosePrice(period);
        if (openPrice == null || openPrice.compareTo(BigDecimal.ZERO) <= 0) {
            // Fallback to trade price if no previous close price available
            openPrice = tradePrice;
            log.warn("No previous close price found for period {}, using trade price as open", period);
        }

        kline.setOpenPrice(openPrice);
        // Consider both open and trade price for high/low calculation
        kline.setHighestPrice(tradePrice.max(openPrice));
        kline.setLowestPrice(tradePrice.min(openPrice));
        kline.setClosePrice(tradePrice);
        kline.setVolume(tradeAmount);
        kline.setTurnover(tradePrice.multiply(tradeAmount));
        kline.setCount(1);
        kline.setIncomplete(true);

        return kline;
    }

    /**
     * Update an existing K-line with new trade data
     *
     * @param kline The K-line to update
     * @param trade The trade data
     */
    private void updateKlineWithTrade(KLine kline, ExchangeTrade trade) {
        BigDecimal tradePrice = trade.getPrice();
        BigDecimal tradeAmount = trade.getAmount();

        // Update close price (always the latest trade price)
        kline.setClosePrice(tradePrice);

        // Update high and low prices
        if (tradePrice.compareTo(kline.getHighestPrice()) > 0) {
            kline.setHighestPrice(tradePrice);
        }
        if (tradePrice.compareTo(kline.getLowestPrice()) < 0) {
            kline.setLowestPrice(tradePrice);
        }

        // Update volume, turnover, and count
        kline.setVolume(kline.getVolume().add(tradeAmount));
        kline.setTurnover(kline.getTurnover().add(tradePrice.multiply(tradeAmount)));
        kline.setCount(kline.getCount() + 1);
    }

    /**
     * Push current K-line update via WebSocket for real-time streaming
     *
     * @param kline The K-line to push
     */
    private void pushCurrentKlineUpdate(KLine kline) {
        if (marketHandlerService != null) {
            try {
                // Push current (incomplete) K-line update via WebSocket only
                // WebsocketMarketHandler is responsible for real-time data
                pushCurrentKlineToRedis(kline);
                marketHandlerService.handleCurrentKLine(symbol, kline);

                // FIX: Update lastPushTimes for trade-based updates too
                // This prevents the empty kline generator from incorrectly overwriting active trading data
                lastPushTimes.put(kline.getPeriod(), System.currentTimeMillis());

                log.debug("Pushed real-time K-line update for {} period {}", symbol, kline.getPeriod());
            } catch (Exception e) {
                log.error("Error pushing real-time K-line update for {} period {}: {}",
                        symbol, kline.getPeriod(), e.getMessage(), e);
            }
        }
    }

    private void pushCurrentKlineToRedis(KLine kline) {
        try{
            String key = String.format(CommonConstants.CURRENT_KLINE_KEY_REDIS, symbol, kline.getPeriod());
            redisTemplate.opsForValue().set(key, kline);
        }catch (Exception e){
            log.error("ERROR pushCurrentKlineToRedis for {} ", symbol, kline.getPeriod(), e);
        }
    }

    /**
     * Create an empty K-line for initialization
     *
     * @param period The time period
     * @return Empty K-line
     */
    private KLine createEmptyKline(String period) {
        KLine kline = new KLine();
        kline.setPeriod(period);
        kline.setSymbol(symbol);
        kline.setOpenPrice(BigDecimal.ZERO);
        kline.setHighestPrice(BigDecimal.ZERO);
        kline.setLowestPrice(BigDecimal.ZERO);
        kline.setClosePrice(BigDecimal.ZERO);
        kline.setVolume(BigDecimal.ZERO);
        kline.setTurnover(BigDecimal.ZERO);
        kline.setCount(0);
        kline.setIncomplete(true);
        return kline;
    }

    /**
     * Align timestamp to period boundary
     *
     * @param timestamp The timestamp to align
     * @param period    The period to align to
     * @return The aligned timestamp
     */
    public long alignTimeToPeriod(long timestamp, String period) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);

        // Reset seconds and milliseconds
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        switch (period) {
            case "1min":
                // Already aligned to a minute
                break;
            case "5min":
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 5) * 5);
                break;
            case "15min":
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 15) * 15);
                break;
            case "30min":
                calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 30) * 30);
                break;
            case "1hour":
                calendar.set(Calendar.MINUTE, 0);
                break;
            case "4hour":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, (calendar.get(Calendar.HOUR_OF_DAY) / 4) * 4);
                break;
            case "1day":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                break;
            case "1week":
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                break;
            default:
                break;
        }

        return calendar.getTimeInMillis();
    }

    /**
     * Get current K-line for a specific period
     *
     * @param period The time period
     * @return Current K-line or null if not found
     */
    public KLine getCurrentKline(String period) {
        return currentKlines.get(period);
    }

    /**
     * Get all current K-lines
     *
     * @return Map of period to current K-line
     */
    public Map<String, KLine> getAllCurrentKlines() {
        return new ConcurrentHashMap<>(currentKlines);
    }

    /**
     * Generate and push empty K-lines for periods with no trades
     * This method should be called at regular intervals to ensure continuous K-line generation
     * especially for the 1m timeframe when there are no trades
     */
    public void generateEmptyKlines() {
        long currentTime = System.currentTimeMillis();

        for (String period : SUPPORTED_PERIODS) {
            // Get the current period start time
            long periodStartTime = DateUtil.alignTimeToPeriod(currentTime, period);

            // Get the last push time for this period
            Long lastPushTime = lastPushTimes.get(period);
            if (lastPushTime == null) {
                lastPushTime = 0L;
            }

            // Get the current K-line for this period
            KLine currentKline = currentKlines.get(period);

            // Check if we need to generate a new K-line for this period
            // We need to generate a new K-line if:
            // 1. The current K-line is null, or
            // 2. The current K-line's time is different from the current period start time AND
            //    we haven't pushed a K-line recently (to avoid race condition with trade processing)
            long periodDuration = getPeriodDuration(period);
            long timeSinceLastPush = currentTime - lastPushTime;

            boolean needsNewKline = currentKline == null ||
                    (currentKline.getTime() != periodStartTime && timeSinceLastPush >= periodDuration);

            if (needsNewKline) {
                // Create a new K-line for this period
                KLine newKline = createEmptyKlineWithContinuity(period, periodStartTime);

                // Update the current K-lines map
                currentKlines.put(period, newKline);

                //push new kline to redis
                pushCurrentKlineToRedis(newKline);
                // Push the new K-line via WebSocket
                pushCurrentKlineUpdate(newKline);

                // Update the last push time for this period
                lastPushTimes.put(period, currentTime);

                log.debug("Generated empty K-line for period {} at time {}", period, periodStartTime);
            }
        }
    }

    /**
     * Create an empty K-line with price continuity from the previous K-line
     *
     * @param period          The time period
     * @param periodStartTime The start time of the period
     * @return Empty K-line with price continuity
     */
    private KLine createEmptyKlineWithContinuity(String period, long periodStartTime) {
        KLine kline = new KLine();
        kline.setPeriod(period);
        kline.setTime(periodStartTime);
        kline.setSymbol(symbol);

        // Get the previous K-line for this period to maintain price continuity
        KLine previousKline = currentKlines.get(period);

        // Find the latest non-zero close price from any previous K-line
        BigDecimal latestClosePrice = findLatestNonZeroClosePrice(period);

        if (latestClosePrice != null && latestClosePrice.compareTo(BigDecimal.ZERO) > 0) {
            // Use the latest non-zero close price for continuity
            log.debug("Using latest non-zero close price {} for empty K-line of period {}", latestClosePrice, period);
            kline.setOpenPrice(latestClosePrice);
            kline.setHighestPrice(latestClosePrice);
            kline.setLowestPrice(latestClosePrice);
            kline.setClosePrice(latestClosePrice);
        } else if (previousKline != null && previousKline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
            // Fallback to previous K-line's close price if available
            BigDecimal previousClose = previousKline.getClosePrice();
            log.debug("Using previous K-line close price {} for empty K-line of period {}", previousClose, period);
            kline.setOpenPrice(previousClose);
            kline.setHighestPrice(previousClose);
            kline.setLowestPrice(previousClose);
            kline.setClosePrice(previousClose);
        } else {
            // No previous K-line or its close price is zero, use zero prices as last resort
            log.warn("No valid close price found for empty K-line of period {}, using zeros", period);
            kline.setOpenPrice(BigDecimal.ZERO);
            kline.setHighestPrice(BigDecimal.ZERO);
            kline.setLowestPrice(BigDecimal.ZERO);
            kline.setClosePrice(BigDecimal.ZERO);
        }

        // Set volume, turnover, and count to zero (no trades)
        kline.setVolume(BigDecimal.ZERO);
        kline.setTurnover(BigDecimal.ZERO);
        kline.setCount(0);
        kline.setIncomplete(true);

        return kline;
    }

    /**
     * Get the duration of a period in milliseconds
     *
     * @param period The time period
     * @return The period duration in milliseconds
     */
    private long getPeriodDuration(String period) {
        return switch (period) {
            case "1min" -> 60 * 1000; // 1 minute
            case "5min" -> 5 * 60 * 1000; // 5 minutes
            case "15min" -> 15 * 60 * 1000; // 15 minutes
            case "30min" -> 30 * 60 * 1000; // 30 minutes
            case "1hour" -> 60 * 60 * 1000; // 1 hour
            case "4hour" -> 4 * 60 * 60 * 1000; // 4 hours
            case "1day" -> 24 * 60 * 60 * 1000; // 1 day
            case "1week" -> 7 * 24 * 60 * 60 * 1000; // 1 week
            default -> 60 * 1000; // Default to 1 minute
        };
    }

    /**
     * Find the latest non-zero close price from any previous K-line for the given period
     * This is used to ensure price continuity for empty K-lines
     *
     * @param period The time period
     * @return The latest non-zero close price or null if not found
     */
    private BigDecimal findLatestNonZeroClosePrice(String period) {
        // First check the current K-line for this period
        KLine currentKline = currentKlines.get(period);
        if (currentKline != null && currentKline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
            return currentKline.getClosePrice();
        }

        // If no valid close price in current K-line, check other periods
        // Try to find a valid close price from smaller timeframes first
        for (String checkPeriod : SUPPORTED_PERIODS) {
            // Skip the current period as we already checked it
            if (checkPeriod.equals(period)) {
                continue;
            }

            KLine kline = currentKlines.get(checkPeriod);
            if (kline != null && kline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
                log.debug("Found non-zero close price {} in period {}", kline.getClosePrice(), checkPeriod);
                return kline.getClosePrice();
            }
        }

        // If still no valid close price, return null
        log.warn("No non-zero close price found in any period for symbol: {}", symbol);
        return null;
    }
}

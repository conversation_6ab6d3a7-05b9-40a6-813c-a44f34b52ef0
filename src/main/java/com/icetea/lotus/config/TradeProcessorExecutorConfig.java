package com.icetea.lotus.config;

import com.icetea.lotus.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Configuration class for trade processing executor service
 * This bean provides the ExecutorService used by ExchangeTradeConsumer for processing trades
 */
@Slf4j
@Configuration
public class TradeProcessorExecutorConfig {

    /**
     * Creates and configures the ExecutorService bean for trade processing
     * 
     * @return Configured ThreadPoolExecutor optimized for trade processing
     */
    @Bean(name = "tradeProcessorExecutor")
    public ExecutorService tradeProcessorExecutor() {
        log.info("Initializing trade processor executor with optimized configuration");
        
        return new ThreadPoolExecutor(
                30,                                    // corePoolSize - minimum threads
                100,                                   // maximumPoolSize - maximum threads
                60L, TimeUnit.SECONDS,                 // keepAliveTime - idle thread timeout
                new LinkedBlockingQueue<>(2048),       // workQueue - increased capacity
                r -> {                                 // threadFactory - custom thread naming
                    Thread t = new Thread(r, "trade-processor-" + System.currentTimeMillis());
                    t.setDaemon(false);
                    return t;
                },
                (r, executor) -> {                     // rejectedExecutionHandler - custom backpressure handling
                    log.warn("Trade processing queue is full. Current queue size: {}, active threads: {}, " +
                            "pool size: {}. Attempting to process trade synchronously as fallback.",
                            executor.getQueue().size(), executor.getActiveCount(), executor.getPoolSize());
                    try {
                        // Fallback: execute synchronously in current thread
                        r.run();
                        log.info("Successfully processed trade synchronously as fallback");
                    } catch (Exception e) {
                        log.error("Critical error: Failed to process trade even with synchronous fallback: {}", 
                                 e.getMessage(), e);
                        throw new BusinessException("Trade processing system overloaded");
                    }
                }
        );
    }
}
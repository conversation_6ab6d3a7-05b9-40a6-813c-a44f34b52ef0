package com.icetea.lotus.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * Thread Pool Configuration for the Market Service
 * <p>
 * This configuration creates a thread pool for executing asynchronous tasks,
 * particularly for processing cryptocurrency market data and generating K-lines.
 * <p>
 * The thread pool parameters are configurable via application properties:
 * - market.thread-pool.core-size: Number of core threads (default: 30)
 * - market.thread-pool.max-size: Maximum number of threads (default: 150)
 * - market.thread-pool.queue-capacity: Size of the task queue (default: 500)
 * - market.thread-pool.keep-alive-seconds: Thread idle timeout in seconds (default: 60)
 * - market.thread-pool.thread-name-prefix: Prefix for thread names (default: "Market-Processor-")
 */
@Configuration
@EnableAsync
public class ThreadPoolTaskConfig {

    @Value("${market.thread-pool.core-size:30}")
    private int corePoolSize;

    @Value("${market.thread-pool.max-size:150}")
    private int maxPoolSize;

    @Value("${market.thread-pool.keep-alive-seconds:60}")
    private int keepAliveTime;

    @Value("${market.thread-pool.queue-capacity:500}")
    private int queueCapacity;

    @Value("${market.thread-pool.thread-name-prefix:Market-Processor-}")
    private String threadNamePrefix;

    /**
     * Creates and configures the main task executor for the application.
     * <p>
     * This executor is used primarily for processing cryptocurrency market data,
     * including generating K-lines and handling trade events. The thread pool
     * is configured with:
     * <p>
     * - A core pool size that determines the number of threads to keep alive even when idle
     * - A maximum pool size that limits the total number of threads
     * - A queue capacity that determines how many tasks can be queued when all threads are busy
     * - A keep-alive time that determines how long excess idle threads will wait for new tasks
     * - A thread name prefix for easier identification in logs and thread dumps
     * - A rejection policy that makes the caller thread execute the task if the queue is full
     *
     * @return The configured ThreadPoolTaskExecutor
     */
    @Bean("lotusTaskExecutor")
    @Primary
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveTime);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

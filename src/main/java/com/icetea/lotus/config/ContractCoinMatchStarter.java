package com.icetea.lotus.config;

import com.icetea.lotus.client.Client;
import com.icetea.lotus.socket.client.WsClientHuobi;
import com.icetea.lotus.util.WebSocketConnectionManage;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

@RequiredArgsConstructor
public class ContractCoinMatchStarter implements ApplicationRunner {

    private final Client client;
    @Setter
    @Getter
    private Logger log = LoggerFactory.getLogger(ContractCoinMatchStarter.class);

    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        try {
            WebSocketConnectionManage.setClient(client);

            WsClientHuobi w = new WsClientHuobi();
            w.run();
        } catch (Exception e) {
            log.info("Unexpected error occurred", e);
        }
    }
}

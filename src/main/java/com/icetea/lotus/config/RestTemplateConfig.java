package com.icetea.lotus.config;

import com.icetea.lotus.config.oauth2.OAuth2RestTemplateFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate configuration for the market module
 * Uses the OAuth2RestTemplateFactory from the core module
 */
@Configuration
public class RestTemplateConfig {

    private final OAuth2RestTemplateFactory oauth2RestTemplateFactory;

    @Autowired
    public RestTemplateConfig(OAuth2RestTemplateFactory oauth2RestTemplateFactory) {
        this.oauth2RestTemplateFactory = oauth2RestTemplateFactory;
    }

    @Bean
    @LoadBalanced
    RestTemplate restTemplate() {
        // Create a RestTemplate with OAuth2 authentication using the default principal name
        // This allows using a single client principal for all client services
        return oauth2RestTemplateFactory.createOAuth2RestTemplate();
    }
}

package com.icetea.lotus.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

@Configuration
@EnableScheduling
@Slf4j
public class SchedulerConfig implements SchedulingConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar registrar) {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(2); // Allow up to 4 tasks to run concurrently
        scheduler.setThreadNamePrefix("my-scheduler-");
        scheduler.setErrorHandler(t -> log.error("Scheduled task error: {}", t.getMessage()));
        scheduler.initialize();

        registrar.setTaskScheduler(scheduler);
    }
}
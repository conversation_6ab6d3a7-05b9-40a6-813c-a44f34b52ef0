package com.icetea.lotus.config;

import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.processor.DefaultCoinProcessor;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

@Configuration
@Slf4j
public class ProcessorConfig {

    private final RedisTemplate<String, Object> redisTemplate;

    public ProcessorConfig(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Bean
    public CoinProcessorFactory processorFactory(MarketHandlerService marketHandlerService,
                                                 MarketService marketService,
                                                 CoinExchangeRate exchangeRate,
                                                 ExchangeCoinService coinService) {

        log.info("====initialized CoinProcessorFactory start==================================");

        CoinProcessorFactory factory = new CoinProcessorFactory();
        List<ExchangeCoin> coins = coinService.findAllEnabled();
        log.info("exchange-coin result:{}", coins);

        for (ExchangeCoin coin : coins) {
            CoinProcessor processor = new DefaultCoinProcessor(coin.getSymbol(), coin.getBaseSymbol(), redisTemplate);
            processor.setMarketHandlerService(marketHandlerService);
            processor.setMarketService(marketService);
            processor.setExchangeRate(exchangeRate);
            processor.getKlineAdapter().setMarketHandlerService(marketHandlerService);
            processor.getKlineAdapter().setIsStopKLine(false);

            // Validate that all dependencies are properly initialized
            if (processor instanceof DefaultCoinProcessor defaultCoinProcessor) {
                defaultCoinProcessor.validateInitialization();
            }

            factory.addProcessor(coin.getSymbol(), processor);
            log.info("new processor = {}", processor);
        }

        log.info("====initialized CoinProcessorFactory completed====");
        log.info("CoinProcessorFactory = {}", factory);
        exchangeRate.setCoinProcessorFactory(factory);
        return factory;
    }
}

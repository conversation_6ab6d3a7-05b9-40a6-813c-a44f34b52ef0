package com.icetea.lotus.netty;

import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

@Component
@Slf4j
public class ChannelSet {
    private Set<Channel> channelSetValue = new LinkedHashSet<>();

    public boolean addChannel(Channel channel) {
        log.info("new channel,id={}", channel.id());
        return this.channelSetValue.add(channel);
    }

    public boolean removeChannel(Channel channel) {
        Iterator<Channel> iterator = channelSetValue.iterator();
        while (iterator.hasNext()) {
            Channel item = iterator.next();
            if (item.id().asLongText().equalsIgnoreCase(channel.id().asLongText())) {
                iterator.remove();
                log.info("remove channel,id={}", channel.id());
                return true;
            }
        }
        return false;
    }

    public Set<Channel> getChannels() {
        return this.channelSetValue;
    }
}

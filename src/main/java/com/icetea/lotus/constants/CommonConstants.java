package com.icetea.lotus.constants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

@SuppressWarnings("java:S1117")
public class CommonConstants {

    private CommonConstants() {

    }

    public static final String COIN_PREFIX = "COIN:";
    public static final int COIN_EXPIRE_TIME = 300;
    public static final String PRICE = "price";
    public static final String SUCCESS_MESSAGE = "success";
    public static final String FUTURE_ORDER_BOOK_ITEM = "items";
    public static final String FUTURE_ORDER_BOOK_BID = "bid";
    public static final String FUTURE_ORDER_BOOK_ASK = "ask";
    public static final String FUTURE_ORDER_BOOK_SYMBOL = "symbol";

    public static final String FUTURE_MATCHING_ENGINE_BID = "bids";
    public static final String FUTURE_MATCHING_ENGINE_ASK = "asks";
    public static final String FUTURE_MATCHING_ENGINE_ORDERBOOKSNAPSHOT = "orderBookSnapshot";
    public static final String CURRENT_KLINE_KEY_REDIS = "CURRENT_KLINE_%s_%s";

    public static final String LIST_ENABLED_SYMBOL_KEY_REDIS = "LIST_ENABLED_SYMBOL11";
    public static final String MAX_LEVERAGE_KEY_REDIS = "MAX_LEVERAGE_MAP";

    public static final String SUFFIX_MARK_PRICE_KEY_REDIS = "_markPrice";
    public static final String SUFFIX_PAIR_DETAIL_KEY_REDIS = "_pairDetail";

    public static final BigDecimal DEFAULT_MAX_LEVERAGE = new BigDecimal(125L);
    public static final BigDecimal IMPACT_MARGIN_NOTIONAL_QUOTE_CURRENCY = new BigDecimal(200);
    public static final String LIST_PREMIUM_INDEX_KEY_REDIS = "LIST_PREMIUM_INDEX_%s";
    public static final String FUTURE_CONTRACT_COIN_INFO_KEY_REDIS = "FUTURE_CONTRACT_COIN_INFO_KEY_REDIS_%s";

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class RatesMap {
        public static final String CNY = "CNY";
        public static final BigDecimal CNY_VALUE = BigDecimal.valueOf(6.36);
        public static final String JPY = "JPY";
        public static final BigDecimal JPY_VALUE = BigDecimal.valueOf(6.36);
        public static final String TWD = "TWD";
        public static final BigDecimal TWD_VALUE = BigDecimal.valueOf(6.40);
        public static final String USD = "USD";
        public static final BigDecimal USD_VALUE = BigDecimal.valueOf(1.00);
        public static final String EUR = "EUR";
        public static final BigDecimal EUR_VALUE = BigDecimal.valueOf(0.91);
        public static final String HKD = "HKD";
        public static final BigDecimal HKD_VALUE = BigDecimal.valueOf(7.81);
        public static final String SGD = "SGD";
        public static final BigDecimal SGD_VALUE = BigDecimal.valueOf(1.36);
        public static final String INR = "INR";
        public static final BigDecimal INR_VALUE = BigDecimal.valueOf(82.34);
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class HeaderValue {
        public static final String APPLICATION_JSON = "application/json";
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class PERIOD {
        public static final String ONE_MIN = "1min";
        public static final String FIVE_MIN = "5min";
        public static final String FIFTEEN_MIN = "15min";
        public static final String THIRTY_MIN = "30min";
        public static final String ONE_HOUR = "1hour";
        public static final String FOUR_HOUR = "4hour";
        public static final String ONE_DAY = "1day";
        public static final String ONE_WEEK = "1week";
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class GetPremiumIndex {
        public static final String URL_BIT_GET_LAST_PRICE = "https://api.bitget.com/api/v2/spot/market/tickers?symbol=%s";
        public static final String URL_BINANCE_LAST_PRICE = "https://api.binance.com/api/v3/ticker/24hr?symbol=%s&type=FULL";
        public static final String URL_OKX_LAST_PRICE = "https://www.okx.com/api/v5/market/ticker?instId=%s";
        public static final String URL_GATE_IO_LAST_PRICE = "https://api.gateio.ws/api/v4/spot/tickers?currency_pair=%s";
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class GetPremiumIndexWebsocket {
        public static final String WEBSOCKET_BIT_GET_LAST_PRICE = "wss://ws.bitget.com/v2/ws/public";
        public static final String WEBSOCKET_BINANCE_LAST_PRICE = "wss://stream.binance.com:9443/ws/%s@ticker";
        public static final String WEBSOCKET_OKX_LAST_PRICE = "wss://ws.okx.com:8443/ws/v5/public";
        public static final String WEBSOCKET_GATE_IO_LAST_PRICE = "wss://api.gateio.ws/ws/v4/usdt";
    }

}

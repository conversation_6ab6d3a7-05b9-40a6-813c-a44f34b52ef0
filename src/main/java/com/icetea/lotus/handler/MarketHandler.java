package com.icetea.lotus.handler;

import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;

public interface MarketHandler {

    /**
     * Store transaction information
     * @param exchangeTrade
     */
    void handleTrade(String symbol, ExchangeTrade exchangeTrade, CoinThumb thumb);


    /**
     * Store K-line information
     *
     * @param kLine
     */
    void handleKLine(String symbol,KLine kLine);
}

package com.icetea.lotus.handler;

import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.job.ExchangePushJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class WebsocketMarketHandler implements MarketHandler {

    private final SimpMessagingTemplate messagingTemplate;

    private final ExchangePushJob pushJob;

    private final RedisTemplate<String, Object> redisTemplate;

    private final ExchangePushJob exchangePushJob;

    // Throttling mechanism to prevent WebSocket connection overload
    // Track last push time for each symbol-period combination
    private final Map<String, Long> lastCurrentKlinePushTimes = new ConcurrentHashMap<>();

    // Minimum interval between current K-line pushes (in milliseconds)
    // Different intervals for different periods to optimize performance
    private static final Map<String, Long> PUSH_INTERVALS = Map.of(
        "1min", 1000L,    // 1 second for 1min (high frequency)
        "5min", 2000L,    // 2 seconds for 5min (medium frequency) 
        "15min", 3000L,   // 3 seconds for 15min
        "30min", 5000L,   // 5 seconds for 30min
        "1hour", 10000L,  // 10 seconds for 1hour
        "4hour", 15000L,  // 15 seconds for 4hour
        "1day", 30000L,   // 30 seconds for 1day
        "1week", 60000L   // 1 minute for 1week
    );

    /**
     * Handles a trade event by processing and pushing the updated coin thumb information.
     *
     * @param symbol        The trading pair symbol associated with the trade.
     * @param exchangeTrade The exchange trade details for the trade event.
     * @param thumb         The updated coin thumb containing market summary information.
     */
    @Override
    @SneakyThrows
    @SuppressWarnings("java:S125")
    public void handleTrade(String symbol, ExchangeTrade exchangeTrade, CoinThumb thumb) {
        // Push abbreviation quotes
//        pushJob.addThumb(symbol, thumb);
        exchangePushJob.handlePushThumb(List.of(thumb), symbol);
    }

    /**
     * Handles the K-line event by processing and pushing the complete and backward-compatible K-line data
     * to the appropriate WebSocket topics.
     *
     * @param symbol The trading pair symbol associated with the K-line data.
     * @param kLine  The K-line data containing market statistics such as open price, highest price,
     *               lowest price, close price, volume, turnover, and more.
     */
    @Override
    public void handleKLine(String symbol, KLine kLine) {
        // Push complete K-line data with a period parameter
        String period = kLine.getPeriod();
        String completeTopic = "/topic/market/kline/" + symbol + "/" + period;

        // Create enhanced K-line data with symbol included
        Map<String, Object> klineData = createKLineData(symbol, kLine, false);

        log.debug("Pushing complete K-line to topic: {} for symbol: {}, period: {}", completeTopic, symbol, period);
        messagingTemplate.convertAndSend(completeTopic, klineData);

        // Also maintain backward compatibility with the old topic format
        messagingTemplate.convertAndSend("/topic/market/kline/" + symbol, kLine);
    }


    /**
     * Handles the current K-line event by creating K-line data with the symbol included,
     * marking it as incomplete, and pushing it to a WebSocket topic associated with the
     * trading symbol and period.
     * 
     * PERFORMANCE FIX: Implements throttling to prevent WebSocket connection overload
     * that was causing client UI connection drops, especially for high-frequency periods like 5min.
     *
     * @param symbol The trading pair symbol associated with the K-line data.
     * @param kLine  The K-line data containing market statistics such as open price,
     *               highest price, lowest price, close price, volume, turnover, and more.
     */
    @SuppressWarnings("java:S125")
    public void handleCurrentKLine(String symbol, KLine kLine) {
        String period = kLine.getPeriod();
        String throttleKey = symbol + "_" + period;
        long currentTime = System.currentTimeMillis();

        // Get the minimum interval for this period
//        Long minInterval = PUSH_INTERVALS.get(period);
//        if (minInterval == null) {
//            minInterval = 2000L; // Default 2 seconds for unknown periods
//        }
//
//        // Check if enough time has passed since the last push for this symbol-period
//        Long lastPushTime = lastCurrentKlinePushTimes.get(throttleKey);
//        if (lastPushTime != null && (currentTime - lastPushTime) < minInterval) {
//            // Skip this update to prevent connection overload
//            log.debug("Throttling current K-line update for {} period {} (last push: {}ms ago)",
//                     symbol, period, currentTime - lastPushTime);
//            return;
//        }

        // Update the last push time
        lastCurrentKlinePushTimes.put(throttleKey, currentTime);

        String currentTopic = "/topic/market/current-kline/" + symbol + "/" + period;

        // Create enhanced K-line data with symbol included and incomplete flag
        Map<String, Object> klineData = createKLineData(symbol, kLine, true);

        log.debug("Pushing current K-line to topic: {} for symbol: {}, period: {} (throttled)", 
                 currentTopic, symbol, period);
        messagingTemplate.convertAndSend(currentTopic, klineData);
    }

    /**
     * Create K-line data map with symbol and completion status
     *
     * @param symbol     The trading symbol
     * @param kLine      The K-line data
     * @param incomplete Whether this is an incomplete K-line
     * @return Map containing K-line data with additional metadata
     */
    private Map<String, Object> createKLineData(String symbol, KLine kLine, boolean incomplete) {
        Map<String, Object> data = new HashMap<>();
        data.put("symbol", symbol);
        data.put("time", kLine.getTime());
        data.put("period", kLine.getPeriod());
        data.put("openPrice", kLine.getOpenPrice());
        data.put("highestPrice", kLine.getHighestPrice());
        data.put("lowestPrice", kLine.getLowestPrice());
        data.put("closePrice", kLine.getClosePrice());
        data.put("volume", kLine.getVolume());
        data.put("turnover", kLine.getTurnover());
        data.put("count", kLine.getCount());
        data.put("incomplete", incomplete);
        return data;
    }
}

package com.icetea.lotus.handler;

import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class MongoMarketHandler implements MarketHandler {

    private final MongoTemplate mongoTemplate;

    @Override
    public void handleTrade(String symbol, ExchangeTrade exchangeTrade, CoinThumb thumb) {
        mongoTemplate.insert(exchangeTrade, "exchange_trade_" + symbol);
    }

    @Override
    public void handleKLine(String symbol, KLine kLine) {
        log.info("handleKline save kline in db: {}", kLine.toString());

        // ✅ CORRECT FIX: Use upsert to prevent duplicate records
        Query query = new Query(Criteria.where("time").is(kLine.getTime())
                .and("period").is(kLine.getPeriod())
                .and("symbol").is(symbol));

        Update update = new Update()
                .set("openPrice", kLine.getOpenPrice())
                .set("closePrice", kLine.getClosePrice())
                .set("highestPrice", kLine.getHighestPrice())
                .set("lowestPrice", kLine.getLowestPrice())
                .set("volume", kLine.getVolume())
                .set("turnover", kLine.getTurnover())
                .set("count", kLine.getCount())
                .set("incomplete", kLine.getIncomplete())
                .set("symbol", symbol);

        mongoTemplate.upsert(query, update, "exchange_kline_" + symbol + "_" + kLine.getPeriod());
        log.info("Upserted kline for symbol: {} at time: {}", symbol, kLine.getTime());
    }
}

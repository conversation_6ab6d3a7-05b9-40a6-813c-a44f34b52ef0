package com.icetea.lotus.controller;

import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/exchange-rate")
@RequiredArgsConstructor
public class ExchangeRateController {

    private final CoinExchangeRate coinExchangeRate;

    @PostMapping("usd/{coin}")
    public MessageResult getUsdExchangeRate(@PathVariable String coin) {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        BigDecimal latestPrice = coinExchangeRate.getUsdRate(coin);
        mr.setData(latestPrice.toString());
        return mr;
    }

    @PostMapping("usdtcny")
    public MessageResult getUsdtExchangeRate() {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        BigDecimal latestPrice = coinExchangeRate.getUsdtCnyRate();
        mr.setData(latestPrice.toString());
        return mr;
    }

    @PostMapping("cny/{coin}")
    public MessageResult getCnyExchangeRate(@PathVariable String coin) {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        BigDecimal latestPrice = coinExchangeRate.getCnyRate(coin);
        mr.setData(latestPrice.toString());
        return mr;
    }

    @PostMapping("all/{coin}")
    public MessageResult getAllExchangeRate(@PathVariable String coin) {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        Map<String, BigDecimal> ratesMap = coinExchangeRate.getAllRate(coin);
        mr.setData(ratesMap);
        return mr;
    }

    @PostMapping("jpy/{coin}")
    public MessageResult getJpyExchangeRate(@PathVariable String coin) {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        BigDecimal latestPrice = coinExchangeRate.getJpyRate(coin);
        mr.setData(latestPrice.toString());
        return mr;
    }

    @PostMapping("hkd/{coin}")
    public MessageResult getHkdExchangeRate(@PathVariable String coin) {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        BigDecimal latestPrice = coinExchangeRate.getHkdRate(coin);
        mr.setData(latestPrice.toString());
        return mr;
    }

    @PostMapping("usd-{unit}")
    public MessageResult getUsdCnyRate(@PathVariable String unit) {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        if ("CNY".equalsIgnoreCase(unit)) {
            mr.setData(coinExchangeRate.getUsdtCnyRate());
        } else if ("JPY".equalsIgnoreCase(unit)) {
            mr.setData(coinExchangeRate.getUsdJpyRate());
        } else if ("HKD".equalsIgnoreCase(unit)) {
            mr.setData(coinExchangeRate.getUsdHkdRate());
        } else {
            mr.setData(BigDecimal.ZERO);
        }
        return mr;
    }
}

package com.icetea.lotus.controller;

import com.icetea.lotus.component.PriceDataPoint;
import com.icetea.lotus.dto.ContractCoinInfo;
import com.icetea.lotus.service.ContractMarketService;
import com.icetea.lotus.service.OrderBookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Queue;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("contracts")
public class ContractMarketController {

    private final ContractMarketService contractMarketService;
    private final OrderBookService orderBookService;

    @GetMapping("symbol-thumb")
    public List<ContractCoinInfo> getSymbolInfos() {
        return contractMarketService.getListContractSymbol();
    }

    @GetMapping("test/get-pair-details")
    public Map<String, Object> testFutureOrderBook(String symbol, Integer action) {
        return orderBookService.testFutureOrderBook(symbol, action);
    }

    @PostMapping("test/get-data")
    public Queue<PriceDataPoint> getData(String symbol) {
        return orderBookService.getDataPoint(symbol);
    }

    @PostMapping("test/update-mark-price")
    public String testUpdateMarkPrice(@RequestBody Map<String, Object> payload) {
        String symbol = (String) payload.get("symbol");
        BigDecimal markPrice = new BigDecimal(String.valueOf(payload.get("markPrice")));
        orderBookService.testUpdateMarkPrice(symbol, markPrice);
        return "OK";
    }
}

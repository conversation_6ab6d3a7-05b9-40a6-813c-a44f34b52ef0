package com.icetea.lotus.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.service.OrderBookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class FutureOrderBookController {

    private final OrderBookService orderBookService;

    @PostMapping("future-order-book")
    public Map<String, ObjectNode> getFutureOrderBook(String symbol) {
        return orderBookService.getFutureOrderBook(symbol);
    }
}

package com.icetea.lotus.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.component.IndexData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("indexdata")
@RequiredArgsConstructor
public class IndexController {
    private final IndexData indexData;

    @PostMapping("sulkindex")
    public ObjectNode sulkIndex() {
        return indexData.getSulkindex();
    }
}

package com.icetea.lotus.controller;

import com.icetea.lotus.config.security.CurrentUser;
import com.icetea.lotus.dto.CoinListResponseDto;
import com.icetea.lotus.entity.transform.AuthMember;
import com.icetea.lotus.service.FavorCoinService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class FavorCoinController {
    private final FavorCoinService favorCoinService;

    /**
     * Find a favor
     *
     * @param member
     * @return
     */
    @PostMapping("find")
    public Page<CoinListResponseDto> findFavor(@CurrentUser AuthMember member,
                                               @RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
                                               @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                               @RequestParam(value = "symbol", required = false) String symbol,
                                               @RequestParam(value = "coinBase", required = false) String coinBase,
                                               @RequestParam(value = "sortType", required = false) String sortType) {
        return favorCoinService.findFavorCoin(member, pageNo, pageSize, symbol, coinBase, sortType);
    }

    /**
     * Add a favor
     *
     * @param member
     * @param symbol
     * @return
     */
    @PostMapping("add")
    public MessageResult addFavor(@CurrentUser AuthMember member, @RequestParam String symbol) {
        return favorCoinService.addFavor(member, symbol);
    }

    /**
     * Delete a favor
     *
     * @param member
     * @param symbol
     * @return
     */
    @PostMapping("delete")
    public MessageResult deleteFavor(@CurrentUser AuthMember member, @RequestParam String symbol) {
        return favorCoinService.deleteFavor(member, symbol);
    }
}

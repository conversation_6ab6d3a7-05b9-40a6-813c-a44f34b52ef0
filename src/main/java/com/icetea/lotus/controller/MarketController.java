package com.icetea.lotus.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.constant.SysConstant;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.dto.CoinDTO;
import com.icetea.lotus.dto.CoinListResponseDto;
import com.icetea.lotus.dto.MarketOverviewDto;
import com.icetea.lotus.dto.TopCoinResponseDto;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.entity.TradePlateItem;
import com.icetea.lotus.entity.spot.Coin;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.enums.PeriodType;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.CoinService;
import com.icetea.lotus.service.CoinThumbService;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.ExchangeTradeService;
import com.icetea.lotus.service.MarketService;
import com.icetea.lotus.service.PercentagePriceChangeService;
import com.icetea.lotus.util.MessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S1144"})
public class MarketController {
    public static final String PROTOCOL = "http://";
    public static final String ITEMS = "items";
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final MarketService marketService;
    private final ExchangeCoinService coinService;
    private final CoinService coinInfoService;
    private final CoinProcessorFactory coinProcessorFactory;
    private final ExchangeTradeService exchangeTradeService;
    private final RestTemplate restTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CoinThumbService coinThumbService;

    private final CoinExchangeRate coinExchangeRate;

    private final PercentagePriceChangeService percentagePriceChangeService;

    @Value("${cex-services.matching-engine}")
    private String matchingEngine;

    private static String convertResolutionToPeriod(String resolution) {
        if (resolution == null || resolution.trim().isEmpty()) {
            throw new IllegalArgumentException("Resolution cannot be null or empty");
        }

        String period;

        try {
            // Handle TradingView standard resolutions correctly
            period = handleTradingView(resolution);
        } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid resolution format: " + resolution, e);
        }

        return period;
    }

    private static @NotNull String handleTradingView(String resolution) {
        String period;
        if (resolution.endsWith("H") || resolution.endsWith("h")) {
            period = resolution.substring(0, resolution.length() - 1) + "hour";
        } else if (resolution.endsWith("D") || resolution.endsWith("d")) {
            period = resolution.substring(0, resolution.length() - 1) + "day";
        } else if (resolution.endsWith("W") || resolution.endsWith("w")) {
            period = resolution.substring(0, resolution.length() - 1) + "week";
        } else if (resolution.endsWith("M")) {
            // Capital M = Month (TradingView standard)
            period = resolution.substring(0, resolution.length() - 1) + "month";
        } else if (resolution.endsWith("m")) {
            // Lowercase m = Minute (TradingView standard)
            period = resolution.substring(0, resolution.length() - 1) + "min";
        } else {
            // Numeric only (e.g., "1", "5", "15", "60", "240")
            int val = Integer.parseInt(resolution);
            if (val <= 0) {
                throw new IllegalArgumentException("Resolution value must be positive: " + resolution);
            }
            if (val < 60) {
                period = resolution + "min";
            } else if (val % 60 == 0) {
                period = (val / 60) + "hour";
            } else {
                // Handle cases like 90 minutes
                period = val + "min";
            }
        }
        return period;
    }

    /**
     * Get supported transaction currency
     *
     * @return
     */
    @GetMapping("symbol")
    public List<ExchangeCoin> findAllSymbol() {
        return coinService.findAllVisible();
    }

    @GetMapping("overview")
    public Map<String, List<CoinThumb>> overview() {
        log.info("/market/overview");
        Map<String, List<CoinThumb>> result = new HashMap<>();
        List<ExchangeCoin> recommendCoin = coinService.findAllByFlag(1);
        List<CoinThumb> recommendThumbs = new ArrayList<>();
        for (ExchangeCoin coin : recommendCoin) {
            CoinProcessor processor = coinProcessorFactory.getProcessor(coin.getSymbol());
            if (processor != null) {
                CoinThumb thumb = processor.getThumb();
                recommendThumbs.add(thumb);
            }
        }
        result.put("recommend", recommendThumbs);
        List<CoinThumb> allThumbs = findSymbolThumb();
        Collections.sort(allThumbs, (o1, o2) -> o2.getChg().compareTo(o1.getChg()));
        int limit = allThumbs.size() > 10 ? 10 : allThumbs.size();
        List<CoinThumb> rankThumbs = new ArrayList<>();
        for (int i = 0; i < limit; i++) {
            if (allThumbs.get(i).getChange().compareTo(BigDecimal.ZERO) > 0) {
                rankThumbs.add(allThumbs.get(i));
            }
        }
        result.put("changeRank", rankThumbs); // Increase list
        allThumbs.sort(Comparator.comparing(CoinThumb::getChg).reversed());
        int downLimit = allThumbs.size() > 10 ? 10 : allThumbs.size();
        List<CoinThumb> rankDownThumbs = new ArrayList<>();
        for (int i = 0; i < downLimit; i++) {
            if (allThumbs.get(i).getChange().compareTo(BigDecimal.ZERO) < 0) {
                rankDownThumbs.add(allThumbs.get(i));
            }
        }
        result.put("changeRankDown", rankDownThumbs); // Decline list
        return result;
    }

    @GetMapping("engines")
    public Map<String, Integer> engines() {
        Map<String, CoinProcessor> processorList = coinProcessorFactory.getProcessorMap();
        Map<String, Integer> symbols = new HashMap<>();
        processorList.forEach((key, processor) -> {
            if (processor.getKlineAdapter().isStopKline()) {
                symbols.put(key, 2);
            } else {
                symbols.put(key, 1);
            }
        });
        return symbols;
    }

    @GetMapping("coin-info")
    public List<CoinDTO> findAllCoin() {
        log.info("Start findAllCoin");
        List<Coin> coinList = coinInfoService.findAll();
        List<CoinDTO> coinDTOList = new ArrayList<>();
        if (coinList.isEmpty()) {
            throw new NullPointerException("Coin list is empty");
        }

        for (Coin coin : coinList) {
            CoinDTO coinDto = new CoinDTO(coin.getName(), coin.getUnit(), coin.getIconUrl());
            coinDTOList.add(coinDto);
        }

        log.info("End findAllCoin");
        return coinDTOList;
    }

    /**
     * Get currency details
     */
    @GetMapping("coin/market-overview")
    public MarketOverviewDto findMarketOverviewCoin(String symbol, String unit) {
        log.info("Start findMarketOverviewCoin, symbol: {}, unit: {}", symbol, unit);

        // Lấy thông tin coin
        Coin coin = coinInfoService.findByUnit(unit);
        if (coin == null) {
            throw new NullPointerException("Not found coin with unit = " + unit);
        }

        // Lấy CoinThumb từ CoinProcessor
        CoinProcessor processor = coinProcessorFactory.getProcessor(symbol);
        CoinThumb thumb = processor != null ? processor.getThumb() : null;

        if (thumb == null) {
            throw new NullPointerException("No CoinThumb data found for symbol = " + symbol);
        }

        // Đọc dữ liệu từ CoinThumb
        BigDecimal lastPrice = Optional.ofNullable(thumb.getClose()).orElse(BigDecimal.ZERO);
        BigDecimal volume24h = Optional.ofNullable(thumb.getTurnover()).orElse(BigDecimal.ZERO);
        BigDecimal high24h = Optional.ofNullable(thumb.getHigh()).orElse(BigDecimal.ZERO);
        BigDecimal low24h = Optional.ofNullable(thumb.getLow()).orElse(BigDecimal.ZERO);

        // All Time High vẫn dùng KLine (nếu cần)
        KLine timeHigh = marketService.findAllTimeHigh(symbol, PeriodType.ONE_MIN.getValue());
        BigDecimal allTimeHigh = timeHigh != null && timeHigh.getHighestPrice() != null
                ? timeHigh.getHighestPrice()
                : BigDecimal.ZERO;

        // Build response
        MarketOverviewDto responseDto = new MarketOverviewDto();
        responseDto.setSymbol(symbol);
        responseDto.setDescription(coin.getDescription());
        responseDto.setCirculationSupply(coin.getCirculationSupply());
        responseDto.setMaxSupplyLevel(coin.getMaxSupply());

        responseDto.setLastPrice(lastPrice);
        responseDto.setVolume24h(volume24h);
        responseDto.setHigh24h(high24h);
        responseDto.setLow24h(low24h);
        responseDto.setAllTimeHigh(allTimeHigh);

        responseDto.setMarketCap(lastPrice.multiply(coin.getCirculationSupply()));

        // % thay đổi vẫn dùng từ service
        responseDto.setChg1h(percentagePriceChangeService.calculateChg(symbol, PeriodType.ONE_HOUR.getValue()));
        responseDto.setChg24h(thumb.getChg().multiply(BigDecimal.valueOf(100)));
        responseDto.setChg7d(percentagePriceChangeService.calculateChg(symbol, PeriodType.ONE_WEEK.getValue()));

        log.info("End findMarketOverviewCoin for symbol: {}", symbol);
        return responseDto;
    }

    /**
     * Get the price of USDT in C2C to RMB
     */
    @PostMapping("ctc-usdt")
    public MessageResult ctcUsdt() {
        MessageResult mr = new MessageResult(0, CommonConstants.SUCCESS_MESSAGE);
        BigDecimal latestPrice = coinExchangeRate.getUsdtCnyRate();
        ObjectNode obj = objectMapper.createObjectNode();
        obj.put("buy", latestPrice.doubleValue());
        // 0.015 is 1.5% bid-ask spread
        obj.put("sell", latestPrice.subtract(latestPrice.multiply(new BigDecimal("0.011")).setScale(2, RoundingMode.DOWN)).doubleValue());
        mr.setData(obj);
        return mr;
    }

    /**
     * Get the details of a transaction pair
     *
     * @param symbol
     * @return
     */
    @GetMapping("symbol-info")
    public ExchangeCoin findSymbol(String symbol) {
        ExchangeCoin coin = coinService.findBySymbol(symbol);
        coin.setCurrentTime(Calendar.getInstance().getTimeInMillis());
        return coin;
    }

    /**
     * Get currency abbreviation
     *
     * @return
     */
    @GetMapping("symbol-thumb")
    public List<CoinThumb> findSymbolThumb() {
        return coinThumbService.findSymbolThumb();
    }

    @GetMapping("symbol-thumb-trend")
    public ArrayNode findSymbolThumbWithTrend() {
        List<ExchangeCoin> coins = coinService.findAllVisible();
        Calendar calendar = Calendar.getInstance();
        // Set the seconds and microsecond fields to 0
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        long nowTime = calendar.getTimeInMillis();
        calendar.add(Calendar.HOUR_OF_DAY, -24);
        ArrayNode array = objectMapper.createArrayNode();
        long firstTimeOfToday = calendar.getTimeInMillis();
        for (ExchangeCoin coin : coins) {
            CoinProcessor processor = coinProcessorFactory.getProcessor(coin.getSymbol());
            CoinThumb thumb = processor.getThumb();
            ObjectNode json = objectMapper.valueToTree(thumb);
            json.put("zone", coin.getZone());
            List<KLine> lines = marketService.findAllKLine(thumb.getSymbol(), firstTimeOfToday, nowTime, "15min");
            ArrayNode trend = objectMapper.createArrayNode();
            for (KLine line : lines) {
                trend.add(line.getClosePrice().doubleValue());
            }
            json.set("trend", trend);
            array.add(json);
        }
        return array;
    }

    @GetMapping("top-gainer")
    public Page<TopCoinResponseDto> findTopGainer(
            @RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "3") int pageSize) {
        return coinThumbService.findTopGainer(pageNo, pageSize);
    }

    @GetMapping("newly-listed")
    public Page<TopCoinResponseDto> findNewlyListed(
            @RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "3") int pageSize) {
        return coinThumbService.findNewlyListed(pageNo, pageSize);
    }

    @GetMapping("top-trending")
    public Page<TopCoinResponseDto> findTopTrending(
            @RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "3") int pageSize) {
        return coinThumbService.findTopTrending(pageNo, pageSize);
    }

    @GetMapping("coin-list")
    public Page<CoinListResponseDto> findCoinList(@RequestParam(defaultValue = "0") int pageNo,
                                                  @RequestParam(defaultValue = "10") int pageSize,
                                                  @RequestParam(required = false) String symbol,
                                                  @RequestParam(required = false) String coinBase,
                                                  @RequestParam(required = false) String sortType) {
        return coinThumbService.findCoinList(pageNo, pageSize, symbol, coinBase, sortType);
    }

    /**
     * Get the historical K-line of currency
     * Automatically fills gaps with flat candles to ensure chart continuity
     */
    /**
     * Get the historical K-line of currency
     */
    @GetMapping("history")
    public ArrayNode findKHistory(String symbol, long from, long to, String resolution) {
        String period = convertResolutionToPeriod(resolution);
        List<KLine> list = marketService.findAllKLine(symbol, from, to, period);

        ArrayNode array = objectMapper.createArrayNode();
        for (KLine item : list) {
            ArrayNode group = objectMapper.createArrayNode();
            group.add(item.getTime());
            group.add(item.getOpenPrice().doubleValue());
            group.add(item.getHighestPrice().doubleValue());
            group.add(item.getLowestPrice().doubleValue());
            group.add(item.getClosePrice().doubleValue());
            group.add(item.getVolume().doubleValue());
            array.add(group);
        }
        KLine currentKline = getKlineFromRedis(symbol, period);
        if (currentKline != null) {
            boolean isCurrent = handleTimeRequest(currentKline.getTime(), to, resolution);
            if (isCurrent) {
                if(!list.isEmpty() && currentKline.getTime() <= list.get(list.size() - 1).getTime()){
                   return array;
                }
                ArrayNode currentKlineArray = objectMapper.createArrayNode();
                currentKlineArray.add(currentKline.getTime());
                currentKlineArray.add(currentKline.getOpenPrice().doubleValue());
                currentKlineArray.add(currentKline.getHighestPrice().doubleValue());
                currentKlineArray.add(currentKline.getLowestPrice().doubleValue());
                currentKlineArray.add(currentKline.getClosePrice().doubleValue());
                currentKlineArray.add(currentKline.getVolume().doubleValue());
                array.add(currentKlineArray);
            }
        }
        return array;
    }

    private boolean handleTimeRequest(long time, long to, String resolution) {
        Instant instant = Instant.ofEpochMilli(time);
        if("1D".equals(resolution)){
            instant = instant.minus(1, ChronoUnit.DAYS);
        }else if ("1W".equals(resolution)){
            instant = instant.minus(7, ChronoUnit.DAYS);
        }else if ("240".equals(resolution)){
            instant = instant.minus(4, ChronoUnit.HOURS);
        }else if ("60".equals(resolution)){
            instant = instant.minus(1, ChronoUnit.HOURS);
        }else if("30".equals(resolution)){
            instant = instant.minus(30, ChronoUnit.MINUTES);
        }else if("15".equals(resolution)){
            instant = instant.minus(15, ChronoUnit.MINUTES);
        }else if("5".equals(resolution)){
            instant = instant.minus(5, ChronoUnit.MINUTES);
        }else {
            instant = instant.minus(1, ChronoUnit.MILLIS);
        }
        return instant.toEpochMilli() < to;
    }

    private KLine getKlineFromRedis(String symbol, String period){
        String redisKey = String.format(CommonConstants.CURRENT_KLINE_KEY_REDIS, symbol, period);
        Object dataRedis =  redisTemplate.opsForValue().get(redisKey);
        return objectMapper.convertValue(dataRedis, KLine.class);
    }

    /**
     * Check the latest transaction records
     *
     * @param symbol Trading pair symbols
     * @param size   Return the maximum number of records
     * @return
     */
    @GetMapping("latest-trade")
    public List<ExchangeTrade> latestTrade(String symbol, int size) {
        symbol = symbol.contains("_") ? symbol.replace("_", "/") : symbol;
        return exchangeTradeService.findLatest(symbol, size);
    }

    @GetMapping("exchange-plate")
    public Map<String, List<TradePlateItem>> findTradePlate(String symbol) {
        String url = PROTOCOL + matchingEngine + "/matching-engine/monitor/plate?symbol=" + symbol;
        ResponseEntity<HashMap<String, List<TradePlateItem>>> response =
                restTemplate.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<>() {
                });

        return response.getBody();
    }

    @GetMapping("exchange-plate-mini")
    public Map<String, ObjectNode> findTradePlateMini(String symbol) {
        String url = PROTOCOL + matchingEngine + "/matching-engine/monitor/plate-mini?symbol=" + symbol;

        ResponseEntity<Map<String, ObjectNode>> response =
                restTemplate.exchange(url, HttpMethod.GET, null,
                        new ParameterizedTypeReference<Map<String, ObjectNode>>() {
                        });

        return response.getBody();
    }

    @GetMapping("exchange-plate-full")
    public Map<String, ObjectNode> findTradePlateFull(String symbol) {
        log.info("Fetching full trade plate for symbol: {}", symbol);
        String url = PROTOCOL + matchingEngine + "/matching-engine/monitor/plate-full?symbol=" + symbol;

        ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<Map<String, Object>>() {
                }
        );

        Map<String, ObjectNode> result = objectMapper.convertValue(
                response.getBody(),
                new TypeReference<Map<String, ObjectNode>>() {
                }
        );

        log.info("Processing trade plate result with {} entries", result.size());

        // Ensure "items" fields are always arrays and sort by price descending for both bid and ask
        result.forEach((key, value) -> {
            // Normalize to array if needed
            if (!value.get(ITEMS).isArray()) {
                log.info("Converting non-array items to array for key: {}", key);
                value.set(ITEMS, objectMapper.createArrayNode().add(value.get(ITEMS)));
            }

            // Sort items: bid by price DESC, ask by price ASC
            if (value.get(ITEMS).isArray()) {
                ArrayNode itemsArray = (ArrayNode) value.get(ITEMS);
                java.util.List<JsonNode> itemsList = new java.util.ArrayList<>();
                itemsArray.forEach(itemsList::add);

                boolean isAsk = "ask".equalsIgnoreCase(key);

                itemsList.sort((a, b) -> {
                    // Defensive: handle missing or non-numeric price
                    BigDecimal pa = a.get(CommonConstants.PRICE).decimalValue();
                    BigDecimal pb = b.get(CommonConstants.PRICE).decimalValue();
                    return isAsk ? pa.compareTo(pb) : pb.compareTo(pa);
                });

                ArrayNode sortedArray = objectMapper.createArrayNode();
                itemsList.forEach(sortedArray::add);
                value.set(ITEMS, sortedArray);
            }
        });

        log.info("Successfully processed trade plate data");
        return result;
    }


    @PostMapping("add_dcitionary/{bond}/{value}")
    public MessageResult addDictionaryForAdmin(@PathVariable("bond") String bond, @PathVariable("value") String value) {
        log.info(">>>>Dictionary table data has been modified>>>Modify the data in cache>>>>>bond>{}>>>>>value>>{}", bond, value);
        String key = SysConstant.DATA_DICTIONARY_BOUND_KEY + bond;
        ValueOperations<String, Object> valueOperations = redisTemplate.opsForValue();

        Object bondvalue = valueOperations.get(key);
        if (bondvalue == null) {
            log.info(">>>>>>No data in the cache>>>>>");
            valueOperations.set(key, value);
        } else {
            log.info(">>>>There is data in the cache>>>");
            valueOperations.getOperations().delete(key);
            valueOperations.set(key, value);
        }
        MessageResult re = new MessageResult();
        re.setCode(0);
        re.setMessage("success");
        return re;
    }

    /**
     * Fills gaps in KLine data to ensure chart continuity
     * FIXED: Properly handles starting price to prevent gaps
     *
     * @param existingKLines List of existing KLines from database
     * @param from           Start time
     * @param to             End time
     * @param period         Period string (e.g., "1min", "5min")
     * @param symbol         Trading symbol
     * @return List of KLines with gaps filled
     */
    @SuppressWarnings("all")
    private List<KLine> fillKLineGaps(List<KLine> existingKLines, long from, long to, String period, String symbol) {
        if (existingKLines == null || existingKLines.isEmpty()) {
            log.debug("No existing KLines to fill gaps for {}", symbol);
            return existingKLines;
        }

        // Calculate interval in milliseconds
        long intervalMs = getPeriodIntervalMs(period);
        if (intervalMs == 0) {
            log.warn("Unknown period: {}, returning original KLines", period);
            return existingKLines;
        }

        // Generate expected time points
        List<Long> expectedTimePoints = generateExpectedTimePoints(from, to, intervalMs);

        // Create map for quick lookup of existing KLines
        Map<Long, KLine> existingKLineMap = new HashMap<>();
        for (KLine kline : existingKLines) {
            existingKLineMap.put(kline.getTime(), kline);
        }

        // ✅ CRITICAL FIX: Get proper starting price instead of ZERO
        BigDecimal lastKnownPrice = getProperStartingPrice(existingKLines, from, period, symbol);

        // Build continuous KLine list
        List<KLine> continuousKLines = new ArrayList<>();

        for (Long timePoint : expectedTimePoints) {
            if (existingKLineMap.containsKey(timePoint)) {
                // KLine exists, use it
                KLine existingKLine = existingKLineMap.get(timePoint);
                continuousKLines.add(existingKLine);
                lastKnownPrice = existingKLine.getClosePrice();
            } else {
                // KLine missing, create flat candle with proper price
                if (lastKnownPrice != null && lastKnownPrice.compareTo(BigDecimal.ZERO) > 0) {
                    KLine flatCandle = createFlatCandle(timePoint, period, lastKnownPrice);
                    continuousKLines.add(flatCandle);
                    log.debug("Created flat candle for {} at {} with price {}", symbol, timePoint, lastKnownPrice);
                } else {
                    log.warn("Skipping gap fill for {} at {} - no valid price available", symbol, timePoint);
                }
            }
        }

        log.info("Filled {} gaps for {} in period {}",
                continuousKLines.size() - existingKLines.size(), symbol, period);
        return continuousKLines;
    }

    /**
     * ✅ NEW METHOD: Get proper starting price for gap filling
     * This ensures continuity by finding the appropriate price to use for missing periods
     * Strategies are ordered by priority for proper chart continuity
     */
    private BigDecimal getProperStartingPrice(List<KLine> existingKLines, long from, String period, String symbol) {
        // Strategy 1: Query for the previous KLine before the requested range (HIGHEST PRIORITY for continuity)
        try {
            KLine previousKLine = marketService.findPreviousKline(symbol, period, from);
            if (previousKLine != null && previousKLine.getClosePrice() != null) {
                log.debug("Using previous KLine close price {} as starting price for {}",
                        previousKLine.getClosePrice(), symbol);
                return previousKLine.getClosePrice();
            }
        } catch (Exception e) {
            log.warn("Error getting previous KLine for gap filling: {}", e.getMessage());
        }

        // Strategy 2: If we have existing KLines, use the earliest one's open price
        if (!existingKLines.isEmpty()) {
            // Sort by time to get the earliest KLine
            KLine earliestKLine = existingKLines.stream()
                    .min(Comparator.comparing(KLine::getTime))
                    .orElse(null);

            if (earliestKLine != null && earliestKLine.getOpenPrice() != null &&
                    earliestKLine.getOpenPrice().compareTo(BigDecimal.ZERO) > 0) {
                log.debug("Using earliest KLine open price {} as starting price for {}",
                        earliestKLine.getOpenPrice(), symbol);
                return earliestKLine.getOpenPrice();
            }
        }

        // Strategy 3: Use the first available non-zero price from existing KLines
        if (!existingKLines.isEmpty()) {
            return getValueOfKline(existingKLines, symbol);
        }

        // Strategy 4: No valid price found - return null to skip gap filling
        log.warn("No valid starting price found for gap filling {} in period {}", symbol, period);
        return null;
    }

    private BigDecimal getValueOfKline(List<KLine> existingKLines, String symbol) {
        for (KLine kline : existingKLines) {
            if (kline.getOpenPrice() != null && kline.getOpenPrice().compareTo(BigDecimal.ZERO) > 0) {
                log.debug("Using first available open price {} as starting price for {}",
                        kline.getOpenPrice(), symbol);
                return kline.getOpenPrice();
            }
            if (kline.getClosePrice() != null && kline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
                log.debug("Using first available close price {} as starting price for {}",
                        kline.getClosePrice(), symbol);
                return kline.getClosePrice();
            }
        }

        return null;
    }

    /**
     * Gets the interval in milliseconds for a given period
     */
    private long getPeriodIntervalMs(String period) {
        try {
            if (period.endsWith("min")) {
                int minutes = Integer.parseInt(period.substring(0, period.length() - 3));
                return minutes * 60 * 1000L;
            } else if (period.endsWith("hour")) {
                int hours = Integer.parseInt(period.substring(0, period.length() - 4));
                return hours * 60 * 60 * 1000L;
            } else if (period.endsWith("day")) {
                int days = Integer.parseInt(period.substring(0, period.length() - 3));
                return days * 24 * 60 * 60 * 1000L;
            } else if (period.endsWith("week")) {
                int weeks = Integer.parseInt(period.substring(0, period.length() - 4));
                return weeks * 7 * 24 * 60 * 60 * 1000L;
            } else if (period.endsWith("month")) {
                int months = Integer.parseInt(period.substring(0, period.length() - 5));
                return months * 30 * 24 * 60 * 60 * 1000L; // Approximate
            }
        } catch (NumberFormatException e) {
            log.error("Error parsing period: {}", period, e);
        }
        return 0;
    }

    /**
     * Generates expected time points between from and to with given interval
     */
    private List<Long> generateExpectedTimePoints(long from, long to, long intervalMs) {
        List<Long> timePoints = new ArrayList<>();

        // Normalize start time to proper period boundary
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(from);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // Normalize to proper period boundaries
        if (intervalMs >= 60 * 1000) { // >= 1 minute
            long minutes = intervalMs / (60 * 1000);
            if (minutes >= 60) { // >= 1 hour
                calendar.set(Calendar.MINUTE, 0);
                if (minutes >= 24 * 60) { // >= 1 day
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                }
            } else if (minutes > 1) { // Multi-minute periods (5min, 15min, 30min)
                int currentMinute = calendar.get(Calendar.MINUTE);
                int normalizedMinute = (currentMinute / (int) minutes) * (int) minutes;
                calendar.set(Calendar.MINUTE, normalizedMinute);
            }
        }

        long currentTime = calendar.getTimeInMillis();

        // Ensure we start from or before the 'from' time
        while (currentTime > from) {
            currentTime -= intervalMs;
        }

        // Generate time points
        while (currentTime <= to) {
            if (currentTime >= from) {
                timePoints.add(currentTime);
            }
            currentTime += intervalMs;
        }

        return timePoints;
    }

    /**
     * Creates a flat candle for a missing time period
     */
    private KLine createFlatCandle(long time, String period, BigDecimal price) {
        KLine flatCandle = new KLine();
        flatCandle.setTime(time);
        flatCandle.setPeriod(period);
        flatCandle.setOpenPrice(price);
        flatCandle.setHighestPrice(price);
        flatCandle.setLowestPrice(price);
        flatCandle.setClosePrice(price);
        flatCandle.setVolume(BigDecimal.ZERO);
        flatCandle.setTurnover(BigDecimal.ZERO);
        flatCandle.setCount(0);
        return flatCandle;
    }

    /**
     * BTC/USDT trend line
     *
     * @return
     */
    @GetMapping("/btc/trend")
    public MessageResult btcTrend() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        long nowTime = calendar.getTimeInMillis();
        calendar.add(Calendar.HOUR_OF_DAY, -24);

        long firstTimeOfToday = calendar.getTimeInMillis();

        List<KLine> lines = marketService.findAllKLine("BTC/USDT", firstTimeOfToday, nowTime, "5min");
        ArrayNode trend = objectMapper.createArrayNode();
        for (KLine line : lines) {
            trend.add(line.getClosePrice().doubleValue());
        }
        MessageResult re = new MessageResult();
        re.setCode(0);
        re.setData(trend);
        re.setMessage("success");
        return re;
    }
}

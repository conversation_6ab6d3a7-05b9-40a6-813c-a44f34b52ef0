package com.icetea.lotus.job;

import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.dto.ContractCoinInfo;
import com.icetea.lotus.service.WebsocketExchangeFuture;
import com.icetea.lotus.service.impl.WebsocketExchangeFutureImpl;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebsocketExchangeJob {

    private final WebsocketExchangeFuture websocketExchangeFuture;
    private final Map<String, Instant> lastPriceUpdate = new ConcurrentHashMap<>();
    private final Map<String, Boolean> connectionStatus = new ConcurrentHashMap<>();
    private final AtomicBoolean jobRunning = new AtomicBoolean(false);
    private final Map<String, ContractCoinInfo> contractCoinInfoMap = new ConcurrentHashMap<>();
    private final RedisTemplate<String, Object> redisTemplate;
    private final Set<String> monitoredSymbols = ConcurrentHashMap.newKeySet();

    /**
     * Khởi động job khi application ready
     */
    @EventListener(ApplicationReadyEvent.class)
    public void startWebsocketConnections() throws InterruptedException {
        if (jobRunning.compareAndSet(false, true)) {
            log.info("🚀 Starting WebSocket Job Service...");
            connectToAllSymbols();
            log.info("✅ WebSocket Job Service started successfully");
        }
    }

    /**
     * Job tự động kết nối tất cả symbols đến tất cả exchanges
     */
    private void connectToAllSymbols() throws InterruptedException {
        if (contractCoinInfoMap.isEmpty()) {
            Map<String, String> symbolMap = getListEnabledSymbol();
            symbolMap.forEach((key, value) -> contractCoinInfoMap.put(key, new ContractCoinInfo()));
        }
        for (Map.Entry<String, ContractCoinInfo> entry : contractCoinInfoMap.entrySet()) {
            monitoredSymbols.add(entry.getKey());

        }

        for (String symbol : monitoredSymbols) {
            connectSymbolToAllExchanges(symbol);
            connectionStatus.put(symbol, true);
            lastPriceUpdate.put(symbol, Instant.now());
        }
    }

    /**
     * Kết nối một symbol đến tất cả exchanges
     */
    private void connectSymbolToAllExchanges(String symbol) throws InterruptedException {
        log.info("🔌 Connecting {} to all exchanges...", symbol);

        try {
            // Kết nối đến Binance
            websocketExchangeFuture.connectBinance(symbol);
            Thread.sleep(100);

            // Kết nối đến Gate.io
            websocketExchangeFuture.connectGate(symbol);
            Thread.sleep(100);

            // Kết nối đến OKX
            websocketExchangeFuture.connectOkx(symbol);
            Thread.sleep(100);

            // Kết nối đến Bitget
            websocketExchangeFuture.connectBitget(symbol);
            Thread.sleep(100);

            log.info("✅ Connected {} to all exchanges", symbol);

        } catch (InterruptedException ie) {
            log.error("Thread interrupted while connecting {}", symbol, ie);
            Thread.currentThread().interrupt();
            throw ie;
        } catch (Exception e) {
            log.error("❌ Error connecting {} to exchanges: {}", symbol, e.getMessage());
        }
    }


    /**
     * Job health check - chạy mỗi 30 giây
     */
    @Scheduled(fixedRate = 30000) // 30 seconds
    public void healthCheck() {
        if (!jobRunning.get()) {
            return;
        }

        log.debug("🏥 Running WebSocket health check...");

        for (String symbol : monitoredSymbols) {
            checkSymbolHealth(symbol);
        }
    }

    private void checkSymbolHealth(String symbol) {
        try {
            // Lấy prices hiện tại
            Map<String, BigDecimal> prices = ((WebsocketExchangeFutureImpl) websocketExchangeFuture)
                    .getPricesForSymbol(symbol);

            Instant lastUpdate = lastPriceUpdate.get(symbol);
            Instant now = Instant.now();

            // Kiểm tra nếu không có price updates trong 2 phút
            if (lastUpdate == null || lastUpdate.plus(2, ChronoUnit.MINUTES).isBefore(now)) {
                log.warn("⚠️ No price updates for {} in last 2 minutes. Prices: {}", symbol, prices);

                // If there is no price, try Reconnect
                if (prices.isEmpty()) {
                    log.warn("🔄 No price data for {}. Attempting reconnection...", symbol);
                    reconnectSymbol(symbol);
                }
            } else {
                // Update last price time If there is data
                if (!prices.isEmpty()) {
                    lastPriceUpdate.put(symbol, now);
                }
            }

            // Check index price
            BigDecimal indexPrice = websocketExchangeFuture.calculateIndexPriceToWebsocket(symbol);
            if (indexPrice != null) {
                log.debug("📊 {} Index Price: {} (from {} exchanges)",
                        symbol, indexPrice, prices.size());
            }

        } catch (Exception e) {
            log.error("❌ Health check failed for {}: {}", symbol, e.getMessage());
        }
    }

    /**
     * Reconnect a symbol
     */
    private void reconnectSymbol(String symbol) {
        log.info("🔄 Reconnecting symbol: {}", symbol);

        // Disconnect before
        ((WebsocketExchangeFutureImpl) websocketExchangeFuture).disconnectSymbol(symbol);

        // Wait a little bit and connect again
        try {
            Thread.sleep(1000);
            connectSymbolToAllExchanges(symbol);
            lastPriceUpdate.put(symbol, Instant.now());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("❌ Reconnection interrupted for {}", symbol);
        }
    }


    @Scheduled(fixedRate = 300000) // 5 minutes
    public void statusReport() {
        if (!jobRunning.get()) {
            return;
        }

        log.info("📋 === WebSocket Status Report ===");

        for (String symbol : monitoredSymbols) {
            Map<String, BigDecimal> prices = ((WebsocketExchangeFutureImpl) websocketExchangeFuture)
                    .getPricesForSymbol(symbol);

            BigDecimal indexPrice = websocketExchangeFuture.calculateIndexPriceToWebsocket(symbol);

            log.info("💎 {}: {} exchanges connected, Index: {}",
                    symbol, prices.size(), indexPrice != null ? indexPrice : "N/A");

            // Log chi tiết prices
            prices.forEach((exchange, price) ->
                    log.debug("   {} {}: {}", exchange, symbol, price));
        }

        log.info("📋 === End Status Report ===");
    }

    /**
     * Job cleanup - chạy mỗi giờ để dọn dẹp
     */
    @Scheduled(fixedRate = 3600000) // 1 hour
    public void cleanup() {
        if (!jobRunning.get()) {
            return;
        }

        log.info("🧹 Running cleanup job...");

        // Có thể thêm logic cleanup ở đây
        // Ví dụ: remove stale price data, log statistics, etc.

        log.info("✅ Cleanup completed");
    }

    /**
     * Stop job
     */
    public void stopJob() {
        if (jobRunning.compareAndSet(true, false)) {
            log.info("🛑 Stopping WebSocket Job Service...");

            // Disconnect all symbols
            for (String symbol : monitoredSymbols) {
                ((WebsocketExchangeFutureImpl) websocketExchangeFuture).disconnectSymbol(symbol);
            }

            connectionStatus.clear();
            lastPriceUpdate.clear();

            log.info("✅ WebSocket Job Service stopped");
        }
    }

    /**
     * Cleanup khi shutdown application
     */
    @PreDestroy
    public void onShutdown() {
        log.info("🛑 Application shutting down, cleaning up WebSocket connections...");
        stopJob();
        ((WebsocketExchangeFutureImpl) websocketExchangeFuture).shutdown();
    }

    private Map<String, String> getListEnabledSymbol() {
        Object redisValue = redisTemplate.opsForValue().get(CommonConstants.LIST_ENABLED_SYMBOL_KEY_REDIS);
        if (redisValue == null) {
            return new HashMap<>();
        }
        try {
            return (HashMap<String, String>) redisValue;
        } catch (Exception e) {
            // Log error and return empty list or handle appropriately
            log.error("Failed to parse Redis value as JSON list: " + redisValue, e);
            return new HashMap<>();
        }
    }
}

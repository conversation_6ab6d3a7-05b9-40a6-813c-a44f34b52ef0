package com.icetea.lotus.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.config.OrderBookFutureStorage;
import com.icetea.lotus.constants.CommonConstants;
import com.icetea.lotus.dto.ContractCoinInfo;
import com.icetea.lotus.dto.RealtimeWalletWhenChangeMarkPrice;
import com.icetea.lotus.dto.UpdatePositionWithMarkPrice;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.MemberService;
import com.icetea.lotus.service.MovingAverageService;
import com.icetea.lotus.service.PremiumIndexService;
import com.icetea.lotus.service.PriceService;
import com.icetea.lotus.util.OrderBookUtils;
import com.icetea.lotus.util.RedisEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

@Component
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("all")
public class ContractPushJob {
    private final OrderBookFutureStorage orderBookFutureStorage;
    private final Map<String, ContractCoinInfo> contractCoinInfoMap = new ConcurrentHashMap<>();
    // Locks for more granular synchronization
    private final ReentrantLock platesLock = new ReentrantLock();

    // Dependencies
    private final ExchangeCoinService coinService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;
    private final PriceService priceService;
    private final MovingAverageService movingAverageService;

    private final RedisTemplate<String, Object> redisTemplate;
    // Price tracking
    private volatile BigDecimal lastBuyHeightPrice = BigDecimal.ZERO;
    private volatile BigDecimal lastSellLowPrice = BigDecimal.ZERO;

    private Boolean isUpdateMarkPrice = true;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final MemberService memberService;
    private final PremiumIndexService premiumIndexService;
    private final RedisEventPublisher redisEventPublisher;

    @Value("${topic-kafka.contract.position-update-with-mark-price}")
    private String positionUpdateWithMarkPriceTopic;

    @Value("${topic-kafka.contract.wallet-realtime-with-mark-price}")
    private String walletRealtimeWithMarkPriceTopic;

    /**
     * Adds a trade plate to the queue for processing
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to add
     */
    public void updateOrderBook(String symbol, Map<String, Object> orderBook) {
        if (orderBook == null) {
            return;
        }

        platesLock.lock();
        try {
            Map<String, Object> list = orderBookFutureStorage.getOrderBookMap().put(symbol, orderBook);
        } finally {
            platesLock.unlock();
        }
    }

    public void pushFutureOrderBook() {
        log.info("=============START JOB pushFutureOrderBook===========");
        for (Map.Entry<String, Map<String, Object>> entry : orderBookFutureStorage.getOrderBookMap().entrySet()) {
            String symbol = entry.getKey();
            Map<String, Object> orderBook = entry.getValue();

            // Chỉ log khi có plates để tránh spam log
            log.debug("pushPlate: symbol: {}, plates: {}", symbol, orderBook.size());
            processFutureOrderBook(symbol, orderBook);
            log.debug("processRealTradePlates completed for symbol: {}", symbol);
        }
        log.info("=============End JOB pushFutureOrderBook===========");
    }

    /**
     * Processes real trade plates from the queue
     *
     * @param symbol Trading pair symbol
     * @param plates List of trade plates to process
     */
    private void processFutureOrderBook(String symbol, Map<String, Object> orderBook) {
        boolean hasPushAskPlate = false;
        boolean hasPushBidPlate = false;

        platesLock.lock();
        try {
            pushOrderBookToWebSocket(symbol, orderBook);
        } finally {
            platesLock.unlock();
        }
    }

    /**
     * Pushes plate data to WebSocket topics
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to push
     */
    private void pushOrderBookToWebSocket(String symbol, Map<String, Object> orderBook) {
        try {
            // Websocket push handover information
            messagingTemplate.convertAndSend("/topic/market/future-order-book/" + symbol, OrderBookUtils.processOrderBook(orderBook, 100));
        } catch (Exception e) {
            log.error("=============Error pushOrderBookToWebSocket===========");
            log.error(e.getMessage());
        }
    }

    public Map<String, Object> getFutureOrderBook(String symbol) {
        return orderBookFutureStorage.getOrderBookMap().get(symbol);
    }

    /**
     * Pushes plate data to WebSocket topics
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to push
     */
    private void pushSymbolInfoToWebSocket(String symbol, ContractCoinInfo coinInfo) {
        try {
            // Websocket push information
            messagingTemplate.convertAndSend("/topic/market/contract/symbol-thumb/" + symbol, coinInfo);
        } catch (Exception e) {
            log.error("=============Error pushSymbolInfoToWebSocket===========");
            log.error(e.getMessage());
        }
    }

    //    @Scheduled(fixedRate = 5000)
    public void updateIndexPrice() {
        try {
            contractCoinInfoMap.put("BTCUSDT", new ContractCoinInfo());
            for (Map.Entry<String, ContractCoinInfo> entry : contractCoinInfoMap.entrySet()) {
                String symbol = entry.getKey();
                ContractCoinInfo coinInfo = entry.getValue();
                coinInfo.setIndexPrice(priceService.calculateIndexPrice(symbol));
                log.info("====updateIndexPrice with symbol: {} - price: {}", symbol, coinInfo.getIndexPrice());
            }
        } catch (Exception e) {
            log.error("Error processing coin info data: {}", e.getMessage(), e);
        }
    }

    @Scheduled(fixedDelay = 30000)
    public void updateContractCoinInfo() {
        try {
            if (contractCoinInfoMap.size() == 0) {
                Map<String, String> symbolMap = getListEnabledSymbol();
                symbolMap.forEach((key, value) -> {
                    contractCoinInfoMap.put(key, new ContractCoinInfo());
                });
            }

            for (Map.Entry<String, ContractCoinInfo> entry : contractCoinInfoMap.entrySet()) {
                String symbol = entry.getKey();
                ContractCoinInfo coinInfo = entry.getValue();

                BigDecimal indexPrice = priceService.calculateIndexPrice(symbol);
                BigDecimal markPrice = indexPrice.setScale(2, RoundingMode.DOWN);
                Map<String, Object> orderBook = orderBookFutureStorage.getOrderBookMap().get(symbol);
                BigDecimal fundingRate = BigDecimal.ZERO;
                if (orderBookFutureStorage.getOrderBookMap().get(symbol) != null) {
                    try {
                        BigDecimal impactMidPrice = priceService.calculateImpactMidPrice(symbol, indexPrice, orderBook);
                        if (ObjectUtils.isNotEmpty(impactMidPrice)) {
                            movingAverageService.addDataPoint(symbol, impactMidPrice, indexPrice);
                            markPrice = priceService.calculateMarkPrice(symbol, indexPrice, movingAverageService.getCurrentMovingAverage(symbol));
                        }

                        // Calculate Premium Index to save Funds
                        BigDecimal premiumIndex = priceService.calculatePremiumIndex(orderBook, indexPrice, 18);
                        if (premiumIndex != null) {
                            premiumIndexService.savePremiumIndex(symbol, premiumIndex);
                        }

                        // If there is PremiumIDEX → Calculate Funding Rate
                        List<BigDecimal> premiums = premiumIndexService.getPremiumIndexes(symbol);
                        if (ObjectUtils.isNotEmpty(premiums) && ObjectUtils.isNotEmpty(premiumIndex)) {
                            BigDecimal avgPremiumIndex = priceService.calculateAveragePremiumIndex(premiums, 8);
                            BigDecimal interestRate = priceService.calculateInterestRate(8);
                            fundingRate = priceService.calculateFundingRate(avgPremiumIndex, interestRate, premiumIndex, 8);
                        }
                    } catch (Exception e) {
                        log.error("Error calculate markPrice: {}", e.getMessage(), e);
                    }
                }
                coinInfo.setIndexPrice(indexPrice);
                if (isUpdateMarkPrice) {
                    coinInfo.setMarkPrice(markPrice);
                    String markPriceKey = symbol + CommonConstants.SUFFIX_MARK_PRICE_KEY_REDIS;
                    setPriceToRedis(markPriceKey, markPrice);

                    // Send kafka to update position
                    sendKafkaToUpdatePosition(symbol, markPrice, indexPrice);

                    // Send kafka to real time wallet
                    sendKafkaToRealTimeWallet(symbol);
                } else {
                    Map<String, Object> pairDetails = getPairDetailRedis(symbol);
                    if (pairDetails != null) {
                        markPrice = new BigDecimal(pairDetails.get("markPrice").toString()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    }
                    coinInfo.setMarkPrice(markPrice);
                }
                coinInfo.setSymbol(symbol);
                coinInfo.setFundingRate(fundingRate);
                setPairDetailToRedis(symbol, objectMapper.convertValue(coinInfo, new TypeReference<Map<String, Object>>() {
                }));
                String coinInfoKey = String.format(CommonConstants.FUTURE_CONTRACT_COIN_INFO_KEY_REDIS, symbol);
                setContractCoinInfoToRedis(coinInfoKey, coinInfo);
                log.info("====updateContractCoinInfo with symbol: {} ");
            }
        } catch (Exception e) {
            log.error("Error processing coin info data: {}", e.getMessage(), e);
        }
    }

    private void sendKafkaToRealTimeWallet(String symbol) throws JsonProcessingException {
        RealtimeWalletWhenChangeMarkPrice message = RealtimeWalletWhenChangeMarkPrice.builder()
                .symbol(symbol)
                .build();
        String payload = new ObjectMapper().writeValueAsString(message);
        log.info("[kafka-producer-liquidation-notification] Sending notification to topic {}: {}", walletRealtimeWithMarkPriceTopic, payload);
        kafkaTemplate.send(walletRealtimeWithMarkPriceTopic, symbol, payload);
    }

    private void sendKafkaToUpdatePosition(String symbol, BigDecimal markPrice, BigDecimal indexPrice) throws JsonProcessingException {
        UpdatePositionWithMarkPrice message = UpdatePositionWithMarkPrice.builder()
                .symbol(symbol)
                .markPrice(markPrice)
                .indexPrice(indexPrice)
                .build();
        String payload = new ObjectMapper().writeValueAsString(message);
        log.info("[kafka-producer-liquidation-notification] Sending notification to topic {}: {}", positionUpdateWithMarkPriceTopic, payload);
        kafkaTemplate.send(positionUpdateWithMarkPriceTopic, symbol, payload);
    }

    @Scheduled(fixedDelay = 5000)
    public void pushCoinInfoJob() {
        log.info("=============START JOB pushCoinInfoJob===========");
        for (Map.Entry<String, ContractCoinInfo> entry : contractCoinInfoMap.entrySet()) {
            String symbol = entry.getKey();
            ContractCoinInfo coinInfo = entry.getValue();

            log.debug("pushCoinInfo: symbol: {}", symbol);
            pushSymbolInfoToWebSocket(symbol, coinInfo);
            log.debug("pushCoinInfo completed for symbol: {}", symbol);
        }
        log.info("=============End JOB pushCoinInfoJob===========");
    }

    public void mockOrderBook(String symbol) {
        platesLock.lock();
        try {
            if (orderBookFutureStorage.getOrderBookMap().containsKey(symbol)) {
                return;
            }
            OrderBookUtils.PriceLevelDto bid1 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(25000))
                    .volume(new BigDecimal(0.5))
                    .orderCount(1)
                    .build();
            OrderBookUtils.PriceLevelDto bid2 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(24990))
                    .volume(new BigDecimal(0.8).setScale(8, BigDecimal.ROUND_HALF_UP))
                    .orderCount(1)
                    .build();
            OrderBookUtils.PriceLevelDto bid3 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(24980))
                    .volume(new BigDecimal(1))
                    .orderCount(1)
                    .build();

            OrderBookUtils.PriceLevelDto bid4 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(25200))
                    .volume(new BigDecimal(1))
                    .orderCount(1)
                    .build();


            OrderBookUtils.PriceLevelDto ask1 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(25301))
                    .volume(new BigDecimal(0.8).setScale(8, BigDecimal.ROUND_HALF_UP))
                    .orderCount(1)
                    .build();
            OrderBookUtils.PriceLevelDto ask2 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(25500))
                    .volume(new BigDecimal(0.5).setScale(8, BigDecimal.ROUND_HALF_UP))
                    .orderCount(1)
                    .build();
            OrderBookUtils.PriceLevelDto ask3 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(25600))
                    .volume(new BigDecimal(0.5))
                    .orderCount(1)
                    .build();

            OrderBookUtils.PriceLevelDto ask4 = OrderBookUtils.PriceLevelDto.builder()
                    .price(new BigDecimal(25201))
                    .volume(new BigDecimal(1.2).setScale(8, BigDecimal.ROUND_HALF_UP))
                    .orderCount(1)
                    .build();


            List<OrderBookUtils.PriceLevelDto> bids = Arrays.asList(bid1, bid2, bid3, bid4);
            bids.sort((p1, p2) -> p2.getPrice().compareTo(p1.getPrice()));
            Map<String, Object> btcOrderBook = new HashMap<>();
            btcOrderBook.put(CommonConstants.FUTURE_MATCHING_ENGINE_BID, bids);

            List<OrderBookUtils.PriceLevelDto> asks = Arrays.asList(ask1, ask2, ask3, ask4);
            asks.sort((p1, p2) -> p1.getPrice().compareTo(p2.getPrice()));
            btcOrderBook.put(CommonConstants.FUTURE_MATCHING_ENGINE_ASK, asks);
            Map<String, Object> list = orderBookFutureStorage.getOrderBookMap().put(symbol, btcOrderBook);
        } finally {
            platesLock.unlock();
        }
    }

    private void setPriceToRedis(String key, BigDecimal value) {
        redisTemplate.opsForValue().set(key, value.toString());
    }

    private void setContractCoinInfoToRedis(String key, ContractCoinInfo coinInfo) {
        redisEventPublisher.publishImmediate(key, coinInfo, null);
    }

    private void setPairDetailToRedis(String symbol, Map<String, Object> coinInfo) {
        redisTemplate.opsForValue().set(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS, coinInfo);
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getListEnabledSymbol() {
        Object redisValue = redisTemplate.opsForValue().get(CommonConstants.LIST_ENABLED_SYMBOL_KEY_REDIS);
        if (redisValue == null) {
            return new HashMap<>();
        }
        try {
            return (HashMap<String, String>) redisValue;
        } catch (Exception e) {
            // Log error and return empty list or handle appropriately
            log.error("Failed to parse Redis value as JSON list: " + redisValue, e);
            return new HashMap<>();
        }
    }

    public void setUpdateMarkPrice(Boolean status) {
        this.isUpdateMarkPrice = status;
    }

    private Map<String, Object> getPairDetailRedis(String symbol) {
        return objectMapper.convertValue(redisTemplate.opsForValue().get(symbol + CommonConstants.SUFFIX_PAIR_DETAIL_KEY_REDIS), new TypeReference<>() {
        });
    }
}

package com.icetea.lotus.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.client.CexApiClientFactory;
import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.spot.ExchangeCoin;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.processor.DefaultCoinProcessor;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.MarketHandlerService;
import com.icetea.lotus.service.MarketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Automatically synchronize trading pairs in Exchange match trading center
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CoinProcessorJob {

    private final CoinProcessorFactory processorFactory;
    private final ExchangeCoinService coinService;
    private final MarketHandlerService marketHandlerService;
    private final MarketService marketService;
    private final CoinExchangeRate exchangeRate;
    private final CexApiClientFactory cexApiClientFactory;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${cex-services.matching-engine}")
    private String matchingEngine;

    /**
     * 1-minute timer, every 1 minute
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void synchronizeExchangeCenter() {
        log.info("========CoinProcessorJob========> synchronize the exchange coin pairs");
        // Get the currency supported by the Matching Trading Center
        String apiUri = "/matching-engine/monitor/engines";
        ResponseEntity<Map<String, Integer>> response;
        try {
            response = cexApiClientFactory.getClient(matchingEngine).get(
                    apiUri,
                    new ParameterizedTypeReference<>() {
                    }
            );
            log.info("synchronizeExchangeCenter response after call exchange: {}", response);
        } catch (Exception e) {
            log.error("synchronizeExchangeCenter ERROR: {}", e.getMessage());
            throw e;
        }

        Map<String, Integer> exchangeCenterCoins = response.getBody();
        log.info("Parsed response: {}", exchangeCenterCoins);

        try {
            log.info("========CoinProcessorJob========> now exchange support coins:{}", objectMapper.writeValueAsString(exchangeCenterCoins));
        } catch (JsonProcessingException e) {
            log.error("Error serializing exchangeCenterCoins", e);
        }
        Map<String, CoinProcessor> processorMap = processorFactory.getProcessorMap();

        log.info("========CoinProcessorJob========> now market support coins");

        // Determine whether the currency that exists in the matching trading center exists in the market
        assert exchangeCenterCoins != null;
        for (Map.Entry<String, Integer> coin : exchangeCenterCoins.entrySet()) {
            String symbol = coin.getKey();
            Integer status = coin.getValue();
            // Is there a processor for this currency?
            if (isProcessorForCurrency(processorMap, symbol, status)) continue;

            // Is this currency present in the database?
            ExchangeCoin focusCoin = coinService.findBySymbol(symbol);
            if (focusCoin != null) {
                log.info("============[Start]initialized New CoinProcessor({}) start=====================", symbol);
                // Create a new Processor
                CoinProcessor processor = new DefaultCoinProcessor(symbol, focusCoin.getBaseSymbol(), redisTemplate);
                processor.setMarketHandlerService(marketHandlerService);
                processor.setMarketService(marketService);
                processor.setExchangeRate(exchangeRate);
                processor.getKlineAdapter().setMarketHandlerService(marketHandlerService);
                processor.initializeThumb();
                processor.initializeUsdRate();
                processor.setIsHalt(false);

                if (status == 2) {
                    processor.getKlineAdapter().setIsStopKLine(true);
                }
                processorFactory.addProcessor(symbol, processor);

                log.info("============[End]initialized  New CoinProcessor({}) end=====================", symbol);
            }
        }
    }

    private static boolean isProcessorForCurrency(Map<String, CoinProcessor> processorMap, String symbol, Integer status) {
        if (processorMap.containsKey(symbol)) {
            CoinProcessor temProcessor = processorMap.get(symbol);
            if (status == 1) {
                // The matching transaction is enabled, then the K-line should be processed in the market.
                if (temProcessor.getKlineAdapter().isStopKline()) {
                    temProcessor.getKlineAdapter().setIsStopKLine(false);
                    log.info("[Start] {} will start generate KLine.", symbol);
                }
            } else if (status == 2 && !temProcessor.getKlineAdapter().isStopKline()) {
                    log.info("[Stop]{} will stop generate KLine.", symbol);
                    temProcessor.getKlineAdapter().setIsStopKLine(true);
                }

            return true;
        }
        return false;
    }
}

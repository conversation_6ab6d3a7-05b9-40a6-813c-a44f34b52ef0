package com.icetea.lotus.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.icetea.lotus.dto.TopCoinResponseDto;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.service.CoinThumbService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class MarketPushJob {
    private final CoinThumbService coinThumbService;
    private final SimpMessagingTemplate messagingTemplate;

    //push message socket cho top trending, top gainer, newly listed
    @Scheduled(fixedRate = 500)
    @SuppressWarnings("all")
    public void pushThumbCoin() {
        log.info("=============START JOB pushThumbCoin===========");
        List<CoinThumb> listCoinThumb = coinThumbService.findSymbolThumb();
        for (CoinThumb coinThumb : listCoinThumb) {
            CoinThumb responseDto = coinThumbService.getCoinThumb(coinThumb.getSymbol());
            log.info("pushThumbCoin: {}", coinThumb);
            messagingTemplate.convertAndSend("/topic/market/push-thumb/" + coinThumb.getSymbol(), responseDto);
//            messagingTemplate.convertAndSend("/topic/market/newly-listed/" + coinThumb.getSymbol(), responseDto);
//            messagingTemplate.convertAndSend("/topic/market/top-trending/" + coinThumb.getSymbol(), responseDto);
        }
    }

    //    @Scheduled(fixedRate = 500)
    public void pushNewlyListed() {
        log.info("=============START JOB pushNewlyListed===========");
        List<TopCoinResponseDto> newlisted = coinThumbService.findNewlyListed(0, 3).getContent();
        log.info("pushNewlyListed: {}", newlisted);
        messagingTemplate.convertAndSend("/topic/market/newly-listed", newlisted);
    }


    //    @Scheduled(fixedRate = 500)
    public void pushTopTrending() throws JsonProcessingException {
        log.info("=============START JOB pushTopTrending===========");
        List<TopCoinResponseDto> topTrending = coinThumbService.findTopTrending(0, 3).getContent();
        log.info("pushTopTrending: {}", topTrending);
        messagingTemplate.convertAndSend("/topic/market/top-trending", topTrending);
    }
}

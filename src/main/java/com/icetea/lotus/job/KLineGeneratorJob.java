package com.icetea.lotus.job;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.processor.HistoricalKlineProcessor;
import com.icetea.lotus.processor.HistoricalKlineProcessorFactory;
import jakarta.validation.constraints.NotNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.core.task.TaskExecutor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Calendar;

/**
 * Generate K-line information for each time period
 */
@Component
@Slf4j
public class KLineGeneratorJob {

    private final CoinProcessorFactory processorFactory;
    private final HistoricalKlineProcessorFactory historicalKlineProcessorFactory;
    private final TaskExecutor taskExecutor;
    private ObjectMapper objectMapper = new ObjectMapper();

    public KLineGeneratorJob(CoinProcessorFactory processorFactory, 
                           HistoricalKlineProcessorFactory historicalKlineProcessorFactory,
                           TaskExecutor taskExecutor) {
        this.processorFactory = processorFactory;
        this.historicalKlineProcessorFactory = historicalKlineProcessorFactory;
        this.taskExecutor = taskExecutor;
    }

    @NotNull
    private static Calendar getCalendarThenLog(String s) {
        Calendar calendar = Calendar.getInstance();
        log.info(s, calendar.getTime());
        return calendar;
    }

    /**
     * Minute timer, processing minute-based K-lines only
     * Runs every minute to generate K-lines for minute-based timeframes (1min, 5min, 15min, 30min)
     * 
     * IMPORTANT: The time parameter represents the START of the period being processed,
     * which aligns with industry standards and TradingView compatibility.
     * 
     * NOTE: Hour-based and larger periods are handled by dedicated scheduled methods
     * to avoid duplicate generation.
     */
    @KafkaListener(topics = "${topic-kafka.jobs.generate-kline-minute}", containerFactory = "kafkaListenerContainerFactory")
    public void handleMinuteKLines(String message) {
        log.info("Starting Kline Generator Job {} {}", LocalDateTime.now(), message);

        // Get the current time, which is the trigger time for the job (e.g., 10:30:00)
        Calendar calendar = getTimeFromMessage(message);
        final int minute = calendar.get(Calendar.MINUTE);

        // Standardize the trigger time to the beginning of the current minute
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        final long triggerTime = calendar.getTimeInMillis();

        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (processor.getKlineAdapter().isStopKline()) {
                return; // Skip if kline processing is stopped for this symbol
            }

            taskExecutor.execute(() -> {
                try {
                    HistoricalKlineProcessor historicalProcessor = historicalKlineProcessorFactory.getProcessor(symbol);

                    // --- Generate 1-minute K-line (always) ---
                    // The period to process is the minute that just ended.
                    long periodStartTime1m = triggerTime - 60 * 1000; // Subtract 1 minute
                    historicalProcessor.generateKLine(1, Calendar.MINUTE, periodStartTime1m);
                    log.debug("Generated 1-minute K-line for symbol: {} at time: {}", symbol, periodStartTime1m);

                    // --- Generate 5-minute K-line ---
                    if (minute % 5 == 0) {
                        long periodStartTime5m = triggerTime - 5 * 60 * 1000; // Subtract 5 minutes
                        historicalProcessor.generateKLine(5, Calendar.MINUTE, periodStartTime5m);
                        log.debug("Generated 5-minute K-line for symbol: {} at time: {}", symbol, periodStartTime5m);
                    }

                    // --- Generate 15-minute K-line ---
                    if (minute % 15 == 0) {
                        long periodStartTime15m = triggerTime - 15 * 60 * 1000; // Subtract 15 minutes
                        historicalProcessor.generateKLine(15, Calendar.MINUTE, periodStartTime15m);
                        log.debug("Generated 15-minute K-line for symbol: {} at time: {}", symbol, periodStartTime15m);
                    }

                    // --- Generate 30-minute K-line ---
                    if (minute % 30 == 0) {
                        long periodStartTime30m = triggerTime - 30 * 60 * 1000; // Subtract 30 minutes
                        historicalProcessor.generateKLine(30, Calendar.MINUTE, periodStartTime30m);
                        log.debug("Generated 30-minute K-line for symbol: {} at time: {}", symbol, periodStartTime30m);
                    }

                } catch (Exception e) {
                    log.error("Error generating historical minute-based K-lines for symbol: {} at trigger time: {}: {}",
                            symbol, triggerTime, e.getMessage(), e);
                }
            });
        });
    }

    @SneakyThrows
    private Calendar getTimeFromMessage(String message) {
        JsonNode request = objectMapper.readTree(message);
        long currentTime = request.get("currentTime").asLong();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currentTime);
        return calendar;
    }

    /**
     * Run every hour - processes the previous completed hour period
     * Time parameter represents the START of the hour period being processed
     */
    @Scheduled(cron = "0 0 * * * *")
    @SchedulerLock(name = "kline_generator_hour", lockAtLeastFor = "55m", lockAtMostFor = "70m")
    public void handleHourKLine() {
        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.getKlineAdapter().isStopKline()) {
                Calendar calendar = getCalendarThenLog("Hour K-line:{}");

                // Calculate the START time of the previous completed hour period
                calendar.add(Calendar.HOUR_OF_DAY, -1);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long periodStartTime = calendar.getTimeInMillis();

                // Use HistoricalKlineProcessor for persisted K-line generation
                taskExecutor.execute(() -> {
                    try {
                        HistoricalKlineProcessor historicalProcessor = historicalKlineProcessorFactory.getProcessor(symbol);
                        historicalProcessor.generateKLine(1, Calendar.HOUR_OF_DAY, periodStartTime);
                        log.debug("Generated historical hourly K-line for symbol: {} at time: {}", symbol, periodStartTime);
                    } catch (Exception e) {
                        log.error("Error generating historical hourly K-line for symbol: {} at time: {}: {}", 
                                symbol, periodStartTime, e.getMessage(), e);
                    }
                });
            }
        });
    }

    /**
     * Run every 4 hours - processes the previous completed 4-hour period
     * Time parameter represents the START of the 4-hour period being processed
     */
    @Scheduled(cron = "0 0 0/4 * * ?")
    @SchedulerLock(name = "kline_generator_4hour", lockAtLeastFor = "PT3H50M", lockAtMostFor = "PT4H30M")
    public void handleHourKLine4Hour() {
        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.getKlineAdapter().isStopKline()) {
                Calendar calendar = getCalendarThenLog("4-Hour K-line:{}");

                // Calculate the START time of the previous completed 4-hour period
                calendar.add(Calendar.HOUR_OF_DAY, -4);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long periodStartTime = calendar.getTimeInMillis();

                // Use HistoricalKlineProcessor for persisted K-line generation
                taskExecutor.execute(() -> {
                    try {
                        HistoricalKlineProcessor historicalProcessor = historicalKlineProcessorFactory.getProcessor(symbol);
                        historicalProcessor.generateKLine(4, Calendar.HOUR_OF_DAY, periodStartTime);
                        log.debug("Generated historical 4-hour K-line for symbol: {} at time: {}", symbol, periodStartTime);
                    } catch (Exception e) {
                        log.error("Error generating historical 4-hour K-line for symbol: {} at time: {}: {}", 
                                symbol, periodStartTime, e.getMessage(), e);
                    }
                });
            }
        });
    }

    /**
     * Daily 0 o'clock processor, processing daily, weekly, and monthly K-lines
     * Time parameter represents the START of the period being processed
     */
    @Scheduled(cron = "0 0 0 * * *")
    @SchedulerLock(name = "kline_generator_daily", lockAtLeastFor = "23h", lockAtMostFor = "25h")
    public void handleDayKLine() {
        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.getKlineAdapter().isStopKline()) {
                Calendar calendar = getCalendarThenLog("Daily K-line:{}");

                // Calculate the START time of the previous completed day period
                calendar.add(Calendar.DAY_OF_YEAR, -1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long periodStartTime = calendar.getTimeInMillis();

                // Get day info for the period being processed
                int week = calendar.get(Calendar.DAY_OF_WEEK);

                // Use HistoricalKlineProcessor for persisted K-line generation
                taskExecutor.execute(() -> {
                    try {
                        HistoricalKlineProcessor historicalProcessor = historicalKlineProcessorFactory.getProcessor(symbol);

                        // Generate weekly K-line if it's Sunday (week == 1 in Calendar.DAY_OF_WEEK)
                        if (week == 1) {
                            long startTimeOfWeek = getStartOfWeek(calendar);
                            historicalProcessor.generateKLine(1, Calendar.WEEK_OF_MONTH, startTimeOfWeek);
                            log.debug("Generated historical weekly K-line for symbol: {} at time: {}", symbol, periodStartTime);
                        }

                        // Generate monthly K-line if it's the last day of the month
                        // Check if tomorrow would be day 1 of next month
                        Calendar nextDay = (Calendar) calendar.clone();
                        nextDay.add(Calendar.DAY_OF_YEAR, 1);
                        if (nextDay.get(Calendar.DAY_OF_MONTH) == 1) {
                            historicalProcessor.generateKLine(1, Calendar.MONTH, periodStartTime);
                            log.debug("Generated historical monthly K-line for symbol: {} at time: {}", symbol, periodStartTime);
                        }

                        // Always generate daily K-line
                        historicalProcessor.generateKLine(1, Calendar.DAY_OF_YEAR, periodStartTime);
                        log.debug("Generated historical daily K-line for symbol: {} at time: {}", symbol, periodStartTime);

                    } catch (Exception e) {
                        log.error("Error generating historical daily/weekly/monthly K-lines for symbol: {} at time: {}: {}", 
                                symbol, periodStartTime, e.getMessage(), e);
                    }
                });
            }
        });
    }

    private long getStartOfWeek(Calendar calendar) {
        // Clone để không ảnh hưởng đến calendar gốc
        Calendar startOfWeek = (Calendar) calendar.clone();

        // Lùi về thứ 2 của tuần trước
        startOfWeek.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        startOfWeek.set(Calendar.HOUR_OF_DAY, 0);
        startOfWeek.set(Calendar.MINUTE, 0);
        startOfWeek.set(Calendar.SECOND, 0);
        startOfWeek.set(Calendar.MILLISECOND, 0);

        return startOfWeek.getTimeInMillis();
    }
}

package com.icetea.lotus.socket.client;

import com.icetea.lotus.socket.ws.WebSocketHuobi;
import com.icetea.lotus.util.WebSocketConnectionManage;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URISyntaxException;

@Slf4j
public class WsClientHuobi {

    public static final String WSS_API_HUOBI_PRO_WS = "wss://api.huobi.pro/ws";

    public void run() {
        try {
            // The address that is not blocked in China/wss://api.huobi.pro/ws ws://api.huobi.br.com:443/ws wss://api.huobiasia.vip/ws
            URI uri = new URI(WSS_API_HUOBI_PRO_WS);
            WebSocketHuobi ws = new WebSocketHuobi(uri);
            WebSocketConnectionManage.setWebSocket(ws);
            WebSocketConnectionManage.getClient().connect(ws);

        } catch (URISyntaxException e) {
            log.info("Unexpected error occurred", e);
        }
    }
}

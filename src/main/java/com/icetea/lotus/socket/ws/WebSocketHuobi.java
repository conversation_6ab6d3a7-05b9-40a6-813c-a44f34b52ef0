package com.icetea.lotus.socket.ws;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.util.ZipUtils;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.enums.ReadyState;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

@Slf4j
@SuppressWarnings("all")
public class WebSocketHuobi extends WebSocketClient {

    public static String DEPTH = "market.%s.depth.step0"; // depth
    public static String KLINE = "market.%s.kline.%s"; // K-line
    public static String DETAIL = "market.%s.detail"; // Market summary (latest price, transaction volume, etc.)
    public static String TRADE = "market.%s.trade.detail"; // Details of transactions
    public static String[] PERIOD = {"1min", "5min", "15min", "30min", "60min", "4hour", "1day", "1mon", "1week"};
    private final Logger logger = LoggerFactory.getLogger(WebSocketClient.class);
    private final double VOLUME_PERCENT = 0.13; // Percentage of Huobi's trading volume
    private final ObjectMapper objectMapper = new ObjectMapper();

    @SuppressWarnings("all")
    public WebSocketHuobi(URI uri
//            , KlineRobotMarketService service
    ) {
        super(uri);
        this.uri = uri;
//        this.marketService = service;
    }

    @Override
    public void onOpen(ServerHandshake shake) {
        log.info("onOpen");
    }


    // Synchronous K-line
    public void reqKLineList(String symbol, String period, long from, long to) {
        String topic = String.format(KLINE, symbol.replace("/", "").toLowerCase(), period);

        // Huobi Websocket requires that the single request cannot exceed 300 pieces of data, so the request needs to be split in one go.
        long timeGap = to - from; // Time difference
        long divideTime = getDivideTime(period);

        if (timeGap > divideTime && divideTime != 0) {
            long times = timeGap % (divideTime) > 0 ? (timeGap / (divideTime) + 1) : timeGap / (divideTime);
            long temTo = from;
            long temFrom = from;
            for (int i = 0; i < times; i++) {
                if (temTo + (divideTime) > to) {
                    temTo = to;
                } else {
                    temTo = temTo + (divideTime);
                }
                sendWsMarket("req", topic, temFrom, temTo);
                temFrom = temFrom + divideTime;
            }
        } else {
            sendWsMarket("req", topic, from, to);
        }
    }

    private static long getDivideTime(String period) {
        long divideTime = 0;
        if (period.equals("1min")) {
            divideTime = (long) 60 * 300; // 1 minute * 300 items
        }
        if (period.equals("5min")) {
            divideTime = (long) 5 * 60 * 300;
        }
        if (period.equals("15min")) {
            divideTime = (long) 15 * 60 * 300;
        }
        if (period.equals("30min")) {
            divideTime = (long) 30 * 60 * 300;
        }
        if (period.equals("60min")) {
            divideTime = (long) 60 * 60 * 300;
        }
        if (period.equals("4hour")) {
            divideTime = (long) 4 * 60 * 60 * 300;
        }
        if (period.equals("1day")) {
            divideTime = (long) 24 * 60 * 60 * 300;
        }
        if (period.equals("1week")) {
            divideTime = (long) 7 * 24 * 60 * 60 * 300;
        }
        if (period.equals("1mon")) {
            divideTime = (long) 30 * 24 * 60 * 60 * 300;
        }

        return divideTime;
    }

    @Override
    public void onMessage(String arg0) {
        if (arg0 != null) {
            logger.info("[WebSocketHuobi] receive message: {}", arg0);
        }
    }

    @Override
    @SuppressWarnings("all")
    public void onError(Exception arg0) {
        logger.error("[WebSocketHuobi] has error ,the message is :: {}", arg0.getMessage());
        arg0.printStackTrace();
        String message = "";
        try {
            message = new String(arg0.getMessage().getBytes(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("[WebSocketHuobi] has error ,the message is :: {}", message);
        }
    }

    @Override
    public void onClose(int arg0, String arg1, boolean arg2) {
        logger.info("[WebSocketHuobi] connection close: {} - {} - {}", arg0, arg1, arg2);
        int tryTimes = 0;
        // Try 20 times
        logger.info("[WebSocketHuobi] Try to reconnect,{}second rate", tryTimes);
        if (this.getReadyState().equals(ReadyState.NOT_YET_CONNECTED) || this.getReadyState().equals(ReadyState.CLOSED) || this.getReadyState().equals(ReadyState.CLOSING)) {

            Runnable sendable = new Runnable() {
                @Override
                public void run() {
                    logger.info("[WebSocketHuobi] Enable reconnection");
                    reconnect();
                }
            };
            new Thread(sendable).start();
        }
    }

    @Override
    @SuppressWarnings("all")
    public void onMessage(ByteBuffer bytes) {
        try {
            String message = new String(ZipUtils.decompress(bytes.array()), StandardCharsets.UTF_8);

            if (!message.isEmpty()) {
                JsonNode jsonNode = objectMapper.readTree(message);
                if (message.indexOf("ping") > 0) {
                    String pong = jsonNode.toString().replace("ping", "pong");
                    send(pong);
                } else {
                    String id = "";
                    if (jsonNode.has("ch")) {
                        id = jsonNode.path("ch").asText();
                        if (id == null || id.split("\\.").length < 3) {
                            return;
                        }
                    }
                    if (jsonNode.has("rep")) {
                        id = jsonNode.path("rep").asText();
                        if (id == null || id.split("\\.").length < 3) {
                            return;
                        }
                    }
                    if (id.isEmpty()) {
                        return;
                    }
                    StringBuilder sb = new StringBuilder(id.split("\\.")[1]);
                    String symbol = "";
                    if (sb.indexOf("eth") > 1) {
                        symbol = sb.insert(sb.indexOf("eth"), "/").toString().toUpperCase();
                    } else if (sb.indexOf("btc") > 1) {
                        symbol = sb.insert(sb.indexOf("btc"), "/").toString().toUpperCase();
                    } else if (sb.indexOf("usdt") > 1) {
                        symbol = sb.insert(sb.indexOf("usdt"), "/").toString().toUpperCase();
                    }

                    String type = id.split("\\.")[2];

                    handleKLineByType(type, id, jsonNode);
                }
            }
        } catch (Exception e) {
            logger.error("[WebSocketHuobi] websocket exception: {}", e.getMessage());
        }
    }

    private void handleKLineByType(String type, String id, JsonNode jsonNode) {
        if (type.equals("kline")) {
            String period = id.split("\\.")[3];
            JsonNode dataNode = jsonNode.path("data");

            if (dataNode != null && !dataNode.isMissingNode() && dataNode.isArray()) {
                for (int i = 0; i < dataNode.size(); i++) {
                    JsonNode klineObj = dataNode.get(i);

                    BigDecimal open = new BigDecimal(klineObj.path("open").asText());
                    BigDecimal close = new BigDecimal(klineObj.path("close").asText());
                    BigDecimal high = new BigDecimal(klineObj.path("high").asText());
                    BigDecimal low = new BigDecimal(klineObj.path("low").asText());
                    BigDecimal amount = new BigDecimal(klineObj.path("amount").asText());
                    BigDecimal vol = new BigDecimal(klineObj.path("vol").asText());
                    int count = klineObj.path("count").asInt();
                    long time = klineObj.path("id").asLong();

                    KLine kline = new KLine(period);
                    kline.setClosePrice(close);
                    kline.setCount(count);
                    kline.setHighestPrice(high);
                    kline.setLowestPrice(low);
                    kline.setOpenPrice(open);
                    kline.setTime(time * 1000);
                    kline.setTurnover(amount.multiply(BigDecimal.valueOf(VOLUME_PERCENT)));
                    kline.setVolume(vol.multiply(BigDecimal.valueOf(VOLUME_PERCENT)));
                }
            }
        }
    }

    public void sendWsMarket(String op, String topic) {
        ObjectNode req = objectMapper.createObjectNode();
        req.put(op, topic);
        try {
            send(objectMapper.writeValueAsString(req));
        } catch (JsonProcessingException e) {
            logger.error("[WebSocketHuobi] Error serializing request: {}", e.getMessage());
        }
    }

    public void sendWsMarket(String op, String topic, long from, long to) {
        ObjectNode req = objectMapper.createObjectNode();
        req.put(op, topic);
        req.put("from", from);
        req.put("to", to);
        try {
            send(objectMapper.writeValueAsString(req));
        } catch (JsonProcessingException e) {
            logger.error("[WebSocketHuobi] Error serializing request: {}", e.getMessage());
        }
    }
}

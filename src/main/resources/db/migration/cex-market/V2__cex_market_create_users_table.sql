-- Example migration script for user-service
-- This script creates a users table in the application's schema

-- Create the application schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS ${schema_name};

-- Create a users table in the application schema
CREATE TABLE IF NOT EXISTS ${schema_name}.users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50),
    last_name VA<PERSON><PERSON><PERSON>(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add a comment to the table
COMMENT ON TABLE ${schema_name}.users IS 'Users table created by Flyway migration for user-service';

-- Insert some sample data
INSERT INTO ${schema_name}.users (username, email, password_hash, first_name, last_name)
VALUES 
    ('john_doe', '<EMAIL>', 'hashed_password_1', '<PERSON>', 'Doe'),
    ('jane_smith', '<EMAIL>', 'hashed_password_2', 'Jane', 'Smith');
server:
  port: ${SERVER_PORT:6004}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/market}

spring:

  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: flyway

  application:
    name: market

  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      auto-commit: true
      leak-detection-threshold: 2000
  jpa:
    database:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
    show-sql: ${SHOW_SQL:false}
    hibernate:
      ddl-auto: ${HIBERNATE_DDL_AUTO:none}
  devtools:
    restart:
      enabled: true

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 60s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.uuid}
        register-health-check: ${CONSUL_REGISTER_HEALTH_CHECK:true}
        # Add these properties to fix IP registration
        hostname: ${POD_IP:${spring.cloud.client.ip-address:localhost}}
        ip-address: ${POD_IP:${spring.cloud.client.ip-address:127.0.0.1}}
        prefer-agent-address: false
        deregister: true
        heartbeat:
          enabled: true
        # Service mesh configuration
        tags:
          - "connect-enabled"
          - "v1"
          - "market"
          - "version=${APP_VERSION:latest}"
          - "environment=${ENVIRONMENT:dev}"
          - "service-mesh=enabled"
        metadata:
          connect-enabled: "true"
          service-mesh: "enabled"
          service-type: "market"
          service-port: "6004"
          mesh-enabled: "true"

management:
  tracing:
    sampling:
      probability: 1.0
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true
    tags:
      application: ${spring.application.name}

  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
    redis:
      enabled: false
    mongo:
      enabled: false
  security:
    enabled: false
  context-path: /actuator

aqmd:
  netty:
    port: 28901
    bossThreadSize: 1
    worker-thread-size: 3
    packetHeaderLength: 4
    max-frame-length: **********
    writer-idle: 200
    max-timeout: 60
    defaultTimeout: 30
    deal-handler-thread-size: 10
    serviceLoggerLevel: debug
    direct-access-flag: 1
    direct-access-command: 20001,20002,20021,20022
    websocket-flag: 1
    websocket-port: 28985

endpoints:
  health:
    sensitive: false
    enabled: true
  info:
    sensitive: false
  metrics:
    sensitive: false


aliyun:
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

spark:
  system:
    name: BIZZAN

second:
  referrer:
    award: false
openapi:
  service:
    api-docs: market
    server: http://localhost:8080
    title: API market Service
    version: 1.0.0

topic-kafka:
  minus:
    wallet-spot: minus-balance-wallet-spot
  contract:
    position-update-with-mark-price: contract-position-update-with-mark-price
    wallet-realtime-with-mark-price: contract-wallet-realtime-with-mark-price

sms:
  driver: ${SMS_DRIVER:sns-aws}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8C822F8}
  sign: ${SMS_SIGN:Bitcello Support Team}
  internationalGateway: ${SMS_INTERNATIONAL_GATEWAY:}
  internationalUsername: ${SMS_INTERNATIONAL_USERNAME:}
  internationalPassword: ${SMS_INTERNATIONAL_PASSWORD:}

aws:
  sns:
    accessKeyId: ${AWS_ACCESS_KEY_ID:}
    secretAccessKey: ${AWS_SECRET_ACCESS_KEY:}
    region: ${AWS_REGION:ap-southeast-1}             # hoặc region khác như ap-southeast-1
    senderName: Bitcello          # tên gửi, nếu áp dụng
    maxPrice: "0.50"              # giá tối đa cho 1 SMS (USD)
    defaultSmsType: Transactional # hoặc Promotional nếu bạn gửi quảng cáo
spring:

  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:3t4sQnnjmABglH4DDTH2m5ANIiDJuigH9VW919pU42knQWGZnJidLBHyjFoRRfYZ}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql

  data:
    mongodb:
      #      uri: ${SPRING_MONGODB_URI:*************************************************************************************************************************************************************************************************************************
      uri: ************************************************************************************************************************************************************************************************************************
      database: ${SPOT_MONGODB_DATABASE:spot}
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
      #      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      connect-timeout: ${REDIS_TIMEOUT:30000}
      jedis:
        pool:
          min-idle: 20
          max-idle: 100
          max-wait: 60000
          max-active: 300

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:31226}
    properties:
      sasl.mechanism: PLAIN
    listener:
      concurrency: 9
      type: batch
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:3}
      batch:
        size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer:
        memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      enable-auto-commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      #      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      # Unique client ID for this consumer
      client-id: ${spring.application.name}-consumer
      # Consumer group ID
      group-id: ${spring.application.name}-group
      max-poll-records: 50
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer


  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 10s
        health-check-critical-timeout: 5m
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.int}
        register-health-check: ${CONSUL_REGISTER_HEALTH_CHECK:true}
        # Add these properties to fix IP registration
        hostname: ${POD_IP:${spring.cloud.client.ip-address:localhost}}
        ip-address: ${POD_IP:${spring.cloud.client.ip-address:127.0.0.1}}
        prefer-agent-address: false
        deregister: true
        # Service mesh configuration
        tags:
          - "connect-enabled"
          - "v1"
          - "market"
          - "version=${APP_VERSION:latest}"
          - "environment=${ENVIRONMENT:dev}"
          - "service-mesh=enabled"
        metadata:
          connect-enabled: "true"
          service-mesh: "enabled"
          service-type: "market"
          service-port: "6004"
          mesh-enabled: "true"

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}/realms/cex-lotus
            token-uri: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}/realms/cex-lotus/protocol/openid-connect/token

cex-services:
  matching-engine: "matching-engine"

keycloak:
  auth-server-url: ${KEYCLOAK_ISSUER_URI:https://keycloak.dev-glyph.click}
  realm: cex-lotus
  resource: cex-market
  credentials:
    secret: 12p3dUlch4lyr1ptEBnC7G0v3ILqOFQQ

# Security configuration
cex-security:
  resource-server-enabled: true
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/btc/trend"
    - "/symbol-thumb-trend"
    - "/market-ws/**"
    - "/market/market-ws/**"
    - "/top-gainer"
    - "/newly-listed"
    - "/top-trending"
    - "/coin-list"
    - "/coin-info"
    - "/symbol-thumb"
    - "/symbol-info"
    - "/symbol"
    - "/overview"
    - "/engines"
    - "/coin/market-overview"
    - "/history"
    - "/latest-trade"
    - "/exchange-plate"
    - "/exchange-plate-full"
    - "/exchange-plate-mini"
    - "/add_dcitionary/**"
    - "btc/trend"
    - "/future-order-book"
    - "/test/ws/ft-order-book"
    - "contracts/test/**"

# kafka topic configuration
topic-kafka:
  exchange:
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success}
    order-cancel-all-completed: ${EXCHANGE_ORDER_CANCEL_ALL_COMPLETED:exchange-order-cancel-all-completed}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed}
    trade: ${EXCHANGE_TRADE:exchange-trade}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate}
    stop-order-triggered: ${EXCHANGE_STOP_ORDER_TRIGGERED:exchange-stop-order-triggered}
  contract:
    trade-plate: ${CONTRACT_TRADE_PLATE:contract-trade-plate}
    position-update-with-mark-price: contract-position-update-with-mark-price
    wallet-realtime-with-mark-price: contract-wallet-realtime-with-mark-price
  jobs:
    generate-kline-minute: spot-generate-kline-minute

handle-trade:
  use-old-version: false
aws:
  sns:
    accessKeyId: ${AWS_ACCESS_KEY_ID:}
    secretAccessKey: ${AWS_SECRET_ACCESS_KEY:}
    region: ${AWS_REGION:ap-southeast-1}             # hoặc region khác như ap-southeast-1
    senderName: Bitcello          # tên gửi, nếu áp dụng
    maxPrice: "0.50"              # giá tối đa cho 1 SMS (USD)
    defaultSmsType: Transactional # hoặc Promotional nếu bạn gửi quảng cáo

sms:
  driver: ${SMS_DRIVER:sns-aws}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:1800000000}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8C822F8}
  sign: ${SMS_SIGN:Bitcello Support Team}
  internationalGateway: ${SMS_INTERNATIONAL_GATEWAY:}
  internationalUsername: ${SMS_INTERNATIONAL_USERNAME:}
  internationalPassword: ${SMS_INTERNATIONAL_PASSWORD:}

es:
  client:
    enabled: false
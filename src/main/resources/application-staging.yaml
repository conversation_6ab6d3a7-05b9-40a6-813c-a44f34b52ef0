spring:

  datasource:
    # Use the internal Kubernetes service name for in-cluster access
    url: ${SPOT_DATASOURCE_URL:*****************************************************************}
    username: ${SPOT_DATASOURCE_USERNAME:spot_user}
    password: ${SPOT_DATASOURCE_PASSWORD:d2mwpsfU4fhSDvysiC60kwIXL3Zjojf6I9DlZJE1FUxYdGMgFPXOYZl0p6LidZnA}
    driver-class-name: org.postgresql.Driver

    # HikariCP connection pool configuration
    hikari:
      # Optimal pool size based on the PostgreSQL max_connections (300)
      # Formula: (core_count * 2) + effective_spindle_count
      maximum-pool-size: 50
      minimum-idle: 10
      # Set the connection timeout to 30 seconds
      connection-timeout: 30000
      # Set idle timeout to 10 minutes
      idle-timeout: 600000
      # Set max lifetime to 30 minutes
      max-lifetime: 1800000
      # Enable auto-commit
      auto-commit: true
      # Pool name for easier identification in monitoring
      pool-name: HikariPool-PostgreSQL
      # TCP keepalive settings matching PostgreSQL configuration
      data-source-properties:
        tcpKeepAlive: true
        socketTimeout: 60
        connectTimeout: 30

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQL10Dialect
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        # Batch size for better performance
        jdbc.batch_size: 50
        # Order inserts for better performance
        order_inserts: true
        # Order updates for better performance
        order_updates: true
        # Format SQL for better readability in logs
        format_sql: false
        # Show SQL in logs (disable in production)
        show_sql: false
        # Generate statistics
        generate_statistics: false
        # Second-level cache configuration
        cache:
          use_second_level_cache: false
          use_query_cache: false
        # Connection handling
        connection:
          provider_disables_autocommit: false
        # C3P0 connection pool integration
        c3p0:
          min_size: 5
          max_size: 20
          timeout: 1800
          max_statements: 50

  # Transaction management
  transaction:
    default-timeout: 30s

  threads:
    virtual:
      enabled: on

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:mongodb://spot_user:<EMAIL>:27017,bitcello-mongodb-cluster-1.bitcello-mongodb-cluster-svc.mongodb.svc.cluster.local:27017,bitcello-mongodb-cluster-2.bitcello-mongodb-cluster-svc.mongodb.svc.cluster.local:27017/spot?replicaSet=bitcello-mongodb-cluster&authSource=spot&readPreference=primary&w=majority&retryWrites=true&serverSelectionTimeoutMS=5000&connectTimeoutMS=10000&socketTimeoutMS=20000}
    redis:
      host: ${REDIS_HOST:}
      port: ${REDIS_PORT:}
      database: ${REDIS_DB:0}
      timeout: 2000
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 2


  kafka:
    # Bootstrap server configuration
    bootstrap-servers: ${KAFKA_BOOTSTRAP:bitcello-kafka-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092}
    # Producer configuration
    producer:
      # Unique client ID for this producer
      client-id: ${spring.application.name}-producer
      # Key and value serializers
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # Acknowledgment configuration
      acks: all
      # Retry configuration
      retries: 3
      # Batch size in bytes
      batch-size: 16384
      # Buffer memory in bytes
      buffer-memory: 33554432
      # Compression type
      compression-type: snappy
      # Additional producer properties
      properties:
        # Idempotence ensures exactly-once delivery semantics
        enable.idempotence: true
        # Maximum in-flight requests per connection
        max.in.flight.requests.per.connection: 5
        # Request timeout in ms
        request.timeout.ms: 30000
        # Delivery timeout in ms
        delivery.timeout.ms: 120000
        # Linger time in ms
        linger.ms: 5
        # TCP keepalive settings
        connections.max.idle.ms: 540000

    # Consumer configuration
    consumer:
      # Unique client ID for this consumer
      client-id: ${spring.application.name}-consumer
      # Consumer group ID
      group-id: ${spring.application.name}-group
      # Key and value deserializers
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # Auto-offset reset strategy
      auto-offset-reset: earliest
      # Enable auto-commit
      enable-auto-commit: false
      # Auto-commit interval in ms
      auto-commit-interval: 5000
      # Maximum poll records
      max-poll-records: 500
      # Additional consumer properties
      properties:
        # Isolation level for reading transactional messages
        isolation.level: read_committed
        # Fetch minimum bytes
        fetch.min.bytes: 1
        # Fetch maximum wait time in ms
        fetch.max.wait.ms: 500
        # Heartbeat interval in ms
        heartbeat.interval.ms: 3000
        # Session timeout in ms
        session.timeout.ms: 30000
        # Maximum poll interval in ms
        max.poll.interval.ms: 300000
        # Trusted packages for deserialization
        spring.json.trusted.packages: "*"

    # Admin configuration
    admin:
      # Additional admin properties
      properties:
        # Retry configuration
        retries: 5
        # Retry backoff in ms
        retry.backoff.ms: 1000

    # Listener configuration
    listener:
      # Concurrency level
      concurrency: 3
      # Ack mode
      ack-mode: MANUAL_IMMEDIATE
      # Missing topics behavior
      missing-topics-fatal: false
      # Poll timeout in ms
      poll-timeout: 5000
      # Idle event interval in ms
      idle-event-interval: 30000
      # Monitor interval in ms
      monitor-interval: 30000
      # Log container configuration
      log-container-config: true
      # Type of listener
      type: BATCH
      # Immediate shutdown
      immediate-stop: false

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:30850}
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: ${server.servlet.context-path}/actuator/health
        health-check-interval: 60s
        health-check-critical-timeout: 2m
        prefer-ip-address: true
        instance-id: ${spring.application.name}-staging
        register-health-check: true
        # Add these properties to fix IP registration
        hostname: ${POD_IP:${spring.cloud.client.ip-address:localhost}}
        ip-address: ${POD_IP:${spring.cloud.client.ip-address:127.0.0.1}}
        prefer-agent-address: false
        deregister: true

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082}/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082}/realms/cex-lotus
            token-uri: ${KEYCLOAK_ISSUER_URI:http://************:32082}/realms/cex-lotus/protocol/openid-connect/token

keycloak:
  auth-server-url: ${KEYCLOAK_ISSUER_URI:http://************:32082}
  realm: cex-lotus
  resource: cex-market
  credentials:
    secret: 12p3dUlch4lyr1ptEBnC7G0v3ILqOFQQ

cex-services:
  matching-engine: "matching-engine"

logging:
  level:
    org.hibernate.type.descriptor.sql: DEBUG
  pattern:
    level: '%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]'

cex-security:
  resource-server-enabled: true
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/**"
    - "/btc/trend"
    - "/symbol-thumb-trend"
    - "/market-ws/**"
    - "/market/market-ws/**"
    - "/top-gainer"
    - "/newly-listed"
    - "/top-trending"
    - "/coin-list"
    - "/coin-info"
    - "/symbol-thumb"
    - "/symbol-info"
    - "/symbol"
    - "/overview"
    - "/engines"
    - "/coin/market-overview"
    - "/history"
    - "/latest-trade"
    - "/exchange-plate-mini"
    - "/exchange-plate"
    - "/exchange-plate-full"
    - "/add_dcitionary/**"
    - "btc/trend"
    - "/future-order-book"
    - "contracts/test/**"

handle-trade:
  use-old-version: true

sms:
  driver: ${SMS_DRIVER:twilio}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8C822F8}
  sign: ${SMS_SIGN:Icetea-Software}
  internationalGateway: ${SMS_INTERNATIONAL_GATEWAY:}
  internationalUsername: ${SMS_INTERNATIONAL_USERNAME:}
  internationalPassword: ${SMS_INTERNATIONAL_PASSWORD:}

twilio:
  account_sid: **********************************
  auth_token: 9d6fe9aa6af7e56b72397b219b817e5f
  trial_number: +***********

topic-kafka:
  exchange:
    order-cancel-success: ${EXCHANGE_ORDER_CANCEL_SUCCESS:exchange-order-cancel-success}
    order-cancel-all-completed: ${EXCHANGE_ORDER_CANCEL_ALL_COMPLETED:exchange-order-cancel-all-completed}
    order-completed: ${EXCHANGE_ORDER_COMPLETED:exchange-order-completed}
    trade: ${EXCHANGE_TRADE:exchange-trade}
    trade-plate: ${EXCHANGE_TRADE_PLATE:exchange-trade-plate}
    stop-order-triggered: ${EXCHANGE_STOP_ORDER_TRIGGERED:exchange-stop-order-triggered}
  contract:
    trade-plate: ${CONTRACT_TRADE_PLATE:contract-trade-plate}
    position-update-with-mark-price: contract-position-update-with-mark-price
    wallet-realtime-with-mark-price: contract-wallet-realtime-with-mark-price
  jobs:
    generate-kline-minute: spot-generate-kline-minute
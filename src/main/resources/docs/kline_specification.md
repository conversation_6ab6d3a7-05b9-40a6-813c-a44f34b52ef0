# K-line (Candlestick) Specification for Cryptocurrency Exchange

## Overview

This document provides a comprehensive specification for implementing K-line (candlestick) data generation, storage, and
retrieval in a cryptocurrency exchange. The specification is designed to follow industry best practices, match real
market requirements, and ensure seamless compatibility with TradingView charting library for modern trading interfaces.

### Key Features

- **Real-time K-line Generation**: Event-driven architecture for instant K-line updates
- **TradingView Integration**: Full compatibility with TradingView charting library
- **WebSocket Streaming**: Real-time data streaming for live chart updates
- **Multi-timeframe Support**: Comprehensive support for all standard trading timeframes
- **High Performance**: Optimized for high-frequency trading environments

## Data Structure

### K-line Entity

Each K-line (candlestick) should contain the following fields:

#### Core Fields

| Field        | Type       | Description                                               | Required |
|--------------|------------|-----------------------------------------------------------|----------|
| symbol       | String     | Trading pair symbol (e.g., "BTC/USDT")                    | Yes      |
| time         | Long       | Timestamp in milliseconds marking the start of the period | Yes      |
| period       | String     | Time period identifier (e.g., "1min", "5min", "1day")     | Yes      |
| openPrice    | BigDecimal | Opening price of the period                               | Yes      |
| highestPrice | BigDecimal | Highest price during the period                           | Yes      |
| lowestPrice  | BigDecimal | Lowest price during the period                            | Yes      |
| closePrice   | BigDecimal | Closing price of the period                               | Yes      |
| volume       | BigDecimal | Trading volume (quantity) during the period               | Yes      |
| turnover     | BigDecimal | Transaction volume (price * quantity) during the period   | Yes      |
| count        | Integer    | Number of trades during the period                        | Yes      |

#### Real-time Specific Fields

| Field          | Type    | Description                                                     | Usage               |
|----------------|---------|-----------------------------------------------------------------|---------------------|
| incomplete     | Boolean | Indicates if the K-line is still being updated (current period) | Real-time streaming |
| lastUpdateTime | Long    | Timestamp of the last update to this K-line                     | Real-time tracking  |

#### JSON Serialization Example

```json
{
  "symbol": "BTC/USDT",
  "time": 1752481320000,
  "period": "1min",
  "openPrice": "122281.69",
  "highestPrice": "122281.69",
  "lowestPrice": "122248.68",
  "closePrice": "122248.68",
  "volume": "2.0",
  "turnover": "244530.37",
  "count": 2,
  "incomplete": true,
  "lastUpdateTime": 1752481380000
}
```

| Field        | Type       | Description                                               |
|--------------|------------|-----------------------------------------------------------|
| symbol       | String     | Trading pair symbol (e.g., "BTC/USDT")                    |
| time         | Long       | Timestamp in milliseconds marking the start of the period |
| period       | String     | Time period identifier (e.g., "1min", "5min", "1day")     |
| openPrice    | BigDecimal | Opening price of the period                               |
| highestPrice | BigDecimal | Highest price during the period                           |
| lowestPrice  | BigDecimal | Lowest price during the period                            |
| closePrice   | BigDecimal | Closing price of the period                               |
| volume       | BigDecimal | Trading volume (quantity) during the period               |
| turnover     | BigDecimal | Transaction volume (price * quantity) during the period   |
| count        | Integer    | Number of trades during the period                        |

### Time Intervals

The system should support the following time intervals:

| Interval   | Period Identifier | Description                |
|------------|-------------------|----------------------------|
| 1 minute   | 1min              | One-minute candlestick     |
| 5 minutes  | 5min              | Five-minute candlestick    |
| 15 minutes | 15min             | Fifteen-minute candlestick |
| 30 minutes | 30min             | Thirty-minute candlestick  |
| 1 hour     | 1hour             | One-hour candlestick       |
| 4 hours    | 4hour             | Four-hour candlestick      |
| 1 day      | 1day              | One-day candlestick        |
| 1 week     | 1week             | One-week candlestick       |
| 1 month    | 1mon              | One-month candlestick      |

## K-line Generation

### Generation Process

1. **Base K-line Generation**:
    - Generate 1-minute K-lines as the base unit
    - Process each trade to update the current 1-minute K-line
    - When a minute completes, finalize the K-line and create a new one

2. **Aggregation for Larger Timeframes**:
    - For 5min, 15min, 30min, 1hour: Aggregate from 1-minute K-lines
    - For 4hour: Aggregate from 1-hour K-lines
    - For 1day: Generate at 00:00:00 each day
    - For 1week: Generate at the beginning of each week
    - For 1month: Generate at the beginning of each month

3. **Handling No-Trade Periods**:
    - If no trades occur during a period, use the last known price for open, high, low, and close
    - Set volume and turnover to zero
    - Ensure continuity in the K-line data

### Calculation Rules

1. **Opening Price**:
    - For the first K-line of a symbol: Use the first trade price
    - For subsequent K-lines: Use the closing price of the previous K-line if no trades occur
    - For aggregated K-lines: Use the opening price of the first sub-period

2. **Highest Price**:
    - The maximum price of all trades during the period
    - For aggregated K-lines: The maximum of all highest prices in the sub-periods

3. **Lowest Price**:
    - The minimum price of all trades during the period
    - For aggregated K-lines: The minimum of all lowest prices in the sub-periods

4. **Closing Price**:
    - The price of the last trade during the period
    - If no trades occur, use the closing price of the previous period

5. **Volume**:
    - The sum of all trade quantities during the period
    - For aggregated K-lines: The sum of volumes from all sub-periods

6. **Turnover**:
    - The sum of (price * quantity) for all trades during the period
    - For aggregated K-lines: The sum of turnovers from all sub-periods

7. **Count**:
    - The number of trades during the period
    - For aggregated K-lines: The sum of counts from all sub-periods

## Storage and Retrieval

### Storage Requirements

1. **Database Storage**:
    - Store all K-line data in a database for historical analysis
    - Index by symbol, period, and time for efficient retrieval
    - Consider time-series database for optimal performance

2. **Cache Layer**:
    - Implement a cache for frequently accessed K-line data
    - Cache recent K-lines for all timeframes to reduce database load
    - Update cache in real-time as new K-lines are generated

### Retrieval API

1. **Historical K-line Retrieval**:
    - Endpoint: `/market/history`
    - Parameters:
        - `symbol`: Trading pair symbol
        - `from`: Start timestamp (milliseconds)
        - `to`: End timestamp (milliseconds)
        - `resolution`: Time resolution (TradingView format)
    - Response: Array of K-line data in TradingView format

2. **Latest K-line Retrieval**:
    - Endpoint: `/market/latest-kline`
    - Parameters:
        - `symbol`: Trading pair symbol
        - `period`: Time period
    - Response: Latest K-line data for the specified symbol and period

## Real-time Updates

### WebSocket API Specification

#### 1. K-line Subscription (Complete K-lines)

**Topic**: `/topic/market/kline/{symbol}/{period}`
**Purpose**: Receive complete K-line data when a period ends
**Update Frequency**:

- 1min K-lines: Every minute
- 5min K-lines: Every 5 minutes
- 1hour K-lines: Every hour
- etc.

**Message Format**:

```json
{
  "symbol": "BTC/USDT",
  "time": 1752481320000,
  "period": "1min",
  "openPrice": "122281.69",
  "highestPrice": "122281.69",
  "lowestPrice": "122248.68",
  "closePrice": "122248.68",
  "volume": "2.0",
  "turnover": "244530.37",
  "count": 2,
  "incomplete": false
}
```

#### 2. Current K-line Updates (Real-time)

**Topic**: `/topic/market/current-kline/{symbol}/{period}`
**Purpose**: Receive current (incomplete) K-line data with each trade
**Update Frequency**: With each trade or at regular intervals (1-2 seconds)

**Message Format**:

```json
{
  "symbol": "BTC/USDT",
  "time": 1752481320000,
  "period": "1min",
  "openPrice": "122281.69",
  "highestPrice": "122281.69",
  "lowestPrice": "122248.68",
  "closePrice": "122248.68",
  "volume": "1.0",
  "turnover": "122248.68",
  "count": 1,
  "incomplete": true,
  "lastUpdateTime": 1752481380000
}
```

#### 3. Multi-timeframe Subscription

**Topic**: `/topic/market/kline/{symbol}/all`
**Purpose**: Receive K-line updates for all timeframes
**Message Format**:

```json
{
  "symbol": "BTC/USDT",
  "klines": {
    "1min": {
      "time": 1752481320000,
      "openPrice": "122281.69",
      "highestPrice": "122281.69",
      "lowestPrice": "122248.68",
      "closePrice": "122248.68",
      "volume": "1.0",
      "turnover": "122248.68",
      "count": 1,
      "incomplete": true
    },
    "5min": {
      "time": 1752481200000,
      "openPrice": "122200.00",
      "highestPrice": "122300.00",
      "lowestPrice": "122100.00",
      "closePrice": "122248.68",
      "volume": "5.0",
      "turnover": "611243.40",
      "count": 5,
      "incomplete": true
    }
  }
}
```

### WebSocket Connection Management

#### Connection Setup

```javascript
// JavaScript WebSocket connection example
const socket = new WebSocket('ws://localhost:8080/ws');

// Subscribe to K-line updates
socket.send(JSON.stringify({
    "action": "subscribe",
    "topic": "/topic/market/current-kline/BTC/USDT/1min"
}));

// Handle incoming messages
socket.onmessage = function (event) {
    const klineData = JSON.parse(event.data);
    updateChart(klineData);
};
```

#### Spring Boot WebSocket Configuration

```java

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        config.enableSimpleBroker("/topic");
        config.setApplicationDestinationPrefixes("/app");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws").setAllowedOrigins("*").withSockJS();
    }
}
```

### Update Rules and Event-Driven Architecture

#### 1. Complete K-lines

- **Trigger**: When a time period ends (e.g., minute boundary for 1min K-lines)
- **Action**: Push complete K-line data to subscribers
- **Fields**: All K-line fields with `incomplete: false`
- **Implementation**: Use scheduled tasks or time-based triggers

#### 2. Current K-line Updates

- **Trigger**: With each trade execution
- **Action**: Update current K-line and push to subscribers
- **Fields**: All K-line fields with `incomplete: true`
- **Implementation**: Event-driven using trade processing events

#### 3. Performance Optimization

- **Batching**: Group multiple updates within a short time window
- **Throttling**: Limit update frequency to prevent overwhelming clients
- **Selective Updates**: Only send updates for subscribed symbols/periods

## TradingView Compatibility

### Data Format for Historical Data

TradingView expects K-line data in a specific format for historical data. The API should convert internal K-line data to
this format:

```json
[
  [
    1624320000000,
    35000.25,
    36100.75,
    34800.50,
    35950.30,
    125.75
  ],
  [
    1624323600000,
    35950.30,
    36200.10,
    35800.20,
    36100.50,
    98.30
  ]
]
```

**Format**: `[timestamp, open, high, low, close, volume]`

### Real-time Data Format

For real-time updates, TradingView expects individual K-line updates:

```json
{
  "time": 1624320000000,
  "open": 35000.25,
  "high": 36100.75,
  "low": 34800.50,
  "close": 35950.30,
  "volume": 125.75
}
```

### Data Conversion Functions

Implement these conversion functions for TradingView compatibility:

```java
// Convert internal KLine to TradingView historical format
public Object[] convertToTradingViewArray(KLine kline) {
    return new Object[]{
            kline.getTime(),
            kline.getOpenPrice().doubleValue(),
            kline.getHighestPrice().doubleValue(),
            kline.getLowestPrice().doubleValue(),
            kline.getClosePrice().doubleValue(),
            kline.getVolume().doubleValue()
    };
}

// Convert internal KLine to TradingView real-time format
public Map<String, Object> convertToTradingViewRealtime(KLine kline) {
    Map<String, Object> result = new HashMap<>();
    result.put("time", kline.getTime());
    result.put("open", kline.getOpenPrice().doubleValue());
    result.put("high", kline.getHighestPrice().doubleValue());
    result.put("low", kline.getLowestPrice().doubleValue());
    result.put("close", kline.getClosePrice().doubleValue());
    result.put("volume", kline.getVolume().doubleValue());
    return result;
}
```

### Resolution Mapping

Map internal period identifiers to TradingView resolutions:

| Internal Period | TradingView Resolution |
|-----------------|------------------------|
| 1min            | 1                      |
| 5min            | 5                      |
| 15min           | 15                     |
| 30min           | 30                     |
| 1hour           | 60 or 1H               |
| 4hour           | 240 or 4H              |
| 1day            | 1D                     |
| 1week           | 1W                     |
| 1month          | 1M                     |

### UDF API (Universal Data Feed)

For full TradingView integration, implement the UDF API:

1. **Configuration Endpoint**:
    - `/udf/config`
    - Returns supported features and timeframes

2. **Symbol Information**:
    - `/udf/symbols?symbol={symbol}`
    - Returns detailed information about the trading pair

3. **History Endpoint**:
    - `/udf/history?symbol={symbol}&resolution={resolution}&from={from}&to={to}`
    - Returns K-line data in TradingView format

4. **Time Endpoint**:
    - `/udf/time`
    - Returns server time for synchronization

## UI Integration Guidelines

### TradingView Chart Integration

#### 1. Basic Chart Setup

```javascript
// Initialize TradingView chart
const widget = new TradingView.widget({
    symbol: 'BTC/USDT',
    interval: '1',
    container_id: 'tradingview_chart',
    datafeed: new Datafeeds.UDFCompatibleDatafeed('/udf'),
    library_path: '/charting_library/',
    locale: 'en',
    disabled_features: ['use_localstorage_for_settings'],
    enabled_features: ['study_templates'],
    charts_storage_url: '/charts_storage',
    charts_storage_api_version: '1.1',
    client_id: 'tradingview.com',
    user_id: 'public_user_id',
    fullscreen: false,
    autosize: true,
    studies_overrides: {}
});
```

#### 2. Real-time Data Integration

```javascript
// WebSocket connection for real-time updates
class RealtimeDataProvider {
    constructor(symbol, interval) {
        this.symbol = symbol;
        this.interval = interval;
        this.subscribers = [];
        this.connect();
    }

    connect() {
        this.socket = new WebSocket('ws://localhost:8080/ws');

        this.socket.onopen = () => {
            // Subscribe to current K-line updates
            this.socket.send(JSON.stringify({
                action: 'subscribe',
                topic: `/topic/market/current-kline/${this.symbol}/${this.interval}`
            }));
        };

        this.socket.onmessage = (event) => {
            const klineData = JSON.parse(event.data);
            this.updateChart(klineData);
        };
    }

    updateChart(klineData) {
        // Convert to TradingView format
        const bar = {
            time: klineData.time,
            open: parseFloat(klineData.openPrice),
            high: parseFloat(klineData.highestPrice),
            low: parseFloat(klineData.lowestPrice),
            close: parseFloat(klineData.closePrice),
            volume: parseFloat(klineData.volume)
        };

        // Update all subscribers
        this.subscribers.forEach(callback => callback(bar));
    }

    subscribeBars(callback) {
        this.subscribers.push(callback);
    }
}
```

#### 3. Custom Datafeed Implementation

```javascript
// Custom datafeed for TradingView
class CustomDatafeed {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.realtimeProvider = null;
    }

    onReady(callback) {
        setTimeout(() => callback({
            supported_resolutions: ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'],
            supports_marks: false,
            supports_timescale_marks: false,
            supports_time: true
        }), 0);
    }

    searchSymbols(userInput, exchange, symbolType, onResultReadyCallback) {
        // Implement symbol search
        fetch(`${this.baseUrl}/search?query=${userInput}`)
            .then(response => response.json())
            .then(data => onResultReadyCallback(data));
    }

    resolveSymbol(symbolName, onSymbolResolvedCallback, onResolveErrorCallback) {
        const symbolInfo = {
            name: symbolName,
            ticker: symbolName,
            description: symbolName,
            type: 'crypto',
            session: '24x7',
            timezone: 'Etc/UTC',
            exchange: 'BITCELLO',
            minmov: 1,
            pricescale: 100000000,
            has_intraday: true,
            has_weekly_and_monthly: true,
            supported_resolutions: ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'],
            volume_precision: 8,
            data_status: 'streaming'
        };

        setTimeout(() => onSymbolResolvedCallback(symbolInfo), 0);
    }

    getBars(symbolInfo, resolution, from, to, onHistoryCallback, onErrorCallback, firstDataRequest) {
        const url = `${this.baseUrl}/history?symbol=${symbolInfo.name}&resolution=${resolution}&from=${from}&to=${to}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                const bars = data.map(item => ({
                    time: item[0],
                    open: item[1],
                    high: item[2],
                    low: item[3],
                    close: item[4],
                    volume: item[5]
                }));

                onHistoryCallback(bars, {noData: bars.length === 0});
            })
            .catch(error => onErrorCallback(error));
    }

    subscribeBars(symbolInfo, resolution, onRealtimeCallback, subscriberUID, onResetCacheNeededCallback) {
        this.realtimeProvider = new RealtimeDataProvider(symbolInfo.name, resolution);
        this.realtimeProvider.subscribeBars(onRealtimeCallback);
    }

    unsubscribeBars(subscriberUID) {
        if (this.realtimeProvider) {
            this.realtimeProvider.socket.close();
            this.realtimeProvider = null;
        }
    }
}
```

### React Integration Example

#### 1. React Component

```jsx
import React, {useEffect, useRef} from 'react';

const TradingViewChart = ({symbol, interval}) => {
    const chartContainerRef = useRef();
    const widgetRef = useRef();

    useEffect(() => {
        if (chartContainerRef.current) {
            widgetRef.current = new TradingView.widget({
                symbol: symbol,
                interval: interval,
                container_id: chartContainerRef.current.id,
                datafeed: new CustomDatafeed('/api/udf'),
                library_path: '/charting_library/',
                locale: 'en',
                disabled_features: ['use_localstorage_for_settings'],
                enabled_features: ['study_templates'],
                fullscreen: false,
                autosize: true
            });
        }

        return () => {
            if (widgetRef.current) {
                widgetRef.current.remove();
            }
        };
    }, [symbol, interval]);

    return <div ref={chartContainerRef} id="tradingview_chart" style={{height: '500px'}}/>;
};

export default TradingViewChart;
```

#### 2. Vue.js Integration

```vue
<template>
  <div ref="chartContainer" id="tradingview_chart" style="height: 500px;"></div>
</template>

<script>
export default {
  name: 'TradingViewChart',
  props: {
    symbol: {
      type: String,
      required: true
    },
    interval: {
      type: String,
      default: '1'
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.widget) {
      this.widget.remove();
    }
  },
  methods: {
    initChart() {
      this.widget = new TradingView.widget({
        symbol: this.symbol,
        interval: this.interval,
        container_id: 'tradingview_chart',
        datafeed: new CustomDatafeed('/api/udf'),
        library_path: '/charting_library/',
        locale: 'en',
        disabled_features: ['use_localstorage_for_settings'],
        enabled_features: ['study_templates'],
        fullscreen: false,
        autosize: true
      });
    }
  }
};
</script>
```

### Best Practices for UI Integration

#### 1. Performance Optimization

- **Throttle Updates**: Limit real-time updates to prevent UI freezing
- **Batch Processing**: Group multiple updates within short time windows
- **Memory Management**: Properly dispose of WebSocket connections and chart instances
- **Lazy Loading**: Load chart library only when needed

#### 2. Error Handling

```javascript
// Robust error handling for WebSocket connections
class RobustWebSocket {
    constructor(url, options = {}) {
        this.url = url;
        this.options = options;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectInterval = options.reconnectInterval || 3000;
        this.connect();
    }

    connect() {
        try {
            this.socket = new WebSocket(this.url);
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket connection failed:', error);
            this.handleReconnect();
        }
    }

    setupEventHandlers() {
        this.socket.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            if (this.options.onOpen) this.options.onOpen();
        };

        this.socket.onmessage = (event) => {
            if (this.options.onMessage) this.options.onMessage(event);
        };

        this.socket.onclose = () => {
            console.log('WebSocket disconnected');
            this.handleReconnect();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            if (this.options.onError) this.options.onError(error);
        };
    }

    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => this.connect(), this.reconnectInterval);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }
}
```

#### 3. Data Validation

```javascript
// Validate K-line data before updating chart
function validateKlineData(klineData) {
    const required = ['time', 'openPrice', 'highestPrice', 'lowestPrice', 'closePrice', 'volume'];

    for (const field of required) {
        if (klineData[field] === undefined || klineData[field] === null) {
            throw new Error(`Missing required field: ${field}`);
        }
    }

    // Validate price relationships
    const open = parseFloat(klineData.openPrice);
    const high = parseFloat(klineData.highestPrice);
    const low = parseFloat(klineData.lowestPrice);
    const close = parseFloat(klineData.closePrice);

    if (high < Math.max(open, close) || low > Math.min(open, close)) {
        throw new Error('Invalid price relationships in K-line data');
    }

    return true;
}
```

## Performance Considerations

1. **Optimization Strategies**:
    - Generate and store 1-minute K-lines in real-time
    - Generate larger timeframes on-demand or at scheduled intervals
    - Use efficient aggregation algorithms for larger timeframes

2. **Scaling**:
    - Design the system to handle high-frequency trading
    - Consider sharding by symbol for horizontal scaling
    - Implement backpressure mechanisms for trade processing

3. **Resilience**:
    - Implement recovery mechanisms for service restarts
    - Store the last processed trade ID to resume processing
    - Periodically validate K-line data integrity

## Implementation Guidelines

1. **K-line Generation Service**:
    - Implement as a scheduled service for regular intervals
    - Process trades in real-time to update current K-lines
    - Finalize K-lines at period boundaries

2. **Trade Processing**:
    - Process each trade to update the current K-line
    - Update market overview data with each trade
    - Push updates to subscribers

3. **Data Consistency**:
    - Ensure atomicity in K-line updates
    - Use locks or transactions to prevent race conditions
    - Validate K-line data periodically

## Monitoring and Maintenance

1. **Health Checks**:
    - Monitor K-line generation for delays or gaps
    - Check for missing K-lines in historical data
    - Verify data consistency across timeframes

2. **Performance Metrics**:
    - Track K-line generation time
    - Monitor trade processing latency
    - Measure API response times

3. **Data Correction**:
    - Implement tools for manual K-line correction
    - Provide mechanisms to regenerate K-lines for specific periods
    - Log all data corrections for audit purposes

## Implementation Patterns and Examples

### Spring Boot Service Implementation

#### 1. K-line Service with Real-time Updates

```java

@Service
@Slf4j
public class KlineService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private MarketService marketService;

    private final Map<String, KlineProcessor> processors = new ConcurrentHashMap<>();

    @EventListener
    public void handleTradeEvent(TradeExecutedEvent event) {
        ExchangeTrade trade = event.getTrade();
        String symbol = trade.getSymbol();

        // Get or create processor for symbol
        KlineProcessor processor = processors.computeIfAbsent(symbol,
                k -> new DefaultKlineProcessor(symbol));

        // Update current K-line and push real-time updates
        processor.pushCurrentKLineForAllTimeframes(trade);
    }

    @Scheduled(fixedRate = 60000) // Every minute
    public void generateCompleteKlines() {
        long currentTime = System.currentTimeMillis();
        int minute = Calendar.getInstance().get(Calendar.MINUTE);
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);

        processors.values().parallelStream().forEach(processor -> {
            processor.generateKLine(currentTime, minute, hour);
        });
    }

    public void pushKlineUpdate(String symbol, String period, KLine kline) {
        String topic = String.format("/topic/market/kline/%s/%s", symbol, period);
        messagingTemplate.convertAndSend(topic, kline);
    }

    public void pushCurrentKlineUpdate(String symbol, String period, KLine kline) {
        String topic = String.format("/topic/market/current-kline/%s/%s", symbol, period);
        kline.setIncomplete(true);
        messagingTemplate.convertAndSend(topic, kline);
    }
}
```

#### 2. REST API Controller

```java

@RestController
@RequestMapping("/api/market")
@CrossOrigin(origins = "*")
public class MarketController {

    @Autowired
    private KlineService klineService;

    @Autowired
    private MarketService marketService;

    @GetMapping("/history")
    public ResponseEntity<List<Object[]>> getKlineHistory(
            @RequestParam String symbol,
            @RequestParam String resolution,
            @RequestParam long from,
            @RequestParam long to) {

        try {
            String period = convertResolutionToPeriod(resolution);
            List<KLine> klines = marketService.getKlineHistory(symbol, period, from, to);

            List<Object[]> tradingViewData = klines.stream()
                    .map(this::convertToTradingViewArray)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(tradingViewData);
        } catch (Exception e) {
            log.error("Error fetching K-line history", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/latest-kline")
    public ResponseEntity<KLine> getLatestKline(
            @RequestParam String symbol,
            @RequestParam String period) {

        try {
            KLine kline = marketService.getLatestKline(symbol, period);
            return ResponseEntity.ok(kline);
        } catch (Exception e) {
            log.error("Error fetching latest K-line", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private Object[] convertToTradingViewArray(KLine kline) {
        return new Object[]{
                kline.getTime(),
                kline.getOpenPrice().doubleValue(),
                kline.getHighestPrice().doubleValue(),
                kline.getLowestPrice().doubleValue(),
                kline.getClosePrice().doubleValue(),
                kline.getVolume().doubleValue()
        };
    }

    private String convertResolutionToPeriod(String resolution) {
        switch (resolution) {
            case "1":
                return "1min";
            case "5":
                return "5min";
            case "15":
                return "15min";
            case "30":
                return "30min";
            case "60":
            case "1H":
                return "1hour";
            case "240":
            case "4H":
                return "4hour";
            case "1D":
                return "1day";
            case "1W":
                return "1week";
            case "1M":
                return "1month";
            default:
                return "1min";
        }
    }
}
```

### Database Schema Recommendations

#### 1. K-line Table Structure

```sql
CREATE TABLE klines
(
    id            BIGINT PRIMARY KEY AUTO_INCREMENT,
    symbol        VARCHAR(20)    NOT NULL,
    PERIOD VARCHAR (10) NOT NULL,
    time          BIGINT         NOT NULL,
    open_price    DECIMAL(20, 8) NOT NULL,
    highest_price DECIMAL(20, 8) NOT NULL,
    lowest_price  DECIMAL(20, 8) NOT NULL,
    close_price   DECIMAL(20, 8) NOT NULL,
    volume        DECIMAL(20, 8) NOT NULL DEFAULT 0,
    turnover      DECIMAL(30, 8) NOT NULL DEFAULT 0,
    count         INT            NOT NULL DEFAULT 0,
    created_at    TIMESTAMP               DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_symbol_period_time (symbol, PERIOD, TIME),
    INDEX         idx_symbol_period (symbol, PERIOD),
    INDEX         idx_time (TIME),
    INDEX         idx_created_at (created_at)
);

-- Partition by time for better performance
ALTER TABLE klines PARTITION BY RANGE (TIME) (
    PARTITION p202401 VALUES LESS THAN (1706745600000),
    PARTITION p202402 VALUES LESS THAN (1709251200000),
    -- Add more partitions as needed
    PARTITION pmax VALUES LESS THAN MAXVALUE
    );
```

#### 2. Caching Strategy

```java

@Component
public class KlineCacheManager {

    private final RedisTemplate<String, Object> redisTemplate;
    private static final String KLINE_CACHE_PREFIX = "kline:";
    private static final String CURRENT_KLINE_PREFIX = "current_kline:";

    public KlineCacheManager(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void cacheKline(String symbol, String period, KLine kline) {
        String key = KLINE_CACHE_PREFIX + symbol + ":" + period + ":" + kline.getTime();
        redisTemplate.opsForValue().set(key, kline, Duration.ofHours(24));
    }

    public void cacheCurrentKline(String symbol, String period, KLine kline) {
        String key = CURRENT_KLINE_PREFIX + symbol + ":" + period;
        redisTemplate.opsForValue().set(key, kline, Duration.ofMinutes(2));
    }

    public KLine getCurrentKline(String symbol, String period) {
        String key = CURRENT_KLINE_PREFIX + symbol + ":" + period;
        return (KLine) redisTemplate.opsForValue().get(key);
    }

    public List<KLine> getKlineHistory(String symbol, String period, long from, long to) {
        // Implementation for retrieving cached K-line history
        // Fall back to database if not in cache
        return null;
    }
}
```

### Testing Strategies

#### 1. Unit Tests for K-line Processing

```java

@ExtendWith(MockitoExtension.class)
class DefaultKlineProcessorTest {

    @Mock
    private MarketService marketService;

    @Mock
    private MarketHandlerService marketHandlerService;

    @InjectMocks
    private DefaultKlineProcessor processor;

    @Test
    void testProcessTrade_UpdatesKlineCorrectly() {
        // Given
        KLine kline = new KLine("1min");
        kline.setTime(System.currentTimeMillis());
        kline.setOpenPrice(BigDecimal.valueOf(50000));

        ExchangeTrade trade = new ExchangeTrade();
        trade.setPrice(BigDecimal.valueOf(51000));
        trade.setAmount(BigDecimal.valueOf(1));
        trade.setTime(System.currentTimeMillis());

        // When
        processor.processTrade(kline, trade);

        // Then
        assertEquals(BigDecimal.valueOf(51000), kline.getClosePrice());
        assertEquals(BigDecimal.valueOf(51000), kline.getHighestPrice());
        assertEquals(1, kline.getCount());
    }

    @Test
    void testGenerateCurrentKLineForPeriod_CreatesCorrectKline() {
        // Test implementation
    }
}
```

#### 2. Integration Tests

```java

@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
class KlineIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private KlineService klineService;

    @Test
    void testKlineHistoryEndpoint() {
        // Given
        String symbol = "BTC/USDT";
        String resolution = "1";
        long from = System.currentTimeMillis() - 3600000; // 1 hour ago
        long to = System.currentTimeMillis();

        // When
        ResponseEntity<Object[]> response = restTemplate.getForEntity(
                "/api/market/history?symbol={symbol}&resolution={resolution}&from={from}&to={to}",
                Object[].class, symbol, resolution, from, to);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}
```

### Monitoring and Alerting

#### 1. Health Check Implementation

```java

@Component
public class KlineHealthIndicator implements HealthIndicator {

    @Autowired
    private KlineService klineService;

    @Override
    public Health health() {
        try {
            // Check if K-line generation is working
            long lastKlineTime = klineService.getLastKlineGenerationTime();
            long currentTime = System.currentTimeMillis();

            if (currentTime - lastKlineTime > 120000) { // 2 minutes
                return Health.down()
                        .withDetail("lastKlineGeneration", lastKlineTime)
                        .withDetail("message", "K-line generation is delayed")
                        .build();
            }

            return Health.up()
                    .withDetail("lastKlineGeneration", lastKlineTime)
                    .withDetail("status", "K-line generation is healthy")
                    .build();

        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}
```

#### 2. Metrics Collection

```java

@Component
public class KlineMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter klineGenerationCounter;
    private final Timer klineProcessingTimer;

    public KlineMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.klineGenerationCounter = Counter.builder("kline.generation.count")
                .description("Number of K-lines generated")
                .register(meterRegistry);
        this.klineProcessingTimer = Timer.builder("kline.processing.time")
                .description("Time taken to process K-line")
                .register(meterRegistry);
    }

    public void recordKlineGeneration(String symbol, String period) {
        klineGenerationCounter.increment(
                Tags.of("symbol", symbol, "period", period)
        );
    }

    public Timer.Sample startProcessingTimer() {
        return Timer.start(meterRegistry);
    }
}
```

## Conclusion

This specification provides a comprehensive guide for implementing K-line generation in a cryptocurrency exchange. By
following these guidelines, developers can create a robust, efficient, and TradingView-compatible K-line system that
meets the needs of traders and analysts.

This enhanced specification provides a comprehensive guide for implementing a modern, high-performance K-line system in
a cryptocurrency exchange with full TradingView integration. The specification now includes:

### Key Enhancements

- **Real-time Architecture**: Event-driven K-line updates with WebSocket streaming
- **TradingView Integration**: Complete compatibility with TradingView charting library
- **UI Integration Guidelines**: Practical examples for React, Vue.js, and vanilla JavaScript
- **Performance Optimization**: Caching strategies, database partitioning, and efficient data structures
- **Error Handling**: Robust error handling and reconnection strategies
- **Testing Strategies**: Comprehensive unit and integration testing approaches
- **Monitoring**: Health checks, metrics collection, and alerting mechanisms

### Implementation Benefits

- **Developer-Friendly**: Clear examples and patterns for easy implementation
- **Production-Ready**: Performance considerations and monitoring built-in
- **Scalable**: Designed to handle high-frequency trading environments
- **Maintainable**: Well-structured code with proper separation of concerns
- **Modern**: Uses current best practices and technologies

By following this specification, developers can create a robust, efficient, and user-friendly K-line system that
provides real-time trading data visualization through TradingView charts, ensuring a professional trading experience for
end users.

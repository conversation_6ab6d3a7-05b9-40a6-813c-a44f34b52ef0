<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="ENCODING" value="UTF-8" />
    <property name="LOG_LEVEL" value="DEBUG" />
    <include
            resource="org/springframework/boot/logging/logback/base.xml" />
    <springProperty scope="context" name="appName"
                    source="spring.application.name" />
    <!-- Reuse the log from Spring -->
    <include
            resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include
            resource="org/springframework/boot/logging/logback/console-appender.xml" />
    <!-- Define own appender to be used in the logger to append the data into -->
    <appender name="JSON_APPENDER"
              class="ch.qos.logback.core.ConsoleAppender">
        <Target>${user-system}.out</Target>
        <encoder
                class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <mdc />
                <pattern>
                    <pattern>
                        {
                        "timestamp": "%date{yyyy-MM-dd'T'HH:mm:ss.SSS,UTC}",
                        "log.level": "%level",
                        "thread": "%thread",
                        "logger": "%logger",
                        "message": "%message",
                        "host": "${HOSTNAME}"
                        }
                    </pattern>
                </pattern>
                <stackTrace>
                    <throwableConverter
                            class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                        <rootCauseFirst>true</rootCauseFirst>
                    </throwableConverter>
                </stackTrace>
            </providers>
        </encoder>
    </appender>

    <appender name="OTEL" class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">
        <captureExperimentalAttributes>true</captureExperimentalAttributes>
        <captureMdcAttributes>*</captureMdcAttributes>
        <appender-ref ref="JSON_APPENDER" />
    </appender>
    <!-- This appender is to define the log to console that human friendly on
        dev machine -->
    <appender name="CONSOLE"
              class="ch.qos.logback.core.ConsoleAppender">
        <Target>${user-system}.out</Target>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>${ENCODING}</charset>
        </encoder>
    </appender>
    <!-- Define the logger and the log level to use (TRACE, DEBUG, INFO, WARN,
        ERROR, FATAL) -->
    <logger
            name="org.springframework.web.filter.CommonsRequestLoggingFilter"
            level="DEBUG" />
    <root level="INFO">
        <appender-ref ref="OTEL" />
    </root>
</configuration>
# K-line (Candlestick) Specification for Cryptocurrency Exchange

## Overview
This document provides a comprehensive specification for implementing K-line (candlestick) data generation, storage, and retrieval in a cryptocurrency exchange. The specification is designed to follow industry best practices, match real market requirements, and ensure compatibility with TradingView charting library.

## Data Structure

### K-line Entity
Each K-line (candlestick) should contain the following fields:

| Field | Type | Description |
|-------|------|-------------|
| symbol | String | Trading pair symbol (e.g., "BTC/USDT") |
| time | Long | Timestamp in milliseconds marking the start of the period |
| period | String | Time period identifier (e.g., "1min", "5min", "1day") |
| openPrice | BigDecimal | Opening price of the period |
| highestPrice | BigDecimal | Highest price during the period |
| lowestPrice | BigDecimal | Lowest price during the period |
| closePrice | BigDecimal | Closing price of the period |
| volume | BigDecimal | Trading volume (quantity) during the period |
| turnover | BigDecimal | Transaction volume (price * quantity) during the period |
| count | Integer | Number of trades during the period |

### Time Intervals
The system should support the following time intervals:

| Interval | Period Identifier | Description |
|----------|-------------------|-------------|
| 1 minute | 1min | One-minute candlestick |
| 5 minutes | 5min | Five-minute candlestick |
| 15 minutes | 15min | Fifteen-minute candlestick |
| 30 minutes | 30min | Thirty-minute candlestick |
| 1 hour | 1hour | One-hour candlestick |
| 4 hours | 4hour | Four-hour candlestick |
| 1 day | 1day | One-day candlestick |
| 1 week | 1week | One-week candlestick |
| 1 month | 1mon | One-month candlestick |

## K-line Generation

### Generation Process
1. **Base K-line Generation**:
   - Generate 1-minute K-lines as the base unit
   - Process each trade to update the current 1-minute K-line
   - When a minute completes, finalize the K-line and create a new one

2. **Aggregation for Larger Timeframes**:
   - For 5min, 15min, 30min, 1hour: Aggregate from 1-minute K-lines
   - For 4hour: Aggregate from 1-hour K-lines
   - For 1day: Generate at 00:00:00 each day
   - For 1week: Generate at the beginning of each week
   - For 1month: Generate at the beginning of each month

3. **Handling No-Trade Periods**:
   - If no trades occur during a period, use the last known price for open, high, low, and close
   - Set volume and turnover to zero
   - Ensure continuity in the K-line data

### Calculation Rules

1. **Opening Price**:
   - For the first K-line of a symbol: Use the first trade price
   - For subsequent K-lines: Use the closing price of the previous K-line if no trades occur
   - For aggregated K-lines: Use the opening price of the first sub-period

2. **Highest Price**:
   - The maximum price of all trades during the period
   - For aggregated K-lines: The maximum of all highest prices in the sub-periods

3. **Lowest Price**:
   - The minimum price of all trades during the period
   - For aggregated K-lines: The minimum of all lowest prices in the sub-periods

4. **Closing Price**:
   - The price of the last trade during the period
   - If no trades occur, use the closing price of the previous period

5. **Volume**:
   - The sum of all trade quantities during the period
   - For aggregated K-lines: The sum of volumes from all sub-periods

6. **Turnover**:
   - The sum of (price * quantity) for all trades during the period
   - For aggregated K-lines: The sum of turnovers from all sub-periods

7. **Count**:
   - The number of trades during the period
   - For aggregated K-lines: The sum of counts from all sub-periods

## Storage and Retrieval

### Storage Requirements
1. **Database Storage**:
   - Store all K-line data in a database for historical analysis
   - Index by symbol, period, and time for efficient retrieval
   - Consider time-series database for optimal performance

2. **Cache Layer**:
   - Implement a cache for frequently accessed K-line data
   - Cache recent K-lines for all timeframes to reduce database load
   - Update cache in real-time as new K-lines are generated

### Retrieval API
1. **Historical K-line Retrieval**:
   - Endpoint: `/market/history`
   - Parameters:
     - `symbol`: Trading pair symbol
     - `from`: Start timestamp (milliseconds)
     - `to`: End timestamp (milliseconds)
     - `resolution`: Time resolution (TradingView format)
   - Response: Array of K-line data in TradingView format

2. **Latest K-line Retrieval**:
   - Endpoint: `/market/latest-kline`
   - Parameters:
     - `symbol`: Trading pair symbol
     - `period`: Time period
   - Response: Latest K-line data for the specified symbol and period

## Real-time Updates

### WebSocket API
1. **K-line Subscription**:
   - Topic: `/topic/market/kline/{symbol}/{period}`
   - Payload: Complete K-line data when updated
   - Update frequency: Every minute for 1min K-lines, proportionally less frequent for larger timeframes

2. **Current K-line Updates**:
   - Topic: `/topic/market/current-kline/{symbol}/{period}`
   - Payload: Current (incomplete) K-line data
   - Update frequency: With each trade or at regular intervals (e.g., every second)

### Update Rules
1. **Complete K-lines**:
   - Push complete K-line data when a period ends
   - Include all fields in the K-line entity

2. **Current K-line**:
   - Push the current (incomplete) K-line data with each trade
   - Mark as incomplete with an additional flag

## TradingView Compatibility

### Data Format
TradingView expects K-line data in a specific format. The API should convert internal K-line data to this format:

```
[
  [1624320000000, 35000.25, 36100.75, 34800.50, 35950.30, 125.75],
  [1624323600000, 35950.30, 36200.10, 35800.20, 36100.50, 98.30],
  ...
]
```

Note: Each array contains [timestamp, open, high, low, close, volume] values.

### Resolution Mapping
Map internal period identifiers to TradingView resolutions:

| Internal Period | TradingView Resolution |
|-----------------|------------------------|
| 1min | 1 |
| 5min | 5 |
| 15min | 15 |
| 30min | 30 |
| 1hour | 60 or 1H |
| 4hour | 240 or 4H |
| 1day | 1D |
| 1week | 1W |
| 1month | 1M |

### UDF API (Universal Data Feed)
For full TradingView integration, implement the UDF API:

1. **Configuration Endpoint**:
   - `/udf/config`
   - Returns supported features and timeframes

2. **Symbol Information**:
   - `/udf/symbols?symbol={symbol}`
   - Returns detailed information about the trading pair

3. **History Endpoint**:
   - `/udf/history?symbol={symbol}&resolution={resolution}&from={from}&to={to}`
   - Returns K-line data in TradingView format

4. **Time Endpoint**:
   - `/udf/time`
   - Returns server time for synchronization

## Performance Considerations

1. **Optimization Strategies**:
   - Generate and store 1-minute K-lines in real-time
   - Generate larger timeframes on-demand or at scheduled intervals
   - Use efficient aggregation algorithms for larger timeframes

2. **Scaling**:
   - Design the system to handle high-frequency trading
   - Consider sharding by symbol for horizontal scaling
   - Implement backpressure mechanisms for trade processing

3. **Resilience**:
   - Implement recovery mechanisms for service restarts
   - Store the last processed trade ID to resume processing
   - Periodically validate K-line data integrity

## Implementation Guidelines

1. **K-line Generation Service**:
   - Implement as a scheduled service for regular intervals
   - Process trades in real-time to update current K-lines
   - Finalize K-lines at period boundaries

2. **Trade Processing**:
   - Process each trade to update the current K-line
   - Update market overview data with each trade
   - Push updates to subscribers

3. **Data Consistency**:
   - Ensure atomicity in K-line updates
   - Use locks or transactions to prevent race conditions
   - Validate K-line data periodically

## Monitoring and Maintenance

1. **Health Checks**:
   - Monitor K-line generation for delays or gaps
   - Check for missing K-lines in historical data
   - Verify data consistency across timeframes

2. **Performance Metrics**:
   - Track K-line generation time
   - Monitor trade processing latency
   - Measure API response times

3. **Data Correction**:
   - Implement tools for manual K-line correction
   - Provide mechanisms to regenerate K-lines for specific periods
   - Log all data corrections for audit purposes

## Conclusion
This specification provides a comprehensive guide for implementing K-line generation in a cryptocurrency exchange. By following these guidelines, developers can create a robust, efficient, and TradingView-compatible K-line system that meets the needs of traders and analysts.

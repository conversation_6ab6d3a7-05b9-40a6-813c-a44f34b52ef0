# Current K-line WebSocket Implementation

## Overview

This document describes the implementation of the `/topic/market/current-kline/{symbol}/{period}` WebSocket topic and how the client-ui consumes it for real-time K-line updates.

## Backend Implementation

### WebSocket Topic Structure

The backend provides the following WebSocket topic for current K-line updates:

```
/topic/market/current-kline/{symbol}/{period}
```

Where:
- `{symbol}` is the trading pair symbol (e.g., "BTCUSDT")
- `{period}` is the time period (e.g., "1min", "5min", "15min", etc.)

### Data Format

The data sent to this topic has the following format:

```json
{
  "symbol": "BTCUSDT",
  "time": 1640995200000,
  "period": "1min",
  "openPrice": 50000.00,
  "highestPrice": 50100.00,
  "lowestPrice": 49900.00,
  "closePrice": 50050.00,
  "volume": 1.5,
  "turnover": 75075.00,
  "count": 10,
  "incomplete": true
}
```

The `incomplete` flag is set to `true` to indicate that this is a current (incomplete) K-line.

### Implementation Details

The backend implementation is in the `WebsocketMarketHandler` class, which has a `handleCurrentKLine` method that sends data to the current-kline topic:

```java
public void handleCurrentKLine(String symbol, KLine kLine) {
    String period = kLine.getPeriod();
    String currentTopic = "/topic/market/current-kline/" + symbol + "/" + period;

    // Create enhanced K-line data with symbol included and incomplete flag
    Map<String, Object> klineData = createKLineData(symbol, kLine, true);

    log.debug("Pushing current K-line to topic: {} for symbol: {}, period: {}", currentTopic, symbol, period);
    messagingTemplate.convertAndSend(currentTopic, klineData);
}
```

## Client-UI Implementation

### Subscription

The client-ui subscribes to the current-kline topic in the `custom-data-feed.ts` file:

```typescript
// Subscribe to current-kline updates for real-time incomplete candle data
const period = RESOLUTION_TO_PERIOD[resolution];
if (period) {
  // Use the provided currentKlineTopic or fall back to default
  const currentKlineTopic = configs.currentKlineTopic || '/topic/market/current-kline';
  stompClient.subscribe(
    `${currentKlineTopic}/${symbolInfo.name}/${period}`,
    function (msg) {
      const resp = JSON.parse(msg.body);

      // Only process if this is marked as incomplete (current candle)
      if (resp.incomplete) {
        const currentKlineBar = {
          time: resp.time,
          open: resp.openPrice,
          high: resp.highestPrice,
          low: resp.lowestPrice,
          close: resp.closePrice,
          volume: resp.volume,
        };

        // Update the current bar for real-time updates
        currentBar = currentKlineBar;
        lastBar = currentKlineBar;
        lastBarsCache.set(symbolInfo.full_name, currentKlineBar);
        onRealtimeCallback(currentKlineBar);
      }
    }
  );
}
```

### Configuration

The client-ui configures the WebSocket topics in the `TVChartContainer` component:

```typescript
let customDataFeedConfigs: CustomDataFeedConfigs = {
  baseSocketPath: 'market/market-ws',
  historyPath: 'market/history',
  tradeTopic: '/topic/market/trade',
  klineTopic: '/topic/market/kline',
  currentKlineTopic: '/topic/market/current-kline',
  handleTradeDataReceived,
};
```

### Resolution Mapping

The client-ui maps TradingView resolutions to backend periods using the `RESOLUTION_TO_PERIOD` mapping:

```typescript
export const RESOLUTION_TO_PERIOD: Record<string, string> = {
  '1': '1min',
  '5': '5min',
  '15': '15min',
  '30': '30min',
  '60': '1hour',
  '240': '4hour',
  '1D': '1day',
  '1W': '1week',
};
```

## Data Flow

1. The backend generates current K-line data for each trade
2. The data is sent to the `/topic/market/current-kline/{symbol}/{period}` topic
3. The client-ui subscribes to this topic for the current resolution
4. When data is received, the client-ui updates the chart with the new data

## Benefits

- Real-time updates for all timeframes
- Smooth chart animations
- Accurate volume and turnover data
- Consistent behavior across all chart resolutions
# Event-Driven K-line Real-time Implementation

## Overview

This document describes the complete refactoring of the K-line real-time system to implement proper Event-Driven Architecture. The system now correctly provides the `/topic/market/current-kline/{symbol}/{period}` WebSocket topic for client-ui to handle real-time K-line updates across all supported timeframes.

## Problem Analysis

### Previous Issues
1. **Limited Timeframe Support**: The old implementation only pushed current K-line updates for 1-minute resolution
2. **Incorrect Event-Driven Architecture**: Current K-lines were not properly generated for all timeframes when trades occurred
3. **Client-UI Missing Subscriptions**: The client-ui was not subscribing to the current-kline topic for all timeframes
4. **Inconsistent Real-time Updates**: Different timeframes had different update behaviors

### Root Cause
The backend was only maintaining a single 1-minute current K-line, but the specification requires current K-line updates for all supported timeframes (1min, 5min, 15min, 30min, 1hour, 4hour, 1day, 1week) whenever a trade occurs.

## Solution Implementation

### Backend Changes

#### 1. Event-Driven Architecture Implementation
**File**: `DefaultCoinProcessor.java`

Added new method `pushCurrentKLineForAllTimeframes()` that:
- Triggers on every trade event
- Generates current K-line data for all 8 supported timeframes
- Pushes updates to WebSocket subscribers via `/topic/market/current-kline/{symbol}/{period}`

```java
private void pushCurrentKLineForAllTimeframes(ExchangeTrade exchangeTrade) {
    String[] periods = {"1min", "5min", "15min", "30min", "1hour", "4hour", "1day", "1week"};
    
    for (String period : periods) {
        KLine currentKLine = generateCurrentKLineForPeriod(period, exchangeTrade);
        if (currentKLine != null) {
            marketHandlerService.handleCurrentKLine(symbol, currentKLine);
        }
    }
}
```

#### 2. Dynamic K-line Generation
**File**: `DefaultCoinProcessor.java`

Added methods for dynamic K-line generation:
- `generateCurrentKLineForPeriod()`: Creates current K-line for specific timeframe
- `alignTimeToPeriod()`: Aligns timestamps to period boundaries
- `buildKLineFromTrades()`: Aggregates trades within a period
- `createKLineFromSingleTrade()`: Handles single trade scenarios

#### 3. WebSocket Topic Structure
**File**: `WebsocketMarketHandler.java`

The handler already supported the correct topic structure:
- **Complete K-lines**: `/topic/market/kline/{symbol}/{period}` (when periods end)
- **Current K-lines**: `/topic/market/current-kline/{symbol}/{period}` (with each trade)

### Client-UI Changes

#### 1. Resolution Mapping
**File**: `custom-data-feed.ts`

Added mapping from TradingView resolutions to backend periods:
```typescript
export const RESOLUTION_TO_PERIOD: Record<string, string> = {
  '1': '1min',
  '5': '5min',
  '15': '15min',
  '30': '30min',
  '60': '1hour',
  '240': '4hour',
  '1D': '1day',
  '1W': '1week',
};
```

#### 2. Current K-line Subscription
**File**: `custom-data-feed.ts`

Added subscription to current-kline topic for all timeframes:
```typescript
// Subscribe to current-kline updates for real-time incomplete candle data
const period = RESOLUTION_TO_PERIOD[resolution];
if (period) {
  stompClient.subscribe(
    `/topic/market/current-kline/${symbolInfo.name}/${period}`,
    function (msg) {
      const resp = JSON.parse(msg.body);
      if (resp.incomplete) {
        // Update chart with real-time data
        onRealtimeCallback(currentKlineBar);
      }
    }
  );
}
```

## Technical Architecture

### Event Flow
1. **Trade Occurs** → `DefaultCoinProcessor.process()`
2. **For Each Trade** → `pushCurrentKLineForAllTimeframes()`
3. **For Each Timeframe** → `generateCurrentKLineForPeriod()`
4. **WebSocket Push** → `marketHandlerService.handleCurrentKLine()`
5. **Client Receives** → Chart updates in real-time

### Supported Timeframes
- **1min**: 1-minute candles
- **5min**: 5-minute candles  
- **15min**: 15-minute candles
- **30min**: 30-minute candles
- **1hour**: 1-hour candles
- **4hour**: 4-hour candles
- **1day**: Daily candles
- **1week**: Weekly candles

### Data Structure
Current K-line messages include:
```json
{
  "symbol": "BTCUSDT",
  "time": 1640995200000,
  "period": "5min",
  "openPrice": 50000.00,
  "highestPrice": 50100.00,
  "lowestPrice": 49900.00,
  "closePrice": 50050.00,
  "volume": 1.5,
  "turnover": 75075.00,
  "count": 10,
  "incomplete": true
}
```

## Performance Considerations

### Optimization Strategies
1. **Efficient Trade Querying**: Uses time-range queries to get trades for each period
2. **Period Boundary Alignment**: Properly aligns timestamps to period boundaries
3. **Error Handling**: Graceful handling of edge cases and exceptions
4. **Memory Management**: Efficient K-line generation without excessive memory usage

### Scalability
- **Event-Driven**: Only generates K-lines when trades occur
- **Selective Updates**: Only processes relevant timeframes
- **Concurrent Processing**: Can handle multiple symbols simultaneously

## Testing

### Test Coverage
All tests pass successfully:
- **DefaultCoinProcessorHandlerTest**: Verifies Event-Driven Architecture (16 calls = 2 trades × 8 timeframes)
- **MarketHandlerServiceTest**: Confirms proper service integration
- **KLineWebSocketTest**: Validates WebSocket functionality

### Verification Points
1. ✅ Current K-lines pushed for all timeframes on each trade
2. ✅ Proper period boundary alignment
3. ✅ WebSocket topics correctly structured
4. ✅ Client-UI subscribes to all relevant topics
5. ✅ Error handling and edge cases covered

## Benefits

### 1. Complete Real-time Support
- All timeframes now receive real-time updates
- Consistent behavior across all chart resolutions
- Proper Event-Driven Architecture implementation

### 2. Improved User Experience
- Smooth chart animations for all timeframes
- Immediate price updates across all resolutions
- Accurate volume and turnover data

### 3. Specification Compliance
- Follows K-line specification requirements
- Proper separation of complete vs. current K-line data
- Consistent topic naming and data structure

### 4. Maintainable Architecture
- Clean separation of concerns
- Extensible for additional timeframes
- Robust error handling and logging

## Conclusion

The refactored implementation successfully addresses all the issues with the previous K-line real-time system. The Event-Driven Architecture ensures that current K-line updates are pushed for all supported timeframes whenever a trade occurs, providing a consistent and responsive user experience across all chart resolutions.

The system is now production-ready and fully compliant with the K-line specification requirements.
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MarketDataClient Test</title>
</head>
<body>
    <h1>MarketDataClient Test</h1>
    <div id="status">Testing...</div>
    <div id="results"></div>

    <!-- Include required libraries -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>
    <script src="js/robust-websocket-client.js"></script>
    
    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        
        function logResult(message, isError = false) {
            const p = document.createElement('p');
            p.textContent = message;
            p.style.color = isError ? 'red' : 'green';
            resultsDiv.appendChild(p);
            console.log(message);
        }

        // Test MarketDataClient definition and instantiation
        try {
            // Test 1: Check if MarketDataClient is defined
            if (typeof MarketDataClient === 'undefined') {
                throw new Error('MarketDataClient is not defined');
            }
            logResult('✅ MarketDataClient is defined');

            // Test 2: Check if RobustWebSocketClient is defined
            if (typeof RobustWebSocketClient === 'undefined') {
                throw new Error('RobustWebSocketClient is not defined');
            }
            logResult('✅ RobustWebSocketClient is defined');

            // Test 3: Try to instantiate MarketDataClient
            const marketClient = new MarketDataClient();
            logResult('✅ MarketDataClient can be instantiated');

            // Test 4: Check if wsClient property exists
            if (!marketClient.wsClient) {
                throw new Error('wsClient property is missing');
            }
            logResult('✅ wsClient property exists');

            // Test 5: Check if required methods exist
            const requiredMethods = ['connect', 'disconnect', 'getConnectionStats'];
            for (const method of requiredMethods) {
                if (typeof marketClient[method] !== 'function') {
                    throw new Error(`Method ${method} is missing or not a function`);
                }
            }
            logResult('✅ All required methods exist');

            // Test 6: Check if wsClient has required methods and properties
            const wsClientMethods = ['on', 'connect', 'disconnect', 'subscribe'];
            for (const method of wsClientMethods) {
                if (typeof marketClient.wsClient[method] !== 'function') {
                    throw new Error(`wsClient method ${method} is missing or not a function`);
                }
            }
            logResult('✅ wsClient has all required methods');

            // Test 7: Check if config can be set
            marketClient.wsClient.config.baseUrl = 'http://localhost:6004';
            marketClient.wsClient.config.socketPath = 'market/market-ws';
            logResult('✅ Configuration can be set');

            statusDiv.textContent = '✅ All tests passed! MarketDataClient is working correctly.';
            statusDiv.style.color = 'green';

        } catch (error) {
            logResult(`❌ Error: ${error.message}`, true);
            statusDiv.textContent = `❌ Test failed: ${error.message}`;
            statusDiv.style.color = 'red';
        }
    </script>
</body>
</html>
# Kline WebSocket Test UI

A simple UI for testing and debugging WebSocket connections for kline data in the Bitcello market application.

## Purpose

This test UI allows developers to:

- Connect to WebSocket endpoints for market data
- Subscribe to specific topics for real-time market data
- View and debug the data received from these topics
- Test different symbols and time periods

## Topics Tested

The test UI supports the following WebSocket topics:

1. `/topic/market/trade/<symbol>` - Real-time trade data
2. `/topic/market/kline/<symbol>` - Completed kline (candlestick) data
3. `/topic/market/current-kline/<symbol>/<period>` - Current (incomplete) kline data

## Setup and Usage

### Running the Test UI

1. Simply open the `index.html` file in a web browser
2. No build process is required as this is a standalone HTML/JS application

### Connection Settings

1. **Base URL**: The base URL of your server (default: `http://localhost:8080`)
2. **Socket Path**: The WebSocket endpoint path
   - `market/market-ws` for regular market
   - `future/contract-ws` for futures market
3. **Symbol**: The trading pair to subscribe to (e.g., `BTC/USDT`)
4. **Period**: The time period for kline data (e.g., `1min`, `5min`, `1hour`, etc.)

### Using the Test UI

1. Configure your connection settings
2. Click "Connect" to establish a WebSocket connection
3. Use the toggle switches to subscribe/unsubscribe from individual topics
4. Or use "Subscribe All" to subscribe to all topics at once
5. View the received messages in the respective message containers
6. Use "Clear Logs" to clear all message displays
7. Click "Disconnect" when finished

## Features

- Real-time connection to WebSocket server
- Individual topic subscription controls
- Formatted JSON display of received messages
- Timestamp for each received message
- Connection status indicator
- Support for both regular market and futures market
- Clear and intuitive user interface

## Development

This test UI is separate from the main client-ui application and is designed specifically for testing and debugging WebSocket connections. It uses:

- HTML5
- CSS3 (with Bootstrap 5)
- JavaScript (ES6+)
- SockJS for WebSocket connections
- STOMP for message protocol

## Troubleshooting

If you encounter issues:

1. Check that your server is running and accessible
2. Verify that the WebSocket endpoints are correctly configured
3. Check browser console for any JavaScript errors
4. Ensure the symbol format matches what the server expects
/**
 * Robust WebSocket Client for Market Data
 * Provides automatic reconnection, error handling, and message buffering
 * to prevent connection drops when consuming high-frequency topics like 5min
 */

class RobustWebSocketClient {
    constructor(config = {}) {
        this.config = {
            baseUrl: 'http://localhost:6004',
            socketPath: 'market/market-ws',
            reconnectInterval: 3000,
            maxReconnectAttempts: 10,
            heartbeatInterval: 30000,
            messageBufferSize: 100,
            connectionTimeout: 10000,
            ...config
        };

        // Connection state
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.connectionStartTime = null;
        this.lastMessageTime = null;

        // WebSocket components
        this.socket = null;
        this.stompClient = null;

        // Subscriptions and message handling
        this.subscriptions = new Map();
        this.messageBuffer = new Map();
        this.eventHandlers = new Map();

        // Statistics
        this.stats = {
            messagesReceived: 0,
            messagesDropped: 0,
            reconnectCount: 0,
            connectionUptime: 0,
            lastMessageTime: null
        };

        // Timers
        this.heartbeatTimer = null;
        this.reconnectTimer = null;
        this.statsTimer = null;

        this.initializeEventHandlers();
    }

    initializeEventHandlers() {
        this.eventHandlers.set('connected', []);
        this.eventHandlers.set('disconnected', []);
        this.eventHandlers.set('error', []);
        this.eventHandlers.set('messageError', []);
        this.eventHandlers.set('maxReconnectAttemptsReached', []);
    }

    on(event, handler) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).push(handler);
        }
    }

    emit(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }

    async connect() {
        if (this.isConnected || this.isConnecting) {
            return;
        }

        this.isConnecting = true;
        
        try {
            const fullSocketUrl = `${this.config.baseUrl}/${this.config.socketPath}`;
            console.log(`Connecting to WebSocket: ${fullSocketUrl}`);

            // Create SockJS socket
            this.socket = new SockJS(fullSocketUrl);
            this.stompClient = Stomp.over(this.socket);

            // Disable debug logs
            this.stompClient.debug = null;

            // Set up connection timeout
            const connectionPromise = new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, this.config.connectionTimeout);

                this.stompClient.connect({}, 
                    () => {
                        clearTimeout(timeout);
                        resolve();
                    },
                    (error) => {
                        clearTimeout(timeout);
                        reject(new Error(`Connection failed: ${error}`));
                    }
                );
            });

            await connectionPromise;

            // Connection successful
            this.isConnected = true;
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.connectionStartTime = Date.now();

            // Start heartbeat and statistics
            this.startHeartbeat();
            this.startStatsTimer();

            // Restore subscriptions
            this.restoreSubscriptions();

            this.emit('connected');
            console.log('WebSocket connected successfully');

        } catch (error) {
            this.isConnecting = false;
            this.handleConnectionError(error);
            throw error;
        }
    }

    async disconnect() {
        this.isConnected = false;
        this.isConnecting = false;

        // Clear timers
        this.stopHeartbeat();
        this.stopStatsTimer();
        this.clearReconnectTimer();

        // Clear subscriptions
        this.subscriptions.clear();

        // Disconnect STOMP client
        if (this.stompClient) {
            try {
                this.stompClient.disconnect(() => {
                    console.log('WebSocket disconnected');
                });
            } catch (error) {
                console.error('Error during disconnect:', error);
            }
        }

        this.stompClient = null;
        this.socket = null;

        this.emit('disconnected');
    }

    subscribe(topic, messageHandler, errorHandler = null) {
        // Store subscription for reconnection
        this.subscriptions.set(topic, { messageHandler, errorHandler });

        if (!this.isConnected || !this.stompClient) {
            console.warn(`Cannot subscribe to ${topic}: not connected`);
            return null;
        }

        try {
            const subscription = this.stompClient.subscribe(topic, (message) => {
                try {
                    const data = JSON.parse(message.body);
                    this.stats.messagesReceived++;
                    this.stats.lastMessageTime = Date.now();
                    this.lastMessageTime = Date.now();

                    // Buffer message for reliability
                    this.bufferMessage(topic, data);

                    // Call message handler
                    messageHandler(data);

                } catch (error) {
                    this.stats.messagesDropped++;
                    console.error(`Error processing message from ${topic}:`, error);
                    
                    if (errorHandler) {
                        errorHandler(error);
                    }
                    
                    this.emit('messageError', { topic, error });
                }
            });

            console.log(`Subscribed to ${topic}`);
            return subscription;

        } catch (error) {
            console.error(`Error subscribing to ${topic}:`, error);
            
            if (errorHandler) {
                errorHandler(error);
            }
            
            this.emit('messageError', { topic, error });
            return null;
        }
    }

    bufferMessage(topic, data) {
        if (!this.messageBuffer.has(topic)) {
            this.messageBuffer.set(topic, []);
        }

        const buffer = this.messageBuffer.get(topic);
        buffer.push({
            timestamp: Date.now(),
            data: data
        });

        // Keep buffer size limited
        if (buffer.length > this.config.messageBufferSize) {
            buffer.shift();
        }
    }

    restoreSubscriptions() {
        console.log(`Restoring ${this.subscriptions.size} subscriptions`);
        
        for (const [topic, handlers] of this.subscriptions) {
            this.subscribe(topic, handlers.messageHandler, handlers.errorHandler);
        }
    }

    handleConnectionError(error) {
        console.error('Connection error:', error);
        this.emit('error', error.message || error);

        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
        } else {
            console.error('Maximum reconnection attempts reached');
            this.emit('maxReconnectAttemptsReached');
        }
    }

    scheduleReconnect() {
        if (this.reconnectTimer) {
            return;
        }

        this.reconnectAttempts++;
        this.stats.reconnectCount++;

        // Exponential backoff with jitter
        const baseDelay = this.config.reconnectInterval;
        const exponentialDelay = baseDelay * Math.pow(1.5, this.reconnectAttempts - 1);
        const jitter = Math.random() * 1000;
        const delay = Math.min(exponentialDelay + jitter, 30000); // Max 30 seconds

        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${Math.round(delay)}ms`);

        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = null;
            
            try {
                await this.connect();
            } catch (error) {
                console.error('Reconnection failed:', error);
            }
        }, delay);
    }

    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected && this.stompClient) {
                try {
                    // Send a simple heartbeat message
                    this.stompClient.send('/app/heartbeat', {}, JSON.stringify({
                        timestamp: Date.now(),
                        client: 'robust-websocket-client'
                    }));
                } catch (error) {
                    console.warn('Heartbeat failed:', error);
                    this.handleConnectionError(new Error('Heartbeat failed'));
                }
            }
        }, this.config.heartbeatInterval);
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    startStatsTimer() {
        this.stopStatsTimer();
        
        this.statsTimer = setInterval(() => {
            if (this.connectionStartTime) {
                this.stats.connectionUptime = Date.now() - this.connectionStartTime;
            }
        }, 1000);
    }

    stopStatsTimer() {
        if (this.statsTimer) {
            clearInterval(this.statsTimer);
            this.statsTimer = null;
        }
    }

    getConnectionStats() {
        return {
            ...this.stats,
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            subscriptionCount: this.subscriptions.size,
            bufferSize: Array.from(this.messageBuffer.values()).reduce((total, buffer) => total + buffer.length, 0)
        };
    }

    getBufferedMessages(topic) {
        return this.messageBuffer.get(topic) || [];
    }

    clearBuffer(topic = null) {
        if (topic) {
            this.messageBuffer.delete(topic);
        } else {
            this.messageBuffer.clear();
        }
    }
}

/**
 * Market Data Client - High-level wrapper for market-specific functionality
 */
class MarketDataClient {
    constructor(config = {}) {
        this.wsClient = new RobustWebSocketClient(config);
        this.activeSubscriptions = new Set();
    }

    async connect() {
        return await this.wsClient.connect();
    }

    async disconnect() {
        this.activeSubscriptions.clear();
        return await this.wsClient.disconnect();
    }

    subscribeToCurrentKline(symbol, period, messageHandler, errorHandler) {
        const topic = `/topic/market/current-kline/${symbol}/${period}`;
        this.activeSubscriptions.add(topic);
        return this.wsClient.subscribe(topic, messageHandler, errorHandler);
    }

    subscribeToKline(symbol, period, messageHandler, errorHandler) {
        const topic = `/topic/market/kline/${symbol}/${period}`;
        this.activeSubscriptions.add(topic);
        return this.wsClient.subscribe(topic, messageHandler, errorHandler);
    }

    subscribeToTrade(symbol, messageHandler, errorHandler) {
        const topic = `/topic/market/trade/${symbol}`;
        this.activeSubscriptions.add(topic);
        return this.wsClient.subscribe(topic, messageHandler, errorHandler);
    }

    getConnectionStats() {
        return this.wsClient.getConnectionStats();
    }

    isConnected() {
        return this.wsClient.isConnected;
    }

    getActiveSubscriptions() {
        return Array.from(this.activeSubscriptions);
    }
}

// Export for use in other modules (if using modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { RobustWebSocketClient, MarketDataClient };
}
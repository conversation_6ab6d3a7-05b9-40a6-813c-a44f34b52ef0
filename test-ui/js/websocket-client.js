// Global variables
let stompClient = null;
let socket = null;
let subscriptions = {
    trade: null,
    kline: null,
    currentKline: null
};

// DOM elements
const connectBtn = document.getElementById('connectBtn');
const disconnectBtn = document.getElementById('disconnectBtn');
const subscribeAllBtn = document.getElementById('subscribeAllBtn');
const unsubscribeAllBtn = document.getElementById('unsubscribeAllBtn');
const clearLogsBtn = document.getElementById('clearLogsBtn');
const connectionStatus = document.getElementById('connectionStatus');
const tradeTopicCheckbox = document.getElementById('tradeTopic');
const klineTopicCheckbox = document.getElementById('klineTopic');
const currentKlineTopicCheckbox = document.getElementById('currentKlineTopic');
const symbolPlaceholders = document.querySelectorAll('.symbol-placeholder');
const periodPlaceholders = document.querySelectorAll('.period-placeholder');

// Message containers
const tradeMessages = document.getElementById('tradeMessages');
const klineMessages = document.getElementById('klineMessages');
const currentKlineMessages = document.getElementById('currentKlineMessages');

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Update symbol and period placeholders when inputs change
    document.getElementById('symbol').addEventListener('input', updatePlaceholders);
    document.getElementById('period').addEventListener('change', updatePlaceholders);
    
    // Button event listeners
    connectBtn.addEventListener('click', connect);
    disconnectBtn.addEventListener('click', disconnect);
    subscribeAllBtn.addEventListener('click', subscribeAll);
    unsubscribeAllBtn.addEventListener('click', unsubscribeAll);
    clearLogsBtn.addEventListener('click', clearLogs);
    
    // Topic checkbox event listeners
    tradeTopicCheckbox.addEventListener('change', function() {
        this.checked ? subscribeTrade() : unsubscribeTrade();
    });
    
    klineTopicCheckbox.addEventListener('change', function() {
        this.checked ? subscribeKline() : unsubscribeKline();
    });
    
    currentKlineTopicCheckbox.addEventListener('change', function() {
        this.checked ? subscribeCurrentKline() : unsubscribeCurrentKline();
    });
    
    // Initialize placeholders
    updatePlaceholders();
});

// Update symbol and period placeholders in the UI
function updatePlaceholders() {
    const symbol = document.getElementById('symbol').value;
    const period = document.getElementById('period').value;
    
    symbolPlaceholders.forEach(placeholder => {
        placeholder.textContent = symbol;
    });
    
    periodPlaceholders.forEach(placeholder => {
        placeholder.textContent = period;
    });
}

// Connect to WebSocket
function connect() {
    const baseUrl = document.getElementById('baseUrl').value;
    const socketPath = document.getElementById('socketPath').value;
    const fullSocketUrl = `${baseUrl}/${socketPath}`;
    
    try {
        socket = new SockJS(fullSocketUrl);
        stompClient = Stomp.over(socket);
        
        // Disable debug logs
        stompClient.debug = null;
        
        // Connect to the WebSocket server
        stompClient.connect({}, onConnected, onError);
        
        // Update UI to show connecting state
        connectionStatus.textContent = 'Connecting...';
        connectionStatus.className = '';
        
    } catch (error) {
        logMessage('Error creating connection: ' + error.message, 'error');
        updateConnectionStatus(false);
    }
}

// Handle successful connection
function onConnected() {
    // Update UI for connected state
    updateConnectionStatus(true);
    
    // Enable subscription controls
    tradeTopicCheckbox.disabled = false;
    klineTopicCheckbox.disabled = false;
    currentKlineTopicCheckbox.disabled = false;
    subscribeAllBtn.disabled = false;
    unsubscribeAllBtn.disabled = false;
    
    logMessage('Connected to WebSocket server', 'success');
}

// Handle connection error
function onError(error) {
    logMessage('Error connecting to WebSocket: ' + error, 'error');
    updateConnectionStatus(false);
}

// Disconnect from WebSocket
function disconnect() {
    if (stompClient) {
        // Unsubscribe from all topics first
        unsubscribeAll();
        
        // Disconnect the client
        stompClient.disconnect(() => {
            logMessage('Disconnected from WebSocket server', 'info');
            updateConnectionStatus(false);
        });
    }
    
    stompClient = null;
    socket = null;
}

// Update connection status in UI
function updateConnectionStatus(connected) {
    if (connected) {
        connectionStatus.textContent = 'Connected';
        connectionStatus.className = 'connected';
        connectBtn.disabled = true;
        disconnectBtn.disabled = false;
    } else {
        connectionStatus.textContent = 'Disconnected';
        connectionStatus.className = 'disconnected';
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
        
        // Disable subscription controls
        tradeTopicCheckbox.disabled = true;
        klineTopicCheckbox.disabled = true;
        currentKlineTopicCheckbox.disabled = true;
        subscribeAllBtn.disabled = true;
        unsubscribeAllBtn.disabled = true;
        
        // Reset checkboxes
        tradeTopicCheckbox.checked = false;
        klineTopicCheckbox.checked = false;
        currentKlineTopicCheckbox.checked = false;
    }
}

// Subscribe to all topics
function subscribeAll() {
    tradeTopicCheckbox.checked = true;
    klineTopicCheckbox.checked = true;
    currentKlineTopicCheckbox.checked = true;
    
    subscribeTrade();
    subscribeKline();
    subscribeCurrentKline();
}

// Unsubscribe from all topics
function unsubscribeAll() {
    tradeTopicCheckbox.checked = false;
    klineTopicCheckbox.checked = false;
    currentKlineTopicCheckbox.checked = false;
    
    unsubscribeTrade();
    unsubscribeKline();
    unsubscribeCurrentKline();
}

// Subscribe to trade topic
function subscribeTrade() {
    if (!stompClient || subscriptions.trade) return;
    
    const symbol = document.getElementById('symbol').value;
    const tradeTopic = `/topic/market/trade/${symbol}`;
    
    try {
        subscriptions.trade = stompClient.subscribe(tradeTopic, function(message) {
            const data = JSON.parse(message.body);
            displayTradeMessage(data);
        });
        
        logMessage(`Subscribed to ${tradeTopic}`, 'info');
    } catch (error) {
        logMessage(`Error subscribing to ${tradeTopic}: ${error.message}`, 'error');
        tradeTopicCheckbox.checked = false;
    }
}

// Subscribe to kline topic
function subscribeKline() {
    if (!stompClient || subscriptions.kline) return;
    
    const symbol = document.getElementById('symbol').value;
    const klineTopic = `/topic/market/kline/${symbol}`;
    
    try {
        subscriptions.kline = stompClient.subscribe(klineTopic, function(message) {
            const data = JSON.parse(message.body);
            displayKlineMessage(data);
        });
        
        logMessage(`Subscribed to ${klineTopic}`, 'info');
    } catch (error) {
        logMessage(`Error subscribing to ${klineTopic}: ${error.message}`, 'error');
        klineTopicCheckbox.checked = false;
    }
}

// Subscribe to current kline topic
function subscribeCurrentKline() {
    if (!stompClient || subscriptions.currentKline) return;
    
    const symbol = document.getElementById('symbol').value;
    const period = document.getElementById('period').value;
    const currentKlineTopic = `/topic/market/current-kline/${symbol}/${period}`;
    
    try {
        subscriptions.currentKline = stompClient.subscribe(currentKlineTopic, function(message) {
            const data = JSON.parse(message.body);
            displayCurrentKlineMessage(data);
        });
        
        logMessage(`Subscribed to ${currentKlineTopic}`, 'info');
    } catch (error) {
        logMessage(`Error subscribing to ${currentKlineTopic}: ${error.message}`, 'error');
        currentKlineTopicCheckbox.checked = false;
    }
}

// Unsubscribe from trade topic
function unsubscribeTrade() {
    if (subscriptions.trade) {
        subscriptions.trade.unsubscribe();
        subscriptions.trade = null;
        logMessage('Unsubscribed from trade topic', 'info');
    }
}

// Unsubscribe from kline topic
function unsubscribeKline() {
    if (subscriptions.kline) {
        subscriptions.kline.unsubscribe();
        subscriptions.kline = null;
        logMessage('Unsubscribed from kline topic', 'info');
    }
}

// Unsubscribe from current kline topic
function unsubscribeCurrentKline() {
    if (subscriptions.currentKline) {
        subscriptions.currentKline.unsubscribe();
        subscriptions.currentKline = null;
        logMessage('Unsubscribed from current kline topic', 'info');
    }
}

// Display trade message
function displayTradeMessage(data) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    
    const timestamp = new Date().toLocaleTimeString();
    const formattedData = JSON.stringify(data, null, 2);
    
    messageDiv.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <pre>${formattedData}</pre>
    `;
    
    tradeMessages.appendChild(messageDiv);
    tradeMessages.scrollTop = tradeMessages.scrollHeight;
}

// Display kline message
function displayKlineMessage(data) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    
    const timestamp = new Date().toLocaleTimeString();
    const formattedData = JSON.stringify(data, null, 2);
    
    messageDiv.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <pre>${formattedData}</pre>
    `;
    
    klineMessages.appendChild(messageDiv);
    klineMessages.scrollTop = klineMessages.scrollHeight;
}

// Display current kline message
function displayCurrentKlineMessage(data) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message';
    
    const timestamp = new Date().toLocaleTimeString();
    const formattedData = JSON.stringify(data, null, 2);
    
    messageDiv.innerHTML = `
        <div class="timestamp">${timestamp}</div>
        <pre>${formattedData}</pre>
    `;
    
    currentKlineMessages.appendChild(messageDiv);
    currentKlineMessages.scrollTop = currentKlineMessages.scrollHeight;
}

// Log a message to the console and UI
function logMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // You could also add a general log area in the UI if needed
}

// Clear all message logs
function clearLogs() {
    tradeMessages.innerHTML = '';
    klineMessages.innerHTML = '';
    currentKlineMessages.innerHTML = '';
    logMessage('Cleared all message logs', 'info');
}
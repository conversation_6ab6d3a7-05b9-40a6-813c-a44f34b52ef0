<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robust WebSocket Client Test - 5min Topic Connection Fix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
        
        .control-group input, .control-group select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover:not(:disabled) {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover:not(:disabled) {
            background: #1e7e34;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .status-panel {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }
        
        .status-card h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 16px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .status-label {
            color: #6c757d;
        }
        
        .status-value {
            font-weight: 500;
            color: #495057;
        }
        
        .status-connected {
            color: #28a745;
        }
        
        .status-disconnected {
            color: #dc3545;
        }
        
        .status-connecting {
            color: #ffc107;
        }
        
        .messages-panel {
            padding: 20px;
        }
        
        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .messages-header h3 {
            margin: 0;
            color: #495057;
        }
        
        .message-tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 15px;
        }
        
        .message-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
            transition: all 0.2s;
        }
        
        .message-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        
        .message-tab:hover {
            color: #007bff;
        }
        
        .message-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: #ffffff;
        }
        
        .message {
            padding: 12px;
            border-bottom: 1px solid #f1f3f4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .message:last-child {
            border-bottom: none;
        }
        
        .message-timestamp {
            color: #6c757d;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .message-content {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 3px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .message-5min {
            border-left: 4px solid #007bff;
        }
        
        .message-1min {
            border-left: 4px solid #28a745;
        }
        
        .message-15min {
            border-left: 4px solid #ffc107;
        }
        
        .message-30min {
            border-left: 4px solid #dc3545;
        }
        
        .message-error {
            border-left: 4px solid #dc3545;
            background: #fff5f5;
        }
        
        .message-info {
            border-left: 4px solid #17a2b8;
            background: #f0f9ff;
        }
        
        .hidden {
            display: none;
        }
        
        .alert {
            padding: 12px 16px;
            margin-bottom: 15px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Robust WebSocket Client Test</h1>
            <p>Testing connection stability for 5min topic with enhanced error handling and reconnection</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="baseUrl">Base URL:</label>
                <input type="text" id="baseUrl" value="http://localhost:6004" />
            </div>
            
            <div class="control-group">
                <label for="socketPath">Socket Path:</label>
                <input type="text" id="socketPath" value="market/market-ws" />
            </div>
            
            <div class="control-group">
                <label for="symbol">Symbol:</label>
                <input type="text" id="symbol" value="ETH/USDT" />
            </div>
            
            <div class="control-group">
                <label for="testDuration">Test Duration (minutes):</label>
                <select id="testDuration">
                    <option value="5">5 minutes</option>
                    <option value="10" selected>10 minutes</option>
                    <option value="15">15 minutes</option>
                    <option value="30">30 minutes</option>
                </select>
            </div>
            
            <div class="control-group">
                <button id="connectBtn" class="btn btn-primary">Connect</button>
                <button id="disconnectBtn" class="btn btn-danger" disabled>Disconnect</button>
                <button id="startTestBtn" class="btn btn-success" disabled>Start 5min Test</button>
                <button id="clearLogsBtn" class="btn">Clear Logs</button>
            </div>
        </div>
        
        <div class="status-panel">
            <div class="status-card">
                <h3>📡 Connection Status</h3>
                <div class="status-item">
                    <span class="status-label">Status:</span>
                    <span id="connectionStatus" class="status-value status-disconnected">Disconnected</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Reconnect Attempts:</span>
                    <span id="reconnectAttempts" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Uptime:</span>
                    <span id="connectionUptime" class="status-value">0s</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Last Message:</span>
                    <span id="lastMessageTime" class="status-value">Never</span>
                </div>
            </div>
            
            <div class="status-card">
                <h3>📊 Message Statistics</h3>
                <div class="status-item">
                    <span class="status-label">Messages Received:</span>
                    <span id="messagesReceived" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Messages Dropped:</span>
                    <span id="messagesDropped" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">5min Messages:</span>
                    <span id="messages5min" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Connection Drops:</span>
                    <span id="connectionDrops" class="status-value">0</span>
                </div>
            </div>
        </div>
        
        <div class="messages-panel">
            <div class="messages-header">
                <h3>📝 Message Log</h3>
                <div>
                    <label>
                        <input type="checkbox" id="autoScroll" checked> Auto-scroll
                    </label>
                </div>
            </div>
            
            <div class="message-tabs">
                <button class="message-tab active" data-tab="all">All Messages</button>
                <button class="message-tab" data-tab="5min">5min Topic</button>
                <button class="message-tab" data-tab="errors">Errors</button>
                <button class="message-tab" data-tab="connection">Connection</button>
            </div>
            
            <div id="messageContainer" class="message-container">
                <div class="alert alert-info">
                    <strong>Ready to test!</strong> Click "Connect" to start the robust WebSocket client, then "Start 5min Test" to begin monitoring the problematic 5min topic.
                </div>
            </div>
        </div>
    </div>

    <!-- Include required libraries -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>
    <script src="js/robust-websocket-client.js"></script>
    
    <script>
        // Global variables
        let marketClient = null;
        let testTimer = null;
        let statsTimer = null;
        let messageCount = {
            total: 0,
            '5min': 0,
            errors: 0,
            connectionEvents: 0
        };
        let connectionDrops = 0;
        let currentTab = 'all';

        // DOM elements
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const startTestBtn = document.getElementById('startTestBtn');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const messageContainer = document.getElementById('messageContainer');
        const autoScrollCheckbox = document.getElementById('autoScroll');

        // Status elements
        const connectionStatus = document.getElementById('connectionStatus');
        const reconnectAttempts = document.getElementById('reconnectAttempts');
        const connectionUptime = document.getElementById('connectionUptime');
        const lastMessageTime = document.getElementById('lastMessageTime');
        const messagesReceived = document.getElementById('messagesReceived');
        const messagesDropped = document.getElementById('messagesDropped');
        const messages5min = document.getElementById('messages5min');
        const connectionDropsElement = document.getElementById('connectionDrops');

        // Initialize event listeners
        document.addEventListener('DOMContentLoaded', function() {
            connectBtn.addEventListener('click', connect);
            disconnectBtn.addEventListener('click', disconnect);
            startTestBtn.addEventListener('click', startTest);
            clearLogsBtn.addEventListener('click', clearLogs);
            
            // Tab switching
            document.querySelectorAll('.message-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });
        });

        // Connect to WebSocket
        async function connect() {
            const baseUrl = document.getElementById('baseUrl').value;
            const socketPath = document.getElementById('socketPath').value;
            
            try {
                // Create robust WebSocket client
                marketClient = new MarketDataClient();
                marketClient.wsClient.config.baseUrl = baseUrl;
                marketClient.wsClient.config.socketPath = socketPath;
                
                // Setup enhanced event handlers
                setupEventHandlers();
                
                // Connect
                await marketClient.connect();
                
                // Update UI
                updateConnectionUI(true);
                logMessage('✅ Connected successfully with robust client', 'connection', 'success');
                
                // Start statistics timer
                startStatsTimer();
                
            } catch (error) {
                logMessage(`❌ Connection failed: ${error.message}`, 'connection', 'error');
                updateConnectionUI(false);
            }
        }

        // Setup event handlers for the robust client
        function setupEventHandlers() {
            marketClient.wsClient.on('connected', () => {
                logMessage('🔗 WebSocket connected', 'connection', 'success');
                updateConnectionUI(true);
            });

            marketClient.wsClient.on('disconnected', () => {
                logMessage('🔌 WebSocket disconnected', 'connection', 'error');
                updateConnectionUI(false);
                connectionDrops++;
            });

            marketClient.wsClient.on('error', (error) => {
                logMessage(`🚨 Connection error: ${error}`, 'connection', 'error');
                messageCount.errors++;
            });

            marketClient.wsClient.on('messageError', ({ topic, error }) => {
                logMessage(`💥 Message error on ${topic}: ${error.message}`, 'errors', 'error');
                messageCount.errors++;
            });

            marketClient.wsClient.on('maxReconnectAttemptsReached', () => {
                logMessage('⚠️ Maximum reconnection attempts reached', 'connection', 'error');
            });
        }

        // Disconnect from WebSocket
        async function disconnect() {
            if (marketClient) {
                await marketClient.disconnect();
                marketClient = null;
            }
            
            if (testTimer) {
                clearInterval(testTimer);
                testTimer = null;
            }
            
            if (statsTimer) {
                clearInterval(statsTimer);
                statsTimer = null;
            }
            
            updateConnectionUI(false);
            logMessage('🔌 Disconnected', 'connection', 'info');
        }

        // Start the 5min topic test
        function startTest() {
            if (!marketClient) {
                logMessage('❌ Not connected', 'errors', 'error');
                return;
            }

            const symbol = document.getElementById('symbol').value;
            const duration = parseInt(document.getElementById('testDuration').value);
            
            logMessage(`🧪 Starting 5min topic test for ${duration} minutes`, 'connection', 'info');
            
            // Subscribe to 5min topic with enhanced monitoring
            marketClient.wsClient.subscribe(
                `/topic/market/current-kline/${symbol}/5min`,
                (data) => {
                    messageCount['5min']++;
                    messageCount.total++;
                    
                    logMessage(`📊 5min kline: ${data.symbol} @ ${new Date(data.time).toLocaleTimeString()} - Close: ${data.closePrice}, Vol: ${data.volume}`, '5min', 'info');
                },
                (error) => {
                    messageCount.errors++;
                    logMessage(`❌ 5min subscription error: ${error.message}`, 'errors', 'error');
                }
            );

            // Subscribe to other periods for comparison
            ['1min', '15min', '30min'].forEach(period => {
                marketClient.wsClient.subscribe(
                    `/topic/market/current-kline/${symbol}/${period}`,
                    (data) => {
                        messageCount.total++;
                        logMessage(`📈 ${period} kline: Close ${data.closePrice}`, 'all', 'info');
                    }
                );
            });

            // Set test timer
            testTimer = setTimeout(() => {
                logMessage(`✅ Test completed after ${duration} minutes`, 'connection', 'success');
                showTestResults();
            }, duration * 60 * 1000);

            startTestBtn.disabled = true;
        }

        // Show test results
        function showTestResults() {
            const stats = marketClient.getConnectionStats();
            const uptime = Math.round(stats.connectionUptime / 1000);
            
            logMessage('📋 Test Results:', 'connection', 'info');
            logMessage(`   • Total Messages: ${messageCount.total}`, 'connection', 'info');
            logMessage(`   • 5min Messages: ${messageCount['5min']}`, 'connection', 'info');
            logMessage(`   • Errors: ${messageCount.errors}`, 'connection', 'info');
            logMessage(`   • Connection Drops: ${connectionDrops}`, 'connection', 'info');
            logMessage(`   • Reconnections: ${stats.reconnectCount}`, 'connection', 'info');
            logMessage(`   • Uptime: ${uptime}s`, 'connection', 'info');
            logMessage(`   • Success Rate: ${((messageCount['5min'] / (messageCount['5min'] + messageCount.errors)) * 100).toFixed(1)}%`, 'connection', 'success');
        }

        // Update connection UI
        function updateConnectionUI(connected) {
            if (connected) {
                connectionStatus.textContent = 'Connected';
                connectionStatus.className = 'status-value status-connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                startTestBtn.disabled = false;
            } else {
                connectionStatus.textContent = 'Disconnected';
                connectionStatus.className = 'status-value status-disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                startTestBtn.disabled = true;
            }
        }

        // Start statistics timer
        function startStatsTimer() {
            if (statsTimer) {
                clearInterval(statsTimer);
            }
            
            statsTimer = setInterval(() => {
                if (marketClient) {
                    const stats = marketClient.getConnectionStats();
                    
                    reconnectAttempts.textContent = stats.reconnectCount;
                    connectionUptime.textContent = Math.round(stats.connectionUptime / 1000) + 's';
                    messagesReceived.textContent = stats.messagesReceived;
                    messagesDropped.textContent = stats.messagesDropped;
                    messages5min.textContent = messageCount['5min'];
                    connectionDropsElement.textContent = connectionDrops;
                    
                    if (stats.lastMessageTime) {
                        lastMessageTime.textContent = new Date(stats.lastMessageTime).toLocaleTimeString();
                    }
                }
            }, 1000);
        }

        // Log message with categorization
        function logMessage(message, category = 'all', level = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${category}`;
            
            const timestamp = new Date().toLocaleTimeString();
            
            messageDiv.innerHTML = `
                <div class="message-timestamp">${timestamp}</div>
                <div class="message-content">${message}</div>
            `;
            
            // Add to appropriate category
            messageDiv.dataset.category = category;
            messageDiv.dataset.level = level;
            
            messageContainer.appendChild(messageDiv);
            
            // Auto-scroll if enabled
            if (autoScrollCheckbox.checked) {
                messageContainer.scrollTop = messageContainer.scrollHeight;
            }
            
            // Update message count
            messageCount.connectionEvents++;
            
            // Filter messages based on current tab
            filterMessages();
        }

        // Switch message tab
        function switchTab(tab) {
            currentTab = tab;
            
            // Update tab UI
            document.querySelectorAll('.message-tab').forEach(t => {
                t.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tab}"]`).classList.add('active');
            
            // Filter messages
            filterMessages();
        }

        // Filter messages based on current tab
        function filterMessages() {
            const messages = messageContainer.querySelectorAll('.message');
            
            messages.forEach(message => {
                const category = message.dataset.category;
                const shouldShow = currentTab === 'all' || category === currentTab;
                message.style.display = shouldShow ? 'block' : 'none';
            });
        }

        // Clear all logs
        function clearLogs() {
            messageContainer.innerHTML = '';
            messageCount = {
                total: 0,
                '5min': 0,
                errors: 0,
                connectionEvents: 0
            };
            connectionDrops = 0;
            
            logMessage('🧹 Logs cleared', 'connection', 'info');
        }
    </script>
</body>
</html>
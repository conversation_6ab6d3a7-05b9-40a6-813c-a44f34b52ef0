<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kline WebSocket Test UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .message-container {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 10px;
            margin-bottom: 20px;
            font-family: monospace;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #e9ecef;
        }
        .message pre {
            margin: 0;
            white-space: pre-wrap;
        }
        .connected {
            color: green;
        }
        .disconnected {
            color: red;
        }
        .subscribed {
            color: blue;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Kline WebSocket Test UI</h1>
        <p>Use this page to test and debug WebSocket connections for kline data.</p>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Connection Settings</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="baseUrl" class="form-label">Base URL</label>
                            <input type="text" class="form-control" id="baseUrl" value="http://localhost:8080">
                        </div>
                        <div class="mb-3">
                            <label for="socketPath" class="form-label">Socket Path</label>
                            <select class="form-select" id="socketPath">
                                <option value="market/market-ws">market/market-ws (Regular Market)</option>
                                <option value="future/contract-ws">future/contract-ws (Futures Market)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="symbol" class="form-label">Symbol</label>
                            <input type="text" class="form-control" id="symbol" value="BTC/USDT">
                        </div>
                        <div class="mb-3">
                            <label for="period" class="form-label">Period</label>
                            <select class="form-select" id="period">
                                <option value="1min">1min</option>
                                <option value="5min">5min</option>
                                <option value="15min">15min</option>
                                <option value="30min">30min</option>
                                <option value="1hour">1hour</option>
                                <option value="4hour">4hour</option>
                                <option value="1day">1day</option>
                                <option value="1week">1week</option>
                            </select>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button id="connectBtn" class="btn btn-primary">Connect</button>
                            <button id="disconnectBtn" class="btn btn-danger" disabled>Disconnect</button>
                            <span id="connectionStatus" class="disconnected align-self-center">Disconnected</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Subscription Controls</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="tradeTopic" disabled>
                                <label class="form-check-label" for="tradeTopic">
                                    /topic/market/trade/<span class="symbol-placeholder">BTC/USDT</span>
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="klineTopic" disabled>
                                <label class="form-check-label" for="klineTopic">
                                    /topic/market/kline/<span class="symbol-placeholder">BTC/USDT</span>
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="currentKlineTopic" disabled>
                                <label class="form-check-label" for="currentKlineTopic">
                                    /topic/market/current-kline/<span class="symbol-placeholder">BTC/USDT</span>/<span class="period-placeholder">1min</span>
                                </label>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button id="subscribeAllBtn" class="btn btn-success" disabled>Subscribe All</button>
                            <button id="unsubscribeAllBtn" class="btn btn-warning" disabled>Unsubscribe All</button>
                            <button id="clearLogsBtn" class="btn btn-secondary">Clear Logs</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <h4>Trade Messages</h4>
                <div id="tradeMessages" class="message-container"></div>
            </div>
            <div class="col-md-4">
                <h4>Kline Messages</h4>
                <div id="klineMessages" class="message-container"></div>
            </div>
            <div class="col-md-4">
                <h4>Current Kline Messages</h4>
                <div id="currentKlineMessages" class="message-container"></div>
            </div>
        </div>
    </div>

    <!-- Load SockJS and STOMP libraries -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    
    <!-- Load our custom JavaScript -->
    <script src="js/websocket-client.js"></script>
</body>
</html>
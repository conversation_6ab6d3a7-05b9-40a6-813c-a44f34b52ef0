# K-line Processing Architecture Separation

## Current Issues

The current implementation has the following design issues:

1. **Mixed Responsibilities**: Both real-time and historical K-line processing use the same `DefaultKlineProcessor` component
2. **Unclear Separation**: `KLineGeneratorJob` and real-time trade processing both call similar methods on the same processor
3. **Data Flow Confusion**: It's unclear whether K-line data should be persisted immediately or kept in memory for real-time streaming

## Proposed Design Separation

### 1. Historical K-line Processing (KLineGeneratorJob)
**Purpose**: Generate and persist historical K-line data for chart history
**Responsibilities**:
- Run on scheduled intervals (every minute, hour, day, etc.)
- Query stored trade data from database
- Generate complete K-line records for completed periods
- Persist K-line data to database for historical charts
- Handle data aggregation for larger timeframes

**Components**:
- `KLineGeneratorJob` - Scheduled job runner
- `HistoricalKlineProcessor` - Processes stored trades into persisted K-lines
- Database queries for trade data
- Database persistence for K-line data

### 2. Real-time K-line Processing (Trade Message Processing)
**Purpose**: Provide live K-line updates for real-time streaming
**Responsibilities**:
- Process trade messages from Kafka topic `topic-kafka.exchange.trade`
- Update in-memory K-line data for current periods
- Push real-time K-line updates via WebSocket
- Handle incomplete/current K-line data for live charts
- No database persistence (handled by historical processor)

**Components**:
- `HandleTradeThread` - Processes Kafka trade messages
- `RealtimeKlineProcessor` - Updates in-memory K-line data
- WebSocket streaming for live updates
- In-memory K-line cache

### 3. Data Flow

```
Kafka Trade Messages → HandleTradeThread → RealtimeKlineProcessor → WebSocket Streaming
                                      ↓
                              Store trades in DB
                                      ↓
                              KLineGeneratorJob → HistoricalKlineProcessor → Persist K-lines to DB
```

## Implementation Plan

1. Create `RealtimeKlineProcessor` for in-memory K-line updates
2. Create `HistoricalKlineProcessor` for database-based K-line generation
3. Modify `KLineGeneratorJob` to use `HistoricalKlineProcessor`
4. Modify `HandleTradeThread` to use `RealtimeKlineProcessor`
5. Ensure proper separation of concerns

## Benefits

1. **Clear Separation**: Real-time and historical processing are distinct
2. **Performance**: Real-time processing doesn't need database operations
3. **Reliability**: Historical data generation is independent of real-time streaming
4. **Maintainability**: Each component has a single, clear responsibility